package com.izpan.infrastructure.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通用的实体类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.domain.BaseEntity
 * @CreateTime 2023/7/6 - 15:45
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BizEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -1182725599996696966L;

    /**
     * ID
     */
    @Schema(description = "ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;


    /**
     * 测验记录ID
     */
    @Schema(description = "测验记录ID")
    private Long clId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 学校编码
     */
    @Schema(description = "学校编码")
    private String schoolId;

    /**
     * 年级编码
     */
    @Schema(description = "年级编码")
    private String gradeId;

    /**
     * 班级编码
     */
    @Schema(description = "班级编码")
    private String clazzId;


    /**
     * 是否删除(0:否,1:是)
     */
    @TableLogic
    @JsonIgnore
    @TableField("is_deleted")
    @Schema(description = "是否删除(0:否,1:是)")
    private Integer deleted;

}
