package com.izpan.infrastructure.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通用的 VO 类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.common.domain.BaseVO
 * @CreateTime 2023/7/10 - 14:47
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 7743104535838008617L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "创建用户名称")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "年级ID")
    private String gradeId;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "班级ID")
    private String clazzId;

    @Schema(description = "班级名称")
    private String clazzName;
}
