package com.izpan.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

@Getter
@AllArgsConstructor
public enum CompositionSourceEnum implements Serializable {

    /**
     * 作文测评来源 1 批量测评
     */
    BATCH("1", "批量测评");

    private final String value;
    private final String name;

    public static CompositionSourceEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (CompositionSourceEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
