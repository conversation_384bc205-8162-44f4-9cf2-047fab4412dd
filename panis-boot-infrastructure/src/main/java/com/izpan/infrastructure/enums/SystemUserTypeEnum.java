package com.izpan.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

@Getter
@AllArgsConstructor
public enum SystemUserTypeEnum implements Serializable {

    SUPER_ADMIN("0", "超级管理员"),
    AGENT("1", "代理商"),
    SCHOOL_ADMIN("2", "学校管理员"),
    TEACHER("3", "老师"),
    STUDENT("4", "学生"),
    USER("5", "用户");

    private final String value;
    private final String name;

    public static SystemUserTypeEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (SystemUserTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
