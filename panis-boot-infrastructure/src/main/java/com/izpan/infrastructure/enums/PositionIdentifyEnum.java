package com.izpan.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;


@Getter
@AllArgsConstructor
public enum PositionIdentifyEnum implements Serializable {

    PRINCIPAL(0L, "校长"),
    VICE_PRINCIPAL(1L, "副校长"),
    GRADE_DIRECTOR(2L, "年级主任"),
    TEACHER(3L, "教师");

    private final Long value;

    private final String name;

    public static PositionIdentifyEnum getByValue(Long value) {
        if (value == null) {
            return null;
        }
        for (PositionIdentifyEnum identify : values()) {
            if (identify.getValue().equals(value)) {
                return identify;
            }
        }
        return value >= TEACHER.getValue() ? TEACHER : null;
    }
}
