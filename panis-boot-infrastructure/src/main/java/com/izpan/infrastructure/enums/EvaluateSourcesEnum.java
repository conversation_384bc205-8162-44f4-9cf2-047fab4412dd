package com.izpan.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

@Getter
@AllArgsConstructor
public enum EvaluateSourcesEnum implements Serializable {

    /**
     * 作文来源类型 0自主，1作业，2测验
     */
    SELF("0", "自主"),
    ASSIGNMENT("1", "作业"),
    TEST("2", "测验");

    private final String value;
    private final String name;

    public static EvaluateSourcesEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (EvaluateSourcesEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
