package com.izpan.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 菜单类型枚举类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.enums.MenuTypeEnum
 * @CreateTime 2024/4/17 - 14:01
 */

@Getter
@AllArgsConstructor
public enum IsEnum implements Serializable {

    NO("0", "否"),
    YES("1", "是");

    private final String value;

    private final String name;
}
