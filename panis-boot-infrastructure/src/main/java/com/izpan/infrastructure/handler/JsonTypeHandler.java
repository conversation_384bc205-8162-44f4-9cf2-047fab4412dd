package com.izpan.infrastructure.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.util.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * MyBatis JSON类型处理器
 * 用于将JSON字符串与Java对象之间进行转换
 */
@MappedTypes({Object.class})
public class JsonTypeHandler<T> extends BaseTypeHandler<T> {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private final TypeReference<T> typeReference;
    private final Class<?> clazz;

    public JsonTypeHandler(TypeReference<T> typeReference) {
        if (typeReference == null) {
            throw new IllegalArgumentException("TypeReference argument cannot be null");
        }
        this.typeReference = typeReference;
        this.clazz = null;
    }
    
    public JsonTypeHandler(Class<?> clazz) {
        this.typeReference = null;
        this.clazz = clazz;
    }

    public JsonTypeHandler() {
        this.typeReference = null;
        // 尝试获取泛型类型
        Type genericType = getClass().getGenericSuperclass();
        if (genericType instanceof ParameterizedType) {
            Type[] actualTypeArguments = ((ParameterizedType) genericType).getActualTypeArguments();
            if (actualTypeArguments != null && actualTypeArguments.length > 0) {
                if (actualTypeArguments[0] instanceof Class) {
                    this.clazz = (Class<?>) actualTypeArguments[0];
                    return;
                }
            }
        }
        this.clazz = Object.class; // 默认使用Object类
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, toJson(parameter));
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return toObject(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return toObject(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return toObject(cs.getString(columnIndex));
    }

    private String toJson(T obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert object to JSON: " + e.getMessage(), e);
        }
    }

    @SuppressWarnings("unchecked")
    private T toObject(String json) {
        if (!StringUtils.hasText(json)) {
            return null;
        }
        try {
            // 优先使用TypeReference
            if (typeReference != null) {
                return MAPPER.readValue(json, typeReference);
            }
            
            // 退回到使用Class
            if (clazz != null) {
                return (T) MAPPER.readValue(json, clazz);
            }
            
            // 如果都没有，尝试使用默认的TypeReference
            return (T) MAPPER.readValue(json, Object.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert JSON to object: " + e.getMessage(), e);
        }
    }
}
