/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.constants;

/**
 * 微信支付常量类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.constants.WechatPayConstants
 * @CreateTime 2025-07-20
 */
public class WechatPayConstants {

    /**
     * 支付渠道编码
     */
    public static final String PAYMENT_CHANNEL_WECHAT_LITE = "WECHAT_LITE";

    /**
     * 订单状态
     */
    public static final String ORDER_STATUS_PENDING = "PENDING_PAYMENT";
    public static final String ORDER_STATUS_PAID = "PAID";
    public static final String ORDER_STATUS_CLOSED = "CLOSED";
    public static final String ORDER_STATUS_FAILED = "FAILED";

    /**
     * 退款状态
     */
    public static final String REFUND_STATUS_NO_REFUND = "NO_REFUND";
    public static final String REFUND_STATUS_PENDING = "REFUND_PENDING";
    public static final String REFUND_STATUS_SUCCESS = "REFUND_SUCCESS";
    public static final String REFUND_STATUS_FAILED = "REFUND_FAILED";
    public static final String REFUND_STATUS_ABNORMAL = "REFUND_ABNORMAL"; // 退款异常

    /**
     * 签名类型
     */
    public static final String SIGN_TYPE_MD5 = "MD5";


}
