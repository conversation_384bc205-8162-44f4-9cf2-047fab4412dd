package com.izpan.infrastructure.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

/**
 * 项目启动监视器
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.event.StartEventListener
 * @CreateTime 2023/7/8 - 16:23
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
public class StartEventListener {

    @Order
    @EventListener(WebServerInitializedEvent.class)
    public void onApplicationEvent(WebServerInitializedEvent event) {
        Environment environment = event.getApplicationContext().getEnvironment();
        String appName = environment.getProperty("spring.application.name");
        int localPort = event.getWebServer().getPort();
        String profile = StringUtils.arrayToCommaDelimitedString(environment.getActiveProfiles());
        log.info("---[PanisBoot]-[{}]-启动完成，当前使用的端口:[{}]，环境变量:[{}]---", appName, localPort, profile);
    }
}
