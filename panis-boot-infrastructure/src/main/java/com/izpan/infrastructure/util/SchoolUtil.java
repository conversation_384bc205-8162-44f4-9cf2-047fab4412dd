package com.izpan.infrastructure.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.izpan.common.domain.LoginUser;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;

/**
 * 学校相关信息处理工具类
 * 用于处理学校ID、年级编码、班级编码等组织信息
 *
 * <AUTHOR>
 * @CreateTime 2024-06-10
 */
@Slf4j
public class SchoolUtil {

    /**
     * 设置学校ID到目标对象
     * 如果传入的学校ID不为空，则直接使用；否则获取当前登录用户的学校ID
     *
     * @param schoolId 传入的学校ID，可为null
     * @param target   目标对象，必须包含setSchoolId方法
     * @param <T>      目标对象类型
     * @return 设置了学校ID的目标对象
     */
    public static <T> T setSchoolId(String schoolId, T target) {
        try {
            // 检查schoolId是否为空
            if (StrUtil.isEmpty(schoolId)) {
                // 获取当前登录用户
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 获取当前登录用户的学校ID (处理可能是 Long 的情况)
                    schoolId = String.valueOf(loginUser.getSchoolId());
                    log.debug("使用当前登录用户的学校ID: {}", schoolId);
                } else {
                    log.warn("当前没有登录用户，无法获取学校ID");
                }
            } else {
                log.debug("使用传入的学校ID: {}", schoolId);
            }

            // 如果此时schoolId有效，则进行设置
            if (StrUtil.isNotEmpty(schoolId)) {
                // 通过反射获取setSchoolId方法
                invokeMethod(target, "setSchoolId", String.class, schoolId);
            }
        } catch (Exception e) {
            log.error("设置学校ID时发生错误", e);
        }

        return target;
    }

    /**
     * 重载方法：仅从当前登录用户获取学校ID并设置
     *
     * @param target 目标对象，必须包含setSchoolId方法
     * @param <T>    目标对象类型
     * @return 设置了学校ID的目标对象
     */
    public static <T> T setSchoolId(T target) {
        return setSchoolId((String)null, target);
    }

    /**
     * 设置年级编码到目标对象
     * 如果传入的年级编码不为空，则直接使用；否则获取当前登录用户的年级编码
     *
     * @param gradeId 传入的年级编码，可为null
     * @param target  目标对象，必须包含setGradeId方法
     * @param <T>     目标对象类型
     * @return 设置了年级编码的目标对象
     */
    public static <T> T setGradeId(String gradeId, T target) {
        try {
            // 检查gradeId是否为空
            if (StrUtil.isEmpty(gradeId)) {
                // 获取当前登录用户
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 获取当前登录用户的年级编码
                    gradeId = loginUser.getGradeId();
                    log.debug("使用当前登录用户的年级编码: {}", gradeId);
                } else {
                    log.warn("当前没有登录用户，无法获取年级编码");
                }
            } else {
                log.debug("使用传入的年级编码: {}", gradeId);
            }

            // 如果此时gradeId有效，则进行设置
            if (gradeId != null && !gradeId.trim().isEmpty()) {
                // 通过反射获取setGradeId方法
                invokeMethod(target, "setGradeId", String.class, gradeId);
            }
        } catch (Exception e) {
            log.error("设置年级编码时发生错误", e);
        }

        return target;
    }

    /**
     * 重载方法：仅从当前登录用户获取年级编码并设置
     *
     * @param target 目标对象，必须包含setGradeId方法
     * @param <T>    目标对象类型
     * @return 设置了年级编码的目标对象
     */
    public static <T> T setGradeId(T target) {
        return setGradeId(null, target);
    }

    /**
     * 设置班级编码到目标对象
     * 如果传入的班级编码不为空，则直接使用；否则获取当前登录用户的班级编码
     *
     * @param clazzId 传入的班级编码，可为null
     * @param target  目标对象，必须包含setClazzId方法
     * @param <T>     目标对象类型
     * @return 设置了班级编码的目标对象
     */
    public static <T> T setClazzId(String clazzId, T target) {
        try {
            // 检查clazzId是否为空
            if (StrUtil.isEmpty(clazzId)) {
                // 获取当前登录用户
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 获取当前登录用户的班级编码
                    clazzId = loginUser.getClazzId();
                    log.debug("使用当前登录用户的班级编码: {}", clazzId);
                } else {
                    log.warn("当前没有登录用户，无法获取班级编码");
                }
            } else {
                log.debug("使用传入的班级编码: {}", clazzId);
            }

            // 如果此时clazzId有效，则进行设置
            if (clazzId != null && !clazzId.trim().isEmpty()) {
                // 通过反射获取setClazzId方法
                invokeMethod(target, "setClazzId", String.class, clazzId);
            }
        } catch (Exception e) {
            log.error("设置班级编码时发生错误", e);
        }

        return target;
    }

    /**
     * 重载方法：仅从当前登录用户获取班级编码并设置
     *
     * @param target 目标对象，必须包含setClazzId方法
     * @param <T>    目标对象类型
     * @return 设置了班级编码的目标对象
     */
    public static <T> T setClazzId(T target) {
        return setClazzId(null, target);
    }

    /**
     * 组合方法：一次性设置学校ID、年级编码和班级编码
     * 对于每个参数，如果传入值不为空则使用传入值，否则使用当前登录用户的对应值
     *
     * @param schoolId 传入的学校ID，可为null
     * @param gradeId  传入的年级编码，可为null
     * @param clazzId  传入的班级编码，可为null
     * @param target   目标对象，必须包含相应的setter方法
     * @param <T>      目标对象类型
     * @return 设置了组织信息的目标对象
     */
    public static <T> T setSchoolAndGradeAndClazz(String schoolId, String gradeId, String clazzId, T target) {
        // 依次设置各项信息
        setSchoolId(schoolId, target);
        setGradeId(gradeId, target);
        setClazzId(clazzId, target);

        return target;
    }

    /**
     * 重载组合方法：仅从当前登录用户获取所有组织信息并设置
     *
     * @param target 目标对象，必须包含相应的setter方法
     * @param <T>    目标对象类型
     * @return 设置了组织信息的目标对象
     */
    public static <T> T setSchoolAndGradeAndClazz(T target) {
        return setSchoolAndGradeAndClazz((String)null, null, null, target);
    }

    /**
     * 获取学校ID
     * 获取当前登录用户的学校ID，如果当前没有登录用户则返回null
     *
     * @return 学校ID，可能为null
     */
    public static String getSchoolId() {
        return getSchoolId(null);
    }

    /**
     * 获取学校ID，如果当前没有登录用户则返回指定的默认值
     *
     * @param defaultValue 默认值
     * @return 学校ID，如果当前没有登录用户则返回defaultValue
     */
    public static String getSchoolId(String defaultValue) {
        try {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser != null && loginUser.getSchoolId() != null) {
                return String.valueOf(loginUser.getSchoolId());
            }
        } catch (Exception e) {
            log.error("获取学校ID时发生错误", e);
        }
        return defaultValue;
    }

    /**
     * 获取年级ID
     * 获取当前登录用户的年级ID，如果当前没有登录用户则返回null
     *
     * @return 年级ID，可能为null
     */
    public static String getGradeId() {
        return getGradeId(null);
    }

    /**
     * 获取年级ID，如果当前没有登录用户则返回指定的默认值
     *
     * @param defaultValue 默认值
     * @return 年级ID，如果当前没有登录用户则返回defaultValue
     */
    public static String getGradeId(String defaultValue) {
        try {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser != null && StrUtil.isNotEmpty(loginUser.getGradeId())) {
                return loginUser.getGradeId();
            }
        } catch (Exception e) {
            log.error("获取年级ID时发生错误", e);
        }
        return defaultValue;
    }

    /**
     * 获取班级ID
     * 获取当前登录用户的班级ID，如果当前没有登录用户则返回null
     *
     * @return 班级ID，可能为null
     */
    public static String getClazzId() {
        return getClazzId(null);
    }

    /**
     * 获取班级ID，如果当前没有登录用户则返回指定的默认值
     *
     * @param defaultValue 默认值
     * @return 班级ID，如果当前没有登录用户则返回defaultValue
     */
    public static String getClazzId(String defaultValue) {
        try {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser != null && StrUtil.isNotEmpty(loginUser.getClazzId())) {
                return loginUser.getClazzId();
            }
        } catch (Exception e) {
            log.error("获取班级ID时发生错误", e);
        }
        return defaultValue;
    }

    /**
     * 设置学校名称到目标对象
     * 如果传入的学校名称不为空，则直接使用；否则获取当前登录用户的学校名称
     *
     * @param schoolName 传入的学校名称，可为null
     * @param target     目标对象，必须包含setSchoolName方法
     * @param <T>        目标对象类型
     * @return 设置了学校名称的目标对象
     */
    public static <T> T setSchoolName(String schoolName, T target) {
        try {
            // 检查schoolName是否为空
            if (StrUtil.isEmpty(schoolName)) {
                // 获取当前登录用户
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 获取当前登录用户的学校名称
                    schoolName = loginUser.getSchoolName();
                    log.debug("使用当前登录用户的学校名称: {}", schoolName);
                } else {
                    log.warn("当前没有登录用户，无法获取学校名称");
                }
            } else {
                log.debug("使用传入的学校名称: {}", schoolName);
            }

            // 如果此时schoolName有效，则进行设置
            if (StrUtil.isNotEmpty(schoolName)) {
                // 通过反射获取setSchoolName方法
                invokeMethod(target, "setSchoolName", String.class, schoolName);
            }
        } catch (Exception e) {
            log.error("设置学校名称时发生错误", e);
        }

        return target;
    }

    /**
     * 重载方法：仅从当前登录用户获取学校名称并设置
     *
     * @param target 目标对象，必须包含setSchoolName方法
     * @param <T>    目标对象类型
     * @return 设置了学校名称的目标对象
     */
    public static <T> T setSchoolName(T target) {
        return setSchoolName(null, target);
    }

    /**
     * 设置年级名称到目标对象
     * 如果传入的年级名称不为空，则直接使用；否则获取当前登录用户的年级名称
     *
     * @param gradeName 传入的年级名称，可为null
     * @param target    目标对象，必须包含setGradeName方法
     * @param <T>       目标对象类型
     * @return 设置了年级名称的目标对象
     */
    public static <T> T setGradeName(String gradeName, T target) {
        try {
            // 检查gradeName是否为空
            if (StrUtil.isEmpty(gradeName)) {
                // 获取当前登录用户
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 获取当前登录用户的年级名称
                    gradeName = loginUser.getGradeName();
                    log.debug("使用当前登录用户的年级名称: {}", gradeName);
                } else {
                    log.warn("当前没有登录用户，无法获取年级名称");
                }
            } else {
                log.debug("使用传入的年级名称: {}", gradeName);
            }

            // 如果此时gradeName有效，则进行设置
            if (StrUtil.isNotEmpty(gradeName)) {
                // 通过反射获取setGradeName方法
                invokeMethod(target, "setGradeName", String.class, gradeName);
            }
        } catch (Exception e) {
            log.error("设置年级名称时发生错误", e);
        }

        return target;
    }

    /**
     * 重载方法：仅从当前登录用户获取年级名称并设置
     *
     * @param target 目标对象，必须包含setGradeName方法
     * @param <T>    目标对象类型
     * @return 设置了年级名称的目标对象
     */
    public static <T> T setGradeName(T target) {
        return setGradeName(null, target);
    }

    /**
     * 设置班级名称到目标对象
     * 如果传入的班级名称不为空，则直接使用；否则获取当前登录用户的班级名称
     *
     * @param clazzName 传入的班级名称，可为null
     * @param target    目标对象，必须包含setClazzName方法
     * @param <T>       目标对象类型
     * @return 设置了班级名称的目标对象
     */
    public static <T> T setClazzName(String clazzName, T target) {
        try {
            // 检查clazzName是否为空
            if (StrUtil.isEmpty(clazzName)) {
                // 获取当前登录用户
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 获取当前登录用户的班级名称
                    clazzName = loginUser.getClazzName();
                    log.debug("使用当前登录用户的班级名称: {}", clazzName);
                } else {
                    log.warn("当前没有登录用户，无法获取班级名称");
                }
            } else {
                log.debug("使用传入的班级名称: {}", clazzName);
            }

            // 如果此时clazzName有效，则进行设置
            if (StrUtil.isNotEmpty(clazzName)) {
                // 通过反射获取setClazzName方法
                invokeMethod(target, "setClazzName", String.class, clazzName);
            }
        } catch (Exception e) {
            log.error("设置班级名称时发生错误", e);
        }

        return target;
    }

    /**
     * 重载方法：仅从当前登录用户获取班级名称并设置
     *
     * @param target 目标对象，必须包含setClazzName方法
     * @param <T>    目标对象类型
     * @return 设置了班级名称的目标对象
     */
    public static <T> T setClazzName(T target) {
        return setClazzName(null, target);
    }

    /**
     * 获取学校名称
     * 获取当前登录用户的学校名称，如果当前没有登录用户则返回null
     *
     * @return 学校名称，可能为null
     */
    public static String getSchoolName() {
        return getSchoolName(null);
    }

    /**
     * 获取学校名称，如果当前没有登录用户则返回指定的默认值
     *
     * @param defaultValue 默认值
     * @return 学校名称，如果当前没有登录用户则返回defaultValue
     */
    public static String getSchoolName(String defaultValue) {
        try {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser != null && StrUtil.isNotEmpty(loginUser.getSchoolName())) {
                return loginUser.getSchoolName();
            }
        } catch (Exception e) {
            log.error("获取学校名称时发生错误", e);
        }
        return defaultValue;
    }

    /**
     * 获取年级名称
     * 获取当前登录用户的年级名称，如果当前没有登录用户则返回null
     *
     * @return 年级名称，可能为null
     */
    public static String getGradeName() {
        return getGradeName(null);
    }

    /**
     * 获取年级名称，如果当前没有登录用户则返回指定的默认值
     *
     * @param defaultValue 默认值
     * @return 年级名称，如果当前没有登录用户则返回defaultValue
     */
    public static String getGradeName(String defaultValue) {
        try {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser != null && StrUtil.isNotEmpty(loginUser.getGradeName())) {
                return loginUser.getGradeName();
            }
        } catch (Exception e) {
            log.error("获取年级名称时发生错误", e);
        }
        return defaultValue;
    }

    /**
     * 获取班级名称
     * 获取当前登录用户的班级名称，如果当前没有登录用户则返回null
     *
     * @return 班级名称，可能为null
     */
    public static String getClazzName() {
        return getClazzName(null);
    }

    /**
     * 获取班级名称，如果当前没有登录用户则返回指定的默认值
     *
     * @param defaultValue 默认值
     * @return 班级名称，如果当前没有登录用户则返回defaultValue
     */
    public static String getClazzName(String defaultValue) {
        try {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser != null && StrUtil.isNotEmpty(loginUser.getClazzName())) {
                return loginUser.getClazzName();
            }
        } catch (Exception e) {
            log.error("获取班级名称时发生错误", e);
        }
        return defaultValue;
    }

    /**
     * 通用方法：同时设置实体的ID和名称
     * 
     * @param target     目标对象
     * @param fieldName  字段名称前缀，如"school"、"grade"、"clazz"
     * @param id         ID值
     * @param name       名称值
     * @param <T>        目标对象类型
     * @return 设置了属性的目标对象
     */
    private static <T> T setEntityInfo(T target, String fieldName, String id, String name) {
        try {
            // 首字母大写，用于构造setter方法名
            String capitalizedField = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            
            // 设置ID
            if (StrUtil.isNotEmpty(id)) {
                invokeMethod(target, "set" + capitalizedField + "Id", String.class, id);
            } else {
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 通过反射获取用户对应字段的值
                    try {
                        Method getIdMethod = LoginUser.class.getMethod("get" + capitalizedField + "Id");
                        Object value = getIdMethod.invoke(loginUser);
                        if (value != null) {
                            String idValue = String.valueOf(value);
                            if (StrUtil.isNotEmpty(idValue)) {
                                log.debug("使用当前登录用户的{}ID: {}", fieldName, idValue);
                                invokeMethod(target, "set" + capitalizedField + "Id", String.class, idValue);
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取{}ID时发生错误", fieldName, e);
                    }
                }
            }
            
            // 设置名称
            if (StrUtil.isNotEmpty(name)) {
                invokeMethod(target, "set" + capitalizedField + "Name", String.class, name);
            } else {
                LoginUser loginUser = GlobalUserHolder.getUser();
                if (loginUser != null) {
                    // 通过反射获取用户对应字段的值
                    try {
                        Method getNameMethod = LoginUser.class.getMethod("get" + capitalizedField + "Name");
                        String value = (String) getNameMethod.invoke(loginUser);
                        if (StrUtil.isNotEmpty(value)) {
                            log.debug("使用当前登录用户的{}名称: {}", fieldName, value);
                            invokeMethod(target, "set" + capitalizedField + "Name", String.class, value);
                        }
                    } catch (Exception e) {
                        log.error("获取{}名称时发生错误", fieldName, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置{}信息时发生错误", fieldName, e);
        }
        
        return target;
    }

    /**
     * 同时设置学校ID和名称
     * 
     * @param schoolId    学校ID
     * @param schoolName  学校名称
     * @param target      目标对象
     * @param <T>         目标对象类型
     * @return 设置了学校信息的目标对象
     */
    public static <T> T setSchoolInfo(String schoolId, String schoolName, T target) {
        return setEntityInfo(target, "school", schoolId, schoolName);
    }
    
    /**
     * 从当前用户获取学校信息并设置
     * 
     * @param target 目标对象
     * @param <T>    目标对象类型
     * @return 设置了学校信息的目标对象
     */
    public static <T> T setSchoolInfo(T target) {
        return setSchoolInfo(null, null, target);
    }
    
    /**
     * 同时设置年级ID和名称
     * 
     * @param gradeId    年级ID
     * @param gradeName  年级名称
     * @param target     目标对象
     * @param <T>        目标对象类型
     * @return 设置了年级信息的目标对象
     */
    public static <T> T setGradeInfo(String gradeId, String gradeName, T target) {
        return setEntityInfo(target, "grade", gradeId, gradeName);
    }
    
    /**
     * 从当前用户获取年级信息并设置
     * 
     * @param target 目标对象
     * @param <T>    目标对象类型
     * @return 设置了年级信息的目标对象
     */
    public static <T> T setGradeInfo(T target) {
        return setGradeInfo(null, null, target);
    }
    
    /**
     * 同时设置班级ID和名称
     * 
     * @param clazzId    班级ID
     * @param clazzName  班级名称
     * @param target     目标对象
     * @param <T>        目标对象类型
     * @return 设置了班级信息的目标对象
     */
    public static <T> T setClazzInfo(String clazzId, String clazzName, T target) {
        return setEntityInfo(target, "clazz", clazzId, clazzName);
    }
    
    /**
     * 从当前用户获取班级信息并设置
     * 
     * @param target 目标对象
     * @param <T>    目标对象类型
     * @return 设置了班级信息的目标对象
     */
    public static <T> T setClazzInfo(T target) {
        return setClazzInfo(null, null, target);
    }

    /**
     * 扩展组合方法：一次性设置学校、年级和班级的ID和名称
     * 对于每个参数，如果传入值不为空则使用传入值，否则使用当前登录用户的对应值
     *
     * @param schoolId    传入的学校ID，可为null
     * @param schoolName  传入的学校名称，可为null
     * @param gradeId     传入的年级ID，可为null
     * @param gradeName   传入的年级名称，可为null
     * @param clazzId     传入的班级ID，可为null
     * @param clazzName   传入的班级名称，可为null
     * @param target      目标对象，必须包含相应的setter方法
     * @param <T>         目标对象类型
     * @return 设置了组织信息的目标对象
     */
    public static <T> T setSchoolAndGradeAndClazzWithNames(
            String schoolId, String schoolName, 
            String gradeId, String gradeName, 
            String clazzId, String clazzName, 
            T target) {
        // 使用新方法简化实现
        setSchoolInfo(schoolId, schoolName, target);
        setGradeInfo(gradeId, gradeName, target);
        setClazzInfo(clazzId, clazzName, target);

        return target;
    }

    /**
     * 重载组合方法：仅从当前登录用户获取所有组织信息(包括名称)并设置
     *
     * @param target 目标对象，必须包含相应的setter方法
     * @param <T>    目标对象类型
     * @return 设置了组织信息的目标对象
     */
    public static <T> T setSchoolAndGradeAndClazzWithNames(T target) {
        return setSchoolAndGradeAndClazzWithNames(
                (String)null, (String)null, 
                (String)null, (String)null, 
                (String)null, (String)null, 
                target);
    }
    
    /**
     * 简化版组合方法：一次性设置学校、年级和班级的ID和名称
     * 自动从登录用户获取所有缺失值
     *
     * @param target 目标对象，必须包含相应的setter方法
     * @param <T>    目标对象类型
     * @return 设置了组织信息的目标对象
     */
    public static <T> T setAllInfo(T target) {
        return setSchoolAndGradeAndClazzWithNames(target);
    }
    
    /**
     * 简化版组合方法：设置指定的学校、年级和班级ID，自动获取对应的名称
     *
     * @param schoolId 学校ID
     * @param gradeId  年级ID
     * @param clazzId  班级ID
     * @param target   目标对象
     * @param <T>      目标对象类型
     * @return 设置了组织信息的目标对象
     */
    public static <T> T setAllInfo(String schoolId, String gradeId, String clazzId, T target) {
        // 只提供ID，名称自动从用户中获取
        setSchoolInfo(schoolId, null, target);
        setGradeInfo(gradeId, null, target);
        setClazzInfo(clazzId, null, target);
        return target;
    }

    /**
     * 通过反射调用目标对象的方法
     *
     * @param target     目标对象
     * @param methodName 方法名
     * @param paramType  参数类型
     * @param paramValue 参数值
     * @param <T>        目标对象类型
     */
    private static <T> void invokeMethod(T target, String methodName, Class<?> paramType, Object paramValue) {
        try {
            Method method = target.getClass().getMethod(methodName, paramType);
            method.invoke(target, paramValue);
        } catch (NoSuchMethodException e) {
            log.warn("目标对象 {} 没有{}({})方法", target.getClass().getName(), methodName, paramType.getSimpleName());
        } catch (Exception e) {
            log.error("调用{}方法时发生错误", methodName, e);
        }
    }

    /**
     * 设置学校名称并自动获取设置学校ID
     * 从当前登录用户获取学校ID，并使用传入的学校名称
     *
     * @param schoolName 学校名称
     * @param target     目标对象，必须包含相应的setter方法
     * @param <T>        目标对象类型
     * @return 设置了学校信息的目标对象
     */
    public static <T> T setSchoolNameAndId(String schoolName, T target) {
        // 从当前用户获取ID，使用传入的名称
        return setSchoolInfo(null, schoolName, target);
    }
    
    /**
     * 设置年级名称并自动获取设置年级ID
     * 从当前登录用户获取年级ID，并使用传入的年级名称
     *
     * @param gradeName 年级名称
     * @param target    目标对象，必须包含相应的setter方法
     * @param <T>       目标对象类型
     * @return 设置了年级信息的目标对象
     */
    public static <T> T setGradeNameAndId(String gradeName, T target) {
        // 从当前用户获取ID，使用传入的名称
        return setGradeInfo(null, gradeName, target);
    }
    
    /**
     * 设置班级名称并自动获取设置班级ID
     * 从当前登录用户获取班级ID，并使用传入的班级名称
     *
     * @param clazzName 班级名称
     * @param target    目标对象，必须包含相应的setter方法
     * @param <T>       目标对象类型
     * @return 设置了班级信息的目标对象
     */
    public static <T> T setClazzNameAndId(String clazzName, T target) {
        // 从当前用户获取ID，使用传入的名称
        return setClazzInfo(null, clazzName, target);
    }
}