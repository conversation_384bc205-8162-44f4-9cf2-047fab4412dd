# ShardingSphere 配置文件
# 官方文档：https://shardingsphere.apache.org/document/current/cn/overview/

# 配置模式
mode:
  # 独立模式
  type: Standalone
  # 使用JDBC作为存储库类型
  repository:
    type: JDBC

# 数据源配置
dataSources:
  db_0:
    dataSourceClassName: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************************
    username: root
    password: root
    # 初始化时建立物理连接的个数
    initial-size: 5
    # 连接池的最小空闲数量
    min-idle: 5
    # 连接池最大连接数量
    max-active: 20
    # 获取连接时最大等待时间，单位毫秒
    max-wait: 60000
    # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
    test-while-idle: true
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    time-between-eviction-runs-millis: 60000
    # 销毁线程时检测当前连接的最后活动时间和当前时间差大于该值时，关闭当前连接(配置连接在池中的最小生存时间)
    min-evictable-idle-time-millis: 30000
    # 用来检测数据库连接是否有效的sql 必须是一个查询语句(oracle中为 select 1 from dual)
    validation-query: select 1
    # 申请连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
    test-on-borrow: false
    # 归还连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
    test-on-return: false
    # 是否缓存preparedStatement, 也就是PSCache,PSCache对支持游标的数据库性能提升巨大，比如说oracle,在mysql下建议关闭。
    pool-prepared-statements: false
    # 置监控统计拦截的filters，去掉后监控界面sql无法统计，stat: 监控统计、Slf4j:日志记录、waLL: 防御sqL注入
    filters: stat,wall,slf4j
    # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
    max-pool-prepared-statement-per-connection-size: -1
    # 合并多个DruidDataSource的监控数据
    use-global-data-source-stat: true
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connect-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

# 规则配置
rules:
  - !SHARDING
    # 数据分片规则配置
    tables:
      sys_notice:
        # 实际数据节点 库名db_0.sys_notice_${1..2} 代表有 sys_notice_1 和 sys_notice_2 两个表
        # 以下示例是固定两个表
        actualDataNodes: db_0.sys_notice, db_0.sys_notice_1
        tableStrategy:
          standard:
            # 分片列
            shardingColumn: id
            # 分片算法名称
            shardingAlgorithmName: notice-inline
        keyGenerateStrategy:
          # 主键生成列
          column: id
          # 主键生成器名称
          keyGeneratorName: snowflake
    keyGenerators:
      snowflake:
        # 主键生成器类型
        type: SNOWFLAKE
    shardingAlgorithms:
      notice-inline:
        # 分片算法类型
        type: INLINE
        props:
          # 分片算法表达式，表示根据id的值对2取模，决定数据存储在sys_notice或sys_notice_1表中
          algorithm-expression: "sys_notice$->{id % 2 == 0 ? '' : '_1'}"
  - !SINGLE
    # 单表配置
    tables:
      # 加载指定数据源中的全部单表
      - "db_0.*"
    # 默认数据源，仅在执行 CREATE TABLE 创建单表时有效。缺失值为空，表示随机单播路由。
    defaultDataSource: ds_0

# 其他属性配置
props:
  # 是否显示SQL语句
  sql-show: true
  # 每个查询的最大连接数
  max-connections-size-per-query: 5