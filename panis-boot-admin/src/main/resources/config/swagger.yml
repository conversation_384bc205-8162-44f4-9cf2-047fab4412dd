# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: '基础管理'
      paths-to-match: '/**'
      packages-to-scan: com.izpan.admin.controller.base
    - group: '作文模块'
      paths-to-match: '/**'
      packages-to-scan: com.izpan.admin.controller.biz
    - group: '系统管理'
      paths-to-match: '/**'
      packages-to-scan: com.izpan.admin.controller.system
    - group: '监控管理'
      paths-to-match: '/**'
      packages-to-scan: com.izpan.admin.controller.monitor
    - group: '工具管理'
      paths-to-match: '/**'
      packages-to-scan: com.izpan.admin.controller.tools

# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: 鸣谢 [knife4j](https://gitee.com/xiaoym/knife4j)