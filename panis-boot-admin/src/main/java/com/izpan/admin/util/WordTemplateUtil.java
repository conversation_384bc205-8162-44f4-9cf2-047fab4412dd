/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.util;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.Style;
import com.deepoove.poi.template.ElementTemplate;
import com.izpan.modules.biz.domain.entity.BizCompositionContent;
import com.izpan.modules.biz.domain.entity.BizCompositionErrorLog;
import com.izpan.modules.biz.domain.entity.BizCompositionGoodWords;
import com.izpan.modules.biz.domain.entity.BizCompositionMeipi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.nio.file.AccessDeniedException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Word模板工具类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName WordTemplateUtil
 * @CreateTime 2024-04-25
 */
public class WordTemplateUtil {

    private static final Logger log = LoggerFactory.getLogger(WordTemplateUtil.class);

    /**
     * 检查Word模板中的变量
     *
     * @param templatePath 模板路径（classpath下）
     * @return 模板中的变量集合
     * @throws IOException 如果发生IO异常
     */
    public static Set<String> checkTemplateVariables(String templatePath) throws IOException {
        log.debug("开始检查模板变量: {}", templatePath);

        ClassPathResource resource = new ClassPathResource(templatePath);

        // 检查文件是否存在
        if (!resource.exists()) {
            throw new FileNotFoundException("模板文件不存在: " + templatePath);
        }

        try (InputStream templateStream = resource.getInputStream();
             XWPFTemplate template = XWPFTemplate.compile(templateStream)) {

            Set<String> variables = template.getElementTemplates().stream()
                    .filter(t -> t instanceof ElementTemplate)
                    .map(t -> (ElementTemplate) t)
                    .map(ElementTemplate::getTagName)
                    .filter(Objects::nonNull)
                    .filter(var -> !var.isEmpty())
                    .collect(Collectors.toSet());

            log.debug("模板变量检查完成, 共找到 {} 个唯一变量", variables.size());
            return variables;

        } catch (AccessDeniedException e) {
            log.error("权限被拒绝: 无法访问模板文件 {}", templatePath, e);
            throw new IOException("权限被拒绝: 无法访问模板文件", e);

        } catch (IOException e) {
            log.error("检查模板变量时发生 IO 异常: {}", e.getMessage(), e);
            throw e;

        } catch (Exception e) {
            log.error("解析模板时发生意外错误: {}", e.getMessage(), e);
            throw new IOException("模板解析失败", e);
        }
    }


    /**
     * 创建默认的POI-TL配置
     *
     * @return 配置对象
     */
    public static Configure createDefaultConfigure() {
        ConfigureBuilder builder = Configure.builder()
                .useSpringEL();  // 启用Spring表达式语言
        return builder.build();
    }
    
    /**
     * 创建自定义的POI-TL配置
     *
     * @param useSpringEL 是否使用Spring表达式语言
     * @return 配置对象
     */
    public static Configure createConfigure(boolean useSpringEL) {
        ConfigureBuilder builder = Configure.builder();
        if (useSpringEL) {
            builder.useSpringEL();
        }
        return builder.build();
    }
    
    /**
     * 创建富文本内容
     *
     * @param text 文本内容
     * @param color 颜色 (例如: "FF0000" 代表红色)
     * @param fontSize 字体大小 (单位: 磅)
     * @param isBold 是否加粗
     * @return 富文本渲染数据
     */
    public static TextRenderData createRichText(String text, String color, int fontSize, boolean isBold) {
        Style style = new Style();
        style.setColor(color);
        style.setFontSize(fontSize);
        style.setBold(isBold);
        
        return new TextRenderData(text, style);
    }
    
    /**
     * 创建段落内容
     *
     * @param text 文本内容
     * @return 段落渲染数据
     */
    public static TextRenderData createParagraph(String text) {
        return Texts.of(text).create();
    }
    
    /**
     * 创建多段落内容
     *
     * @param text 包含多段落的文本内容（段落之间用换行符分隔）
     * @return 多段落渲染数据列表
     */
    public static List<TextRenderData> createParagraphs(String text) {
        if (text == null || text.isEmpty()) {
            return Collections.singletonList(Texts.of("").create());
        }
        
        String[] paragraphs = text.split("\n");
        List<TextRenderData> result = new ArrayList<>(paragraphs.length);
        
        for (String paragraph : paragraphs) {
            result.add(Texts.of(paragraph).create());
        }
        
        return result;
    }
    
    /**
     * 格式化作文评语
     * 将包含特殊格式的评语转换为带换行的文本
     * 
     * @param comments 包含特殊格式的评语字符串
     * @return 格式化后的评语TextRenderData
     */
    public static TextRenderData formatComments(String comments) {
        if (comments == null || comments.isEmpty()) {
            return Texts.of("").create();
        }
        
        // 替换HTML空格占位符为实际空格
        String processedText = comments.replace("&nbsp;", " ");
        
        // 将'|'符号替换为换行符
        processedText = processedText.replace("|", "\n");
        
        // 返回单个TextRenderData对象
        return Texts.of(processedText.trim()).create();
    }
    
    /**
     * 创建图片内容
     *
     * @param imagePath 图片路径
     * @param width 宽度 (单位: 像素)
     * @param height 高度 (单位: 像素)
     * @return 图片渲染数据
     * @throws IOException 如果读取图片失败
     */
    public static PictureRenderData createPicture(String imagePath, int width, int height) throws IOException {
        try {
            return Pictures.ofLocal(imagePath).size(width, height).create();
        } catch (Exception e) {
            log.error("创建图片时发生错误: {}", e.getMessage(), e);
            throw new IOException("创建图片时发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 渲染Word模板并将结果写入到输出流
     *
     * @param templatePath 模板路径（classpath下）
     * @param dataModel 数据模型
     * @param outputStream 输出流
     * @throws IOException 如果发生IO异常
     */
    public static void renderTemplate(String templatePath, Map<String, Object> dataModel, OutputStream outputStream) throws IOException {
        log.debug("开始渲染模板: {}", templatePath);
        try (InputStream templateStream = new ClassPathResource(templatePath).getInputStream()) {
            // 创建配置
            Configure config = createDefaultConfigure();
            
            // 编译模板并渲染
            XWPFTemplate template = XWPFTemplate.compile(templateStream, config).render(dataModel);
            
            // 输出文档
            template.write(outputStream);
            template.close();
            log.debug("模板渲染完成: {}", templatePath);
        } catch (IOException e) {
            log.error("渲染模板时发生IO异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("渲染模板时发生异常: {}", e.getMessage(), e);
            throw new IOException("渲染模板时发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 渲染Word模板并保存为文件
     *
     * @param templatePath 模板路径（classpath下）
     * @param dataModel 数据模型
     * @param outputFilePath 输出文件路径
     * @throws IOException 如果发生IO异常
     */
    public static void renderTemplateToFile(String templatePath, Map<String, Object> dataModel, String outputFilePath) throws IOException {
        log.debug("开始渲染模板并保存为文件: {}, 输出到: {}", templatePath, outputFilePath);
        try (FileOutputStream fos = new FileOutputStream(new File(outputFilePath))) {
            renderTemplate(templatePath, dataModel, fos);
            log.debug("模板渲染并保存为文件完成: {}", outputFilePath);
        } catch (IOException e) {
            log.error("渲染模板并保存为文件时发生IO异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    
    /**
     * 检查并创建唯一的模板标签列表，去除重复项
     *
     * @param templatePath 模板路径（classpath下）
     * @return 去重后的变量名列表
     * @throws IOException 如果发生IO异常
     */
    public static List<String> checkUniqueTemplateVariables(String templatePath) throws IOException {
        Set<String> allVariables = checkTemplateVariables(templatePath);
        Set<String> uniqueVariablesSet = new HashSet<>(allVariables);

        log.debug("模板中有{}个变量，去重后有{}个变量", allVariables.size(), uniqueVariablesSet.size());
        return new ArrayList<>(uniqueVariablesSet);
    }
    
    /**
     * 验证数据模型是否包含模板中所需的所有变量
     *
     * @param templatePath 模板路径（classpath下）
     * @param dataModel 数据模型
     * @return 缺失的变量列表，如果没有缺失则返回空列表
     * @throws IOException 如果发生IO异常
     */
    public static List<String> validateDataModel(String templatePath, Map<String, Object> dataModel) throws IOException {
        List<String> templateVariables = checkUniqueTemplateVariables(templatePath);
        List<String> missingVariables = new ArrayList<>();
        
        for (String variable : templateVariables) {
            if (!dataModel.containsKey(variable)) {
                missingVariables.add(variable);
            }
        }
        
        if (!missingVariables.isEmpty()) {
            log.warn("数据模型缺少以下变量: {}", missingVariables);
        }
        
        return missingVariables;
    }
    
    /**
     * 自动填充缺失的变量
     *
     * @param templatePath 模板路径（classpath下）
     * @param dataModel 数据模型
     * @throws IOException 如果发生IO异常
     */
    public static void autoFillMissingVariables(String templatePath, Map<String, Object> dataModel) throws IOException {
        List<String> missingVariables = validateDataModel(templatePath, dataModel);
        
        for (String variable : missingVariables) {
            log.info("自动填充缺失变量: {}", variable);
            dataModel.put(variable, "");
        }
    }
    
    /**
     * 创建一个完整的数据模型预览，包括所有模板变量
     *
     * @param templatePath 模板路径（classpath下）
     * @return 包含所有模板变量的空数据模型
     * @throws IOException 如果发生IO异常
     */
    public static Map<String, Object> createEmptyDataModel(String templatePath) throws IOException {
        List<String> templateVariables = checkUniqueTemplateVariables(templatePath);
        Map<String, Object> emptyDataModel = new HashMap<>();
        
        for (String variable : templateVariables) {
            emptyDataModel.put(variable, "");
        }
        
        return emptyDataModel;
    }

    /**
     * 格式化富文本作文内容
     * 处理作文段落内容、错误文本、好文本和段批等复杂结构
     *
     * @param contentList 作文段落内容列表
     * @param meiList 眉批/段批列表
     * @param errorList 错误列表
     * @param goodWordsList 好词好句列表
     * @return 格式化后的富文本渲染数据
     */
    public static TextRenderData formatRichCompositionContent(
            List<BizCompositionContent> contentList,
            List<BizCompositionMeipi> meiList,
            List<BizCompositionErrorLog> errorList,
            List<BizCompositionGoodWords> goodWordsList) {
        
        if (contentList == null || contentList.isEmpty()) {
            return Texts.of("").create();
        }
        
        StringBuilder contentBuilder = new StringBuilder();
        
        // 处理每个段落
        for (int i = 0; i < contentList.size(); i++) {
            BizCompositionContent paragraph = contentList.get(i);
            if (paragraph == null || paragraph.getCcontent() == null) {
                continue;
            }
            
            String paragraphText = paragraph.getCcontent().trim();
            String paragraphNum = String.valueOf(i + 1);
            
            // 添加段落内容，带有首行缩进
            contentBuilder.append("　　"+"("+paragraphNum+")").append(paragraphText).append("\n");
            
            // 查找并添加该段落对应的段批
            BizCompositionMeipi paragraphComment = findParagraphComment(meiList, i + 1);
            if (paragraphComment != null && paragraphComment.getSentence() != null) {
                // 创建段批文本，使用不同的格式以区分正文
                String commentText = "【" + paragraphComment.getSentence() + "】";
                contentBuilder.append(commentText).append("\n");
            }
        }
        
        return Texts.of(contentBuilder.toString()).create();
    }
    
    /**
     * 查找特定段落的段批
     *
     * @param meiList 眉批/段批列表
     * @param paragraphNumber 段落编号
     * @return 对应的段批对象，如果没有则返回null
     */
    private static BizCompositionMeipi findParagraphComment(List<BizCompositionMeipi> meiList, int paragraphNumber) {
        if (meiList == null || meiList.isEmpty()) {
            return null;
        }
        
        return meiList.stream()
                .filter(mei -> mei.getParagraph() != null 
                        && mei.getParagraph() == paragraphNumber 
                        && (mei.getOriginal() == null || mei.getOriginal().isEmpty()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 查找段落中的错误文本
     * 
     * 注意：由于POI-TL的限制，当前版本无法在段落内部分文本应用不同样式，
     * 因此此方法仅用于识别错误，但不会在文档中高亮显示
     *
     * @param errorList 错误列表
     * @param paragraphNum 段落编号
     * @param paragraphText 段落文本
     * @return 错误文本渲染数据列表
     */
    private static List<TextRenderData> findErrorTexts(List<BizCompositionErrorLog> errorList, String paragraphNum, String paragraphText) {
        List<TextRenderData> result = new ArrayList<>();
        if (errorList == null || errorList.isEmpty()) {
            return result;
        }
        
        // 筛选当前段落的错误
        List<BizCompositionErrorLog> paragraphErrors = errorList.stream()
                .filter(error -> paragraphNum.equals(error.getEparagraph()))
                .collect(Collectors.toList());
        
        // 记录错误但不应用样式 - 当前POI-TL版本不支持段落内文本部分样式
        for (BizCompositionErrorLog error : paragraphErrors) {
            if (error.getEsentence() != null && !error.getEsentence().isEmpty()) {
                log.debug("段落{}中发现错误文本: {}", paragraphNum, error.getEsentence());
                TextRenderData errorText = Texts.of(error.getEsentence())
                        .color("FF0000") // 红色
                        .create();
                result.add(errorText);
            }
        }
        
        return result;
    }
    
    /**
     * 查找段落中的好词好句
     * 
     * 注意：由于POI-TL的限制，当前版本无法在段落内部分文本应用不同样式，
     * 因此此方法仅用于识别好词好句，但不会在文档中高亮显示
     *
     * @param goodWordsList 好词好句列表
     * @param paragraphNum 段落编号
     * @param paragraphText 段落文本
     * @return 好词好句渲染数据列表
     */
    private static List<TextRenderData> findGoodTexts(List<BizCompositionGoodWords> goodWordsList, String paragraphNum, String paragraphText) {
        List<TextRenderData> result = new ArrayList<>();
        if (goodWordsList == null || goodWordsList.isEmpty()) {
            return result;
        }
        
        // 筛选当前段落的好词好句
        List<BizCompositionGoodWords> paragraphGoodWords = goodWordsList.stream()
                .filter(goodWord -> paragraphNum.equals(goodWord.getGparagraph()))
                .collect(Collectors.toList());
        
        // 记录好词好句但不应用样式 - 当前POI-TL版本不支持段落内文本部分样式
        for (BizCompositionGoodWords goodWord : paragraphGoodWords) {
            if (goodWord.getGcontent() != null && !goodWord.getGcontent().isEmpty()) {
                log.debug("段落{}中发现好词好句: {}", paragraphNum, goodWord.getGcontent());
                TextRenderData goodText = Texts.of(goodWord.getGcontent())
                        .color("0000FF") // 蓝色
                        .create();
                result.add(goodText);
            }
        }
        
        return result;
    }
    
    /**
     * 创建富文本段落，支持错误文本和好文本的高亮显示
     *
     * @param text 段落文本
     * @param errorRanges 错误文本范围列表
     * @param goodRanges 好文本范围列表
     * @return 富文本渲染数据
     */
    public static TextRenderData createRichParagraph(String text, List<TextRange> errorRanges, List<TextRange> goodRanges) {
        if (text == null || text.isEmpty()) {
            return Texts.of("").create();
        }
        
        // 如果没有需要高亮的文本，直接返回普通文本
        if ((errorRanges == null || errorRanges.isEmpty()) && (goodRanges == null || goodRanges.isEmpty())) {
            return Texts.of(text).create();
        }
        
        // TODO: 实现复杂的文本高亮逻辑
        // 注意: 这里需要POI-TL支持的高级富文本功能，当前版本可能不支持段落内的部分文本样式
        // 此处仅返回普通文本，实际实现需要根据POI-TL版本进行调整
        
        return Texts.of(text).create();
    }
    
    /**
     * 文本范围类，用于表示需要高亮的文本范围
     */
    public static class TextRange {
        private int start;
        private int end;
        private String text;
        private String type; // "error" 或 "good"
        
        public TextRange(int start, int end, String text, String type) {
            this.start = start;
            this.end = end;
            this.text = text;
            this.type = type;
        }
        
        public int getStart() {
            return start;
        }
        
        public int getEnd() {
            return end;
        }
        
        public String getText() {
            return text;
        }
        
        public String getType() {
            return type;
        }
    }

    /**
     * 创建多段落内容（带首行缩进）
     *
     * @param text 包含多段落的文本内容（段落之间用换行符分隔）
     * @return 多段落渲染数据列表（使用ParagraphRenderData）
     */
    public static List<ParagraphRenderData> createParagraphsWithIndent(String text) {
        if (text == null || text.isEmpty()) {
            return Collections.singletonList(Paragraphs.of("").create());
        }
        
        String[] paragraphs = text.split("\n");
        List<ParagraphRenderData> result = new ArrayList<>(paragraphs.length);
        
        for (String paragraph : paragraphs) {
            // 添加两个全角空格作为首行缩进
            if (!paragraph.trim().isEmpty()) {
                result.add(Paragraphs.of("　　" + paragraph.trim()).create());
            } else {
                result.add(Paragraphs.of("").create());
            }
        }
        
        return result;
    }

    /**
     * 验证模板文件是否可以正常加载
     *
     * @param templatePath 模板路径（classpath下）
     * @return 验证结果信息
     */
    public static String validateTemplate(String templatePath) {
        try {
            ClassPathResource resource = new ClassPathResource(templatePath);
            
            // 检查文件是否存在
            if (!resource.exists()) {
                return "模板文件不存在: " + templatePath;
            }
            
            // 尝试获取文件大小
            long fileSize = resource.contentLength();
            log.info("模板文件验证成功: {}, 文件大小: {} bytes", templatePath, fileSize);
            
            // 尝试编译模板
            try (InputStream templateStream = resource.getInputStream()) {
                XWPFTemplate template = XWPFTemplate.compile(templateStream);
                int variableCount = template.getElementTemplates().size();
                template.close();
                
                return String.format("模板验证成功: %s, 文件大小: %d bytes, 变量数量: %d", 
                    templatePath, fileSize, variableCount);
                    
            } catch (Exception e) {
                return "模板编译失败: " + e.getMessage();
            }
            
        } catch (Exception e) {
            log.error("验证模板文件时发生错误: {}", e.getMessage(), e);
            return "验证失败: " + e.getMessage();
        }
    }
}