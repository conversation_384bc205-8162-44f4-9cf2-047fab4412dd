/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.packages.BsePackageAddDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageDeleteDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageSearchDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageUpdateDTO;
import com.izpan.modules.base.domain.vo.BsePackageVO;
import com.izpan.modules.base.domain.vo.BsePackageMiniVO;
import com.izpan.modules.base.facade.IBsePackageFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 基础服务-套餐信息表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BsePackageController
 * @CreateTime 2025-07-20 - 12:54:18
 */

@RestController
@Tag(name = "基础服务-套餐信息表")
@RequiredArgsConstructor
@RequestMapping("bse_package")
public class BsePackageController {

    @NonNull
    private IBsePackageFacade bsePackageFacade;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取基础服务-套餐信息表列表")
    public Result<RPage<BsePackageVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                            @Parameter(description = "查询对象") BsePackageSearchDTO bsePackageSearchDTO) {
        return Result.data(bsePackageFacade.listBsePackagePage(pageQuery, bsePackageSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取基础服务-套餐信息表详细信息")
    public Result<BsePackageVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bsePackageFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增基础服务-套餐信息表")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BsePackageAddDTO bsePackageAddDTO) {
        return Result.status(bsePackageFacade.add(bsePackageAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新基础服务-套餐信息表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BsePackageUpdateDTO bsePackageUpdateDTO) {
        return Result.status(bsePackageFacade.update(bsePackageUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除基础服务-套餐信息表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BsePackageDeleteDTO bsePackageDeleteDTO) {
        return Result.status(bsePackageFacade.batchDelete(bsePackageDeleteDTO));
    }

    @GetMapping("/mini/list")
    @Operation(operationId = "6", summary = "小程序-获取套餐列表")
    public Result<List<BsePackageMiniVO>> miniList() {
        return Result.data(bsePackageFacade.listMiniPackages());
    }

}