package com.izpan.admin.controller.weixin;

import com.izpan.common.api.Result;
import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import com.izpan.modules.system.domain.dto.user.SysUserAddDTO;
import com.izpan.modules.system.domain.vo.SysUserVO;
import com.izpan.modules.system.facade.ISysUserFacade;
import com.izpan.modules.system.service.ISysUserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/wx")
public class WXLoginController {

    @NonNull
    private ISysUserFacade sysUserFacade;

    @NonNull
    private ISysUserService sysUserService;
    /**
     * 用户注册
     * @param registerData 注册数据
     * @return 注册结果
     */
    @PostMapping("/register")
    @Operation(operationId = "1", summary = "微信用户注册")
    public Result<Boolean> register(@RequestBody Map<String, String> registerData) {
        try {
            // 从请求中获取注册信息
            String userName = registerData.get("userName");
            String nickName = registerData.get("nickName");
            String password = registerData.get("password");

            // 验证必填字段
            if (userName == null || userName.trim().isEmpty()) {
                return Result.failure("账号不能为空");
            }
            if (nickName == null || nickName.trim().isEmpty()) {
                return Result.failure("姓名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.failure("密码不能为空");
            }
            
            // 检查用户名是否已存在
            SysUserVO existingUser = sysUserService.getUserByUserName(userName);
            if (existingUser != null) {
                return Result.failure("该账号已被注册，请更换其他账号");
            }
            
            // 创建用户对象
            SysUserAddDTO userBO = new SysUserAddDTO();
            userBO.setUserName(userName);
            userBO.setNickName(nickName);
            userBO.setRealName(nickName);
            userBO.setPassword(password);
            userBO.setUserType(SystemUserTypeEnum.USER.getValue());

            // 调用服务添加用户
            boolean result = sysUserFacade.addUser(userBO) != null;
            
            return Result.status(result);
        } catch (Exception e) {
            return Result.failure("注册失败：" + e.getMessage());
        }
    }
}
