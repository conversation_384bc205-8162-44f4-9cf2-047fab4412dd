/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.biz.domain.dto.composition.release.*;
import com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO;
import com.izpan.modules.biz.facade.IBizCompositionReleaseFacade;
import com.izpan.modules.system.domain.vo.SysUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 作文发布表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.biz.BizCompositionReleaseController
 * @CreateTime 2024-12-18 - 12:57:47
 */

@RestController
@Tag(name = "作文发布表")
@RequiredArgsConstructor
@RequestMapping("biz_composition_release")
public class BizCompositionReleaseController {

    @NonNull
    private IBizCompositionReleaseFacade bizCompositionReleaseFacade;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取作文发布表列表")
    public Result<RPage<BizCompositionReleaseVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                       @Parameter(description = "查询对象") BizCompositionReleaseSearchDTO bizCompositionReleaseSearchDTO) {
        return Result.data(bizCompositionReleaseFacade.listBizCompositionReleasePage(pageQuery, bizCompositionReleaseSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取作文发布表详细信息")
    public Result<BizCompositionReleaseVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bizCompositionReleaseFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增作文发布表")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return Result.status(bizCompositionReleaseFacade.add(bizCompositionReleaseAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新作文发布表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BizCompositionReleaseUpdateDTO bizCompositionReleaseUpdateDTO) {
        return Result.status(bizCompositionReleaseFacade.update(bizCompositionReleaseUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除作文发布表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BizCompositionReleaseDeleteDTO bizCompositionReleaseDeleteDTO) {
        return Result.status(bizCompositionReleaseFacade.batchDelete(bizCompositionReleaseDeleteDTO));
    }

    @PostMapping("/createComposition")
    @Operation(operationId = "6", summary = "保存作业")
    public Result<Boolean> createComposition(@Parameter(description = "创建作业") @RequestBody BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return Result.status(bizCompositionReleaseFacade.compositionCreate(bizCompositionReleaseAddDTO));
    }

    @PostMapping("/createCompositionAndRelease")
    @Operation(operationId = "7", summary = "保存作业并发布")
    public Result<Boolean> createCompositionAndRelease(@Parameter(description = "创建作业") @RequestBody BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return Result.status(bizCompositionReleaseFacade.compositionCreateAndRelease(bizCompositionReleaseAddDTO));
    }

    @PostMapping("/compositionRelease")
    @Operation(operationId = "8", summary = "发布作业")
    public Result<Boolean> compositionRelease(@Parameter(description = "发布作业") @RequestBody CompositionReleaseDTO compositionReleaseDTO) {
        return Result.status(bizCompositionReleaseFacade.compositionRelease(compositionReleaseDTO));
    }

    @PostMapping("/queryCompositionList")
    @Operation(operationId = "9", summary = "用户查询作文列表")
    public Result<List<BizCompositionReleaseVO>> queryCompositionList(@RequestBody CompositionStudentQueryDTO compositionStudentQueryDTO) {
        return Result.data(bizCompositionReleaseFacade.queryCompositionList(compositionStudentQueryDTO));
    }

    @GetMapping("/queryStudentAnswerCompositionInfo")
    @Operation(operationId = "10", summary = "查询学生是否可以回答该作文")
    public Result<SysUserVO> studentAnswerComposition(@Parameter(description = "作文ID", required = true) Long compositionId, @Parameter(description = "学生ID", required = true) String studentNum,
                                                      @Parameter(description = "是否校验") Boolean isCheck) {
        if (isCheck == null) {
            isCheck = true;
        }
        return Result.data(bizCompositionReleaseFacade.studentAnswerComposition(compositionId, studentNum, isCheck));
    }

    @PostMapping("/list")
    @Operation(operationId = "11", summary = "老师查询作文列表")
    public Result<List<BizCompositionReleaseVO>> list(@Parameter(description = "查询对象") @RequestBody BizCompositionReleaseSearchDTO bizCompositionReleaseSearchDTO) {
        return Result.data(bizCompositionReleaseFacade.list(bizCompositionReleaseSearchDTO));
    }

}