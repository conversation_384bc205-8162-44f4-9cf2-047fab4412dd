/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.wechat;

import com.izpan.common.api.Result;
import com.izpan.modules.base.domain.dto.wechat.WechatLoginDTO;
import com.izpan.modules.base.domain.vo.WechatLoginResultVO;
import com.izpan.modules.base.service.IWechatMiniProgramService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 微信小程序 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.wechat.WechatMiniProgramController
 * @CreateTime 2025-07-20
 */
@Slf4j
@RestController
@Tag(name = "微信小程序")
@RequiredArgsConstructor
@RequestMapping("wechat/miniprogram")
public class WechatMiniProgramController {

    private final IWechatMiniProgramService wechatMiniProgramService;

    @PostMapping("/login")
    @Operation(operationId = "1", summary = "微信小程序登录获取openid")
    public Result<WechatLoginResultVO> login(@Parameter(description = "登录信息") @RequestBody @Valid WechatLoginDTO wechatLoginDTO) {
        WechatLoginResultVO result = wechatMiniProgramService.getOpenidByCode(wechatLoginDTO);
        
        if (result.getErrcode() != null && result.getErrcode() != 0) {
            return Result.failure(result.getErrmsg());
        }
        
        return Result.data(result);
    }

}
