/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuAddDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuDeleteDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuSearchDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuUpdateDTO;
import com.izpan.modules.system.domain.vo.SysUserRoleMenuVO;
import com.izpan.modules.system.facade.ISysUserRoleMenuFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 用户角色菜单 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.system.SysUserRoleMenuController
 * @CreateTime 2024-12-13 - 22:46:35
 */

@RestController
@Tag(name = "用户角色菜单")
@RequiredArgsConstructor
@RequestMapping("sys_user_role_menu")
public class SysUserRoleMenuController {

    @NonNull
    private ISysUserRoleMenuFacade sysUserRoleMenuFacade;

    @GetMapping("/page")
    @SaCheckPermission("sys:user:role:menu:page")
    @Operation(operationId = "1", summary = "获取用户角色菜单列表")
    public Result<RPage<SysUserRoleMenuVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                 @Parameter(description = "查询对象") SysUserRoleMenuSearchDTO sysUserRoleMenuSearchDTO) {
        return Result.data(sysUserRoleMenuFacade.listSysUserRoleMenuPage(pageQuery, sysUserRoleMenuSearchDTO));
    }

    @GetMapping("/{id}")
    @SaCheckPermission("sys:user:role:menu:get")
    @Operation(operationId = "2", summary = "根据ID获取用户角色菜单详细信息")
    public Result<SysUserRoleMenuVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(sysUserRoleMenuFacade.get(id));
    }

    @PostMapping("/")
    @SaCheckPermission("sys:user:role:menu:add")
    @Operation(operationId = "3", summary = "新增用户角色菜单")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody SysUserRoleMenuAddDTO sysUserRoleMenuAddDTO) {
        return Result.status(sysUserRoleMenuFacade.add(sysUserRoleMenuAddDTO));
    }

    @PutMapping("/")
    @SaCheckPermission("sys:user:role:menu:update")
    @Operation(operationId = "4", summary = "更新用户角色菜单信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody SysUserRoleMenuUpdateDTO sysUserRoleMenuUpdateDTO) {
        return Result.status(sysUserRoleMenuFacade.update(sysUserRoleMenuUpdateDTO));
    }

    @DeleteMapping("/")
    @SaCheckPermission("sys:user:role:menu:delete")
    @Operation(operationId = "5", summary = "批量删除用户角色菜单信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody SysUserRoleMenuDeleteDTO sysUserRoleMenuDeleteDTO) {
        return Result.status(sysUserRoleMenuFacade.batchDelete(sysUserRoleMenuDeleteDTO));
    }

    @PostMapping("/batchAdd")
    //@SaCheckPermission("sys:user:role:menu:batchAdd")
    @Operation(operationId = "6", summary = "新增用户角色菜单")
    public Result<Boolean> batchAdd(@Parameter(description = "新增对象") @RequestBody List<SysUserRoleMenuAddDTO> sysUserRoleMenuAddDTO) {
        return Result.status(sysUserRoleMenuFacade.batchAdd(sysUserRoleMenuAddDTO));
    }

    @PostMapping("/getUserMenuAndPermission/{userId}")
   // @SaCheckPermission("sys:user:role:menu:add")
    @Operation(operationId = "6", summary = "查询用户角色菜单")
    public Result< List<SysUserRoleMenuVO>> getUserMenuAndPermission(@Parameter(description = "ID") @PathVariable("userId") Long userId) {
        return Result.data(sysUserRoleMenuFacade.getUserId(userId));
    }


}