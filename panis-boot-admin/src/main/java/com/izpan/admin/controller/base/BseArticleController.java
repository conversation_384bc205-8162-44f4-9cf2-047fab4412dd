/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.article.BseArticleAddDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleDeleteDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleSearchDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleUpdateDTO;
import com.izpan.modules.base.domain.vo.BseArticleVO;
import com.izpan.modules.base.facade.IBseArticleFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

/**
 * 文章表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BseArticleController
 * @CreateTime 2025-03-24 - 11:17:41
 */

@RestController
@Tag(name = "文章表")
@RequiredArgsConstructor
@RequestMapping("bse_article")
public class BseArticleController {

    @NonNull
    private IBseArticleFacade bseArticleFacade;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取文章表列表")
    public Result<RPage<BseArticleVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                            @Parameter(description = "查询对象") BseArticleSearchDTO bseArticleSearchDTO) {
        return Result.data(bseArticleFacade.listBseArticlePage(pageQuery, bseArticleSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取文章表详细信息")
    public Result<BseArticleVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bseArticleFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增文章表")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BseArticleAddDTO bseArticleAddDTO) {
        return Result.status(bseArticleFacade.add(bseArticleAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新文章表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BseArticleUpdateDTO bseArticleUpdateDTO) {
        return Result.status(bseArticleFacade.update(bseArticleUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除文章表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BseArticleDeleteDTO bseArticleDeleteDTO) {
        return Result.status(bseArticleFacade.batchDelete(bseArticleDeleteDTO));
    }

}