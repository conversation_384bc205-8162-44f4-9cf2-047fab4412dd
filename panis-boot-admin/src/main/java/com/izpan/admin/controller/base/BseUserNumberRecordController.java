/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordAddDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordDeleteDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordSearchDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordUpdateDTO;
import com.izpan.modules.base.domain.entity.BseUserNumberRecord;
import com.izpan.modules.base.domain.vo.BseUserNumberRecordVO;
import com.izpan.modules.base.facade.IBseUserNumberRecordFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

import java.util.List;

/**
 *  Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BseUserNumberRecordController
 * @CreateTime 2025-03-24 - 22:55:48
 */

@RestController
@Tag(name = "消费记录")
@RequiredArgsConstructor
@RequestMapping("bse_user_number_record")
public class BseUserNumberRecordController {

    @NonNull
    private IBseUserNumberRecordFacade bseUserNumberRecordFacade;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取列表")
    public Result<RPage<BseUserNumberRecordVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                     @Parameter(description = "查询对象") BseUserNumberRecordSearchDTO bseUserNumberRecordSearchDTO) {
        return Result.data(bseUserNumberRecordFacade.listBseUserNumberRecordPage(pageQuery, bseUserNumberRecordSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取详细信息")
    public Result<BseUserNumberRecordVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bseUserNumberRecordFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BseUserNumberRecordAddDTO bseUserNumberRecordAddDTO) {
        return Result.status(bseUserNumberRecordFacade.add(bseUserNumberRecordAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BseUserNumberRecordUpdateDTO bseUserNumberRecordUpdateDTO) {
        return Result.status(bseUserNumberRecordFacade.update(bseUserNumberRecordUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BseUserNumberRecordDeleteDTO bseUserNumberRecordDeleteDTO) {
        return Result.status(bseUserNumberRecordFacade.batchDelete(bseUserNumberRecordDeleteDTO));
    }

    @GetMapping("/user_list")
    @Operation(operationId = "6", summary = "获取用户购买记录列表")
    public Result<List<BseUserNumberRecord>> userList(@Parameter(description = "查询对象") BseUserNumberRecordSearchDTO bseUserNumberRecordSearchDTO) {
        return Result.data(bseUserNumberRecordFacade.listBseUserNumberRecordByUserId(bseUserNumberRecordSearchDTO));
    }
}