package com.izpan.admin.controller.system;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.izpan.admin.config.TaskSubmitter;
import com.izpan.modules.base.domain.dto.school.BseSchoolAddDTO;
import com.izpan.modules.base.facade.IBseSchoolFacade;
import com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogAddDTO;
import com.izpan.modules.biz.domain.dto.composition.release.BizCompositionReleaseAddDTO;
import com.izpan.modules.biz.domain.entity.*;
import com.izpan.modules.biz.facade.IBizCompositionLogFacade;
import com.izpan.modules.biz.service.IBizCompositionReleaseService;
import com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsAddDTO;
import com.izpan.modules.system.domain.dto.user.SysUserAddDTO;
import com.izpan.modules.system.domain.entity.SysOrgUnits;
import com.izpan.modules.system.facade.ISysOrgUnitsFacade;
import com.izpan.modules.system.facade.ISysUserFacade;
import com.izpan.modules.tools.service.RemoteDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;

/**
 * 同步数据控制器（并发安全改造版）
 */
@RestController
@RequestMapping("/sync-data")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "同步数据")
public class SyncDataController {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy/M/d H:m:s");

    // 年级缓存：schoolId:gradeName → gradeId
    private final Map<String, Long> gradeCache = new ConcurrentHashMap<>();

    // 班级缓存：schoolId:gradeId:clazzName → clazzId
    private final Map<String, Long> clazzCache = new ConcurrentHashMap<>();

    // 作文批次缓存：mappingId -> id
    private final Map<Long, Long> compositionBatchCache = new ConcurrentHashMap<>();

    // 作文日志映射缓存（线程安全）
    private static final Map<String, Long> COMPOSITION_LOG_MAP = new ConcurrentHashMap<>();

    // 接口地址
    private static final String BASE_URL = "http://hyzy.cewenwang.com/interface/Data_School.asmx";
//    private static final String NEW_BASE_URL = "http://hyzy.cewenwang.com/interface/Data_School.asmx";
    private static final String DLS_URL = BASE_URL + "/GetDLS";
    private static final String SCHOOL_URL = BASE_URL + "/GetSchool?aid=%s";
    private static final String TEACHER_URL = BASE_URL + "/GetTeach?schoolid=%s&schoolName=%s";
    private static final String STUDENT_URL = BASE_URL + "/GetStudent?tid=%s&schoolName=%s";
    private static final String WRITING_URL = BASE_URL + "/selectzuowen_list?uid=%s&schoolid=%s";
    private static final String WRITING_BATCH_URL = BASE_URL + "/select_list_b?schoolID=%s&schoolName=%s";
    private static final String WRITING_IDS_URL = BASE_URL + "/selectzuowenlist_ids?uid=%s";
    private static final String WRITING_BYIDS_URL  = BASE_URL + "/selectzuowen_list_byids?ids=%s";

    @NonNull
    private final IBseSchoolFacade bseSchoolFacade;

    @NonNull
    private final RemoteDataService remoteDataService;

    @NonNull
    private final ISysUserFacade sysUserFacade;

    @NonNull
    private final ISysOrgUnitsFacade sysOrgUnitsFacade;

    @NonNull
    private final IBizCompositionLogFacade bizCompositionLogFacade;

    @NonNull
    private final IBizCompositionReleaseService bizCompositionReleaseService;

    @PostConstruct
    public void initTaskSubmitter() {
        taskSubmitter = new TaskSubmitter(teacherSyncExecutor, new Semaphore(20));
        studentTaskSubmitter = new TaskSubmitter(executor, new Semaphore(300));
        schoolTaskSubmitter = new TaskSubmitter(schoolSyncExecutor, new Semaphore(10));
        writingTaskSubmitter = new TaskSubmitter(loadExecutor, new Semaphore(100));
    }

    private TaskSubmitter taskSubmitter;

    private TaskSubmitter studentTaskSubmitter;

    private TaskSubmitter schoolTaskSubmitter;

    private TaskSubmitter writingTaskSubmitter;

    @Resource(name = "dataSyncExecutor") // 自定义线程池
    private ExecutorService executor;

    @Resource(name = "teacherSyncExecutor") // 自定义线程池
    private ExecutorService teacherSyncExecutor;

    @Resource(name = "schoolSyncExecutor") // 自定义线程池
    private ExecutorService schoolSyncExecutor;

    @Resource(name = "loadExecutor")
    private ExecutorService loadExecutor;


    @RequestMapping("/monitor")
    public void monitor() {
        if (!(executor instanceof ThreadPoolExecutor threadPoolExecutor)) {
            log.warn("线程池类型不匹配，无法监控");
            return;
        }

        long taskCount = threadPoolExecutor.getTaskCount();
        long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();
        int activeCount = threadPoolExecutor.getActiveCount();
        int poolSize = threadPoolExecutor.getPoolSize();
        int queueSize = threadPoolExecutor.getQueue().size();
        int largestPoolSize = threadPoolExecutor.getLargestPoolSize();
        int corePoolSize = threadPoolExecutor.getCorePoolSize();
        int maximumPoolSize = threadPoolExecutor.getMaximumPoolSize();

        log.info("[线程池监控] 核心线程数: {}, 最大线程数: {}, 当前线程数: {}, 最大并发记录: {}",
                corePoolSize, maximumPoolSize, poolSize, largestPoolSize);
        log.info("[线程池监控] 总任务数: {}, 已完成任务数: {}, 队列任务数: {}",
                taskCount, completedTaskCount, queueSize);

        if (queueSize > 0) {
            log.warn("⚠️ 警告：线程池任务队列中有等待任务，请注意负载情况");
        }

        if (activeCount >= maximumPoolSize) {
            log.warn("❗警告：线程池已达到最大线程数，请考虑扩容或优化任务执行时间");
        }

        if (!(teacherSyncExecutor instanceof ThreadPoolExecutor teacherSyncThreadPoolExecutor)) {
            log.warn("线程池类型不匹配，无法监控");
            return;
        }

        long taskCount1 = teacherSyncThreadPoolExecutor.getTaskCount();
        long completedTaskCount1 = teacherSyncThreadPoolExecutor.getCompletedTaskCount();
        int activeCount1 = teacherSyncThreadPoolExecutor.getActiveCount();
        int poolSize1 = teacherSyncThreadPoolExecutor.getPoolSize();
        int queueSize1 = teacherSyncThreadPoolExecutor.getQueue().size();
        int largestPoolSize1 = teacherSyncThreadPoolExecutor.getLargestPoolSize();
        int corePoolSize1 = teacherSyncThreadPoolExecutor.getCorePoolSize();
        int maximumPoolSize1 = teacherSyncThreadPoolExecutor.getMaximumPoolSize();

        log.info("[老师线程池监控] 核心线程数: {}, 最大线程数: {}, 当前线程数: {}, 最大并发记录: {}",
                corePoolSize1, maximumPoolSize1, poolSize1, largestPoolSize1);
        log.info("[老师线程池监控] 总任务数: {}, 已完成任务数: {}, 队列任务数: {}",
                taskCount1, completedTaskCount1, queueSize1);

        if (queueSize1 > 0) {
            log.warn("⚠️ 警告：线程池任务队列中有等待任务，请注意负载情况");
        }

        if (activeCount1 >= maximumPoolSize1) {
            log.warn("❗警告：线程池已达到最大线程数，请考虑扩容或优化任务执行时间");
        }
    }

    @PostMapping("/init")
    @Operation(operationId = "1", summary = "同步数据")
    public void init() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("开始同步数据");

        JSONArray agentList = getRemoteArray(DLS_URL, "schoolList");
        processAgents(agentList);

        stopWatch.stop();
        log.info("同步数据完成，耗时：{}s", stopWatch.getTotalTimeSeconds());
    }

    private void clearCache() {
        gradeCache.clear();
        clazzCache.clear();
        compositionBatchCache.clear();
    }

    @PostMapping("/syncAgent")
    @Operation(operationId = "2", summary = "同步代理商")
    public void syncAgent(@RequestParam("agentId") Long agentId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("开始同步{}代理商", agentId);
        JSONArray agentList = getRemoteArray(DLS_URL, "schoolList");
        if (agentList == null || agentList.isEmpty()) {
            log.warn("未获取到代理商数据");
            return;
        }
        //遍历获取指定代理商数据
        for (int i = 0; i < agentList.size(); i++) {
            JSONObject agentObj = agentList.getJSONObject(i);
            if (agentObj.getLongValue("schoolId") == agentId) {
                processAgent(agentObj);
                break;
            }
        }
        clearCache();
        stopWatch.stop();
        log.info("同步{}代理商完成，耗时：{}s", agentId, stopWatch.getTotalTimeSeconds());
    }

    /**
     * 并行处理所有代理商
     */
    @SneakyThrows
    private void processAgents(JSONArray agents) {
        if (agents == null || agents.isEmpty()) return;

        for (int i = 0; i < agents.size(); i++) {
            JSONObject agentObj = agents.getJSONObject(i);
            log.info("开始同步代理商：{}", agents.getJSONObject(i).getString("schoolId"));

            processAgent(agentObj);

            log.info("已同步代理商：{}", agentObj.getString("schoolId"));

            clearCache();
            // 同步完一个代理商暂停 2分钟
            TimeUnit.MINUTES.sleep(2);
        }
    }

    private void processAgent(JSONObject agentObj) {
        Long agentId = saveAgent(agentObj);
        if (agentId == null) return;

        JSONArray schools = getRemoteArray(String.format(SCHOOL_URL, agentObj.getString("schoolId")), "schoolList");
        processSchools(schools, agentId);
    }

    /**
     * 并行处理学校
     */
    private void processSchools(JSONArray schools, Long agentId) {
        if (schools == null || schools.isEmpty()) return;
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int j = 0; j < schools.size(); j++) {
            JSONObject schoolObj = schools.getJSONObject(j);
            futures.add(schoolTaskSubmitter.submit(() -> processSchool(schoolObj, agentId)));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private void processSchool(JSONObject schoolObj, Long agentId) {
        Long schoolId = saveSchool(schoolObj, agentId);
        if (schoolId == null) return;

        // 处理作文批次
        JSONArray batches = getRemoteArray(String.format(WRITING_BATCH_URL, schoolObj.getString("schoolId"), schoolObj.getString("schoolName")), "BatchList");
        processCompositionBatches(batches, schoolId, schoolObj);

        // 处理教师和学生
        JSONArray teachers = getRemoteArray(String.format(TEACHER_URL, schoolObj.getString("schoolId"), schoolObj.getString("schoolName")), "usreList");
        processTeachers(teachers, schoolId, schoolObj);
    }

    @RequestMapping("/syncCb")
    public void syncCb(@RequestParam("schoolId") String schoolId,
                       @RequestParam("schoolName") String schoolName,
                       @RequestParam("realSchoolId") Long realSchoolId) {
        log.info("开始同步批次：{}", schoolId);
        JSONObject schoolObj = new JSONObject();
        schoolObj.put("schoolId", schoolId);
        schoolObj.put("schoolName", schoolName);
        // 处理作文批次
        JSONArray batches = getRemoteArray(String.format(WRITING_BATCH_URL, schoolObj.getString("schoolId"), schoolObj.getString("schoolName")), "BatchList");
        processCompositionBatches(batches, realSchoolId, schoolObj);

        // 处理教师和学生
        JSONArray teachers = getRemoteArray(String.format(TEACHER_URL, schoolObj.getString("schoolId"), schoolObj.getString("schoolName")), "usreList");
        processTeachers(teachers, realSchoolId, schoolObj);
        log.info("同步批次：{}完成", schoolId);
    }

    /**
     * 并行处理作文批次
     */
    private void processCompositionBatches(JSONArray batches, Long schoolId, JSONObject schoolObj) {
        if (batches == null || batches.isEmpty()) return;
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int m = 0; m < batches.size(); m++) {
            JSONObject batchObj = batches.getJSONObject(m);
            futures.add(CompletableFuture.runAsync(() -> processCompositionBatch(batchObj, schoolId, schoolObj), executor));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private void processCompositionBatch(JSONObject batchObj, Long schoolId, JSONObject schoolObj) {
        // 处理作文批次
        saveCompositionRelease(batchObj, schoolId, schoolObj.getString("schoolName"));
    }

    /**
     * 并行处理教师
     */
    private void processTeachers(JSONArray teachers, Long schoolId, JSONObject schoolObj) {
        if (teachers == null || teachers.isEmpty()) return;

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int k = 0; k < teachers.size(); k++) {
            JSONObject teacherObj = teachers.getJSONObject(k);
            futures.add(taskSubmitter.submit(() -> processTeacher(teacherObj, schoolId, schoolObj)));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    @SneakyThrows
    private void processTeacher(JSONObject teacherObj, Long schoolId, JSONObject schoolObj) {
        Pair<SysUserAddDTO, Long> pair = saveTeacher(teacherObj, schoolId, schoolObj);
        String userId = teacherObj.getString("userId");

        processWritings(userId, pair);

        JSONArray students = getRemoteArray(String.format(STUDENT_URL, userId, schoolObj.getString("schoolName")), "usreList");
        processStudents(students, schoolId, schoolObj);
    }

    /**
     * 并行处理学生
     */
    private void processStudents(JSONArray students, Long schoolId, JSONObject schoolObj) {
        if (students == null || students.isEmpty()) return;

        List<CompletableFuture<Void>> batchFutures = new ArrayList<>();

        for (int l = 0; l < students.size(); l++) {
            JSONObject studentObj = students.getJSONObject(l);
            batchFutures.add(studentTaskSubmitter.submit(() -> processStudent(studentObj, schoolId, schoolObj)));
        }
        CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0])).join();
    }

    private void processStudent(JSONObject studentObj, Long schoolId, JSONObject schoolObj) {
        Pair<SysUserAddDTO, Long> pair = saveStudent(studentObj, schoolId, schoolObj);
        String userId = studentObj.getString("userId");


        processWritings(userId,pair);
    }

    private void processWritings(String userId,Pair<SysUserAddDTO, Long> pair) {

        String[] remoteIds = getRemoteIds(String.format(WRITING_IDS_URL, userId));
        if (remoteIds == null || remoteIds.length == 0) return;
        // 设置每批次大小
        int batchSize = 100;
        int total = remoteIds.length;

        List<CompletableFuture<Void>> batchFutures = new ArrayList<>();
        for (int i = 0; i < total; i += batchSize) {
            // 计算当前批次的结束位置，避免越界
            int end = Math.min(i + batchSize, total);
            // 截取当前批次的 ID 数组
            String[] batchIds = Arrays.copyOfRange(remoteIds, i, end);

            // 提交异步任务处理当前批次
            batchFutures.add(writingTaskSubmitter.submit(() -> {
                JSONArray writings = getRemoteArray(String.format(WRITING_BYIDS_URL, String.join(",",batchIds)), "CpList");
                processWritings(writings, pair.getLeft(), pair.getRight());
            }));
        }
        // 等待所有批次任务完成
        CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0])).join();

    }

    /**
     * 同步处理作文
     */
    private void processWritings(JSONArray writings, SysUserAddDTO sysUserAddDTO, Long userId) {
        if (writings == null || writings.isEmpty()) return;

        for (int m = 0; m < writings.size(); m++) {
            JSONObject writingObj = writings.getJSONObject(m);
            BizCompositionLogAddDTO dto = convertToBizCompositionLogBO(writingObj, sysUserAddDTO, userId);
            bizCompositionLogFacade.syncAdd(dto);
        }
    }

    private JSONArray getRemoteArray(String url, String field) {
        return remoteDataService.fetchJsonArray(url, field);
    }

    private String[] getRemoteIds(String url) {
        return remoteDataService.fetchJsonIds(url, "ids");
    }

    private Long saveAgent(JSONObject obj) {
        BseSchoolAddDTO dto = convertToBseSchoolBO(obj, 1);
        dto.setParentId(0L);
        return bseSchoolFacade.add(dto);
    }

    private Long saveSchool(JSONObject obj, Long parentId) {
        BseSchoolAddDTO dto = convertToBseSchoolBO(obj, 2);
        dto.setParentId(parentId);
        return bseSchoolFacade.add(dto);
    }

    private Pair<SysUserAddDTO, Long> saveTeacher(JSONObject obj, Long schoolId, JSONObject schoolObj) {
        SysUserAddDTO dto = convertToSysUserBO(obj, schoolId, schoolObj);
        return Pair.of(dto, sysUserFacade.addUser(dto));
    }

    private Pair<SysUserAddDTO, Long> saveStudent(JSONObject obj, Long schoolId, JSONObject schoolObj) {
        SysUserAddDTO dto = convertToStudentSysUserBO(obj, schoolId, schoolObj);
        return Pair.of(dto, sysUserFacade.addUser(dto));
    }

    private Long saveCompositionRelease(JSONObject obj, Long schoolId, String schoolName) {
        BizCompositionReleaseAddDTO dto = convertToBizCompositionReleaseBO(obj, schoolId, schoolName);
        return bizCompositionReleaseService.syncCompositionCreateOrRelease(dto);
    }

    /**
     * 转换为 BseSchoolAddDTO
     */
    private BseSchoolAddDTO convertToBseSchoolBO(JSONObject item, Integer type) {
        BseSchoolAddDTO dto = new BseSchoolAddDTO();
        dto.setSchoolName(item.getString("schoolName"));
        dto.setType(type);
        dto.setPrincipalName(item.getString("principalName"));
        dto.setContactPhone(item.getString("userName"));
        dto.setContactPerson(item.getString("principalName"));
        dto.setEvaluationNumber(item.getInteger("evaluationNumber"));
        dto.setStatus("1");
        dto.setExpireTime(LocalDateTime.parse(item.getString("expireTime"), FORMATTER));
        dto.setMappingId(item.getLong("schoolId"));
        dto.setPassword(item.getString("password"));
        return dto;
    }

    private SysUserAddDTO convertToSysUserBO(JSONObject item, Long schoolId, JSONObject schoolObj) {
        SysUserAddDTO dto = new SysUserAddDTO();
        dto.setMappingId(item.getLong("userId"));
        dto.setUserName(item.getString("userName"));
        dto.setRealName(item.getString("realName"));
        dto.setNickName(item.getString("realName"));
        dto.setPhone(item.getString("phone"));
        dto.setSchoolId(Convert.toStr(schoolId));
        dto.setSchoolName(item.getString("schoolName"));

        String clazzName = item.getString("clazzName");
        Pair<String, String> pair = getClazzStr(clazzName, schoolId, schoolObj);
        dto.setClazzId(pair.getLeft());
        dto.setClazzName(pair.getRight());

        dto.setUserType("3");
        dto.setMicroLessonsNumber(item.getInteger("microLessonsNumber"));
        dto.setEvaluationNumber(item.getInteger("evaluationNumber"));
        dto.setStatus("1");
        dto.setRoleIds(Collections.singletonList(1868238594641330177L));
        dto.setPassword(item.getString("password"));
        return dto;
    }

    private SysUserAddDTO convertToStudentSysUserBO(JSONObject item, Long schoolId, JSONObject schoolObj) {
        SysUserAddDTO dto = new SysUserAddDTO();
        dto.setMappingId(item.getLong("userId"));
        dto.setUserName(item.getString("userName"));
        dto.setRealName(item.getString("realName"));
        dto.setNickName(item.getString("realName"));
        dto.setPhone(item.getString("phone"));
        dto.setSchoolId(Convert.toStr(schoolId));
        dto.setSchoolName(item.getString("schoolName"));

        String[] gradeAndClazzName = matchGradeAndClazz(item.getString("clazzName"));
        String gradeName = gradeAndClazzName[0];
        String clazzName = gradeAndClazzName[1];

        String schoolName = schoolObj.getString("schoolName");
        Long gradeId = getOrSaveGrade(gradeName, schoolId, schoolName);
        Long clazzId = getOrSaveClazz(clazzName, schoolId, gradeId, schoolName);

        dto.setGradeName(gradeName);
        dto.setGradeId(Convert.toStr(gradeId));
        dto.setClazzName(clazzName);
        dto.setClazzId(Convert.toStr(clazzId));
        dto.setUserType("4");
        dto.setMicroLessonsNumber(item.getInteger("microLessonsNumber"));
        dto.setEvaluationNumber(item.getInteger("evaluationNumber"));
        dto.setStatus("1");
        dto.setPassword(item.getString("password"));

        return dto;
    }

    private Long getOrSaveGrade(String gradeName, Long schoolId, String schoolName) {
        if (StringUtils.isEmpty(gradeName)) return null;

        String key = schoolId + ":" + gradeName;
        if (gradeCache.containsKey(key)) {
            return gradeCache.get(key);
        }

        SysOrgUnitsAddDTO dto = new SysOrgUnitsAddDTO();
        dto.setName(gradeName);
        dto.setParentId(0L);
        dto.setAncestors("0");
        dto.setSchoolId(Convert.toStr(schoolId));
        dto.setSchoolName(schoolName);
        Long id = null;
        try {
            id = sysOrgUnitsFacade.add(dto);
            gradeCache.putIfAbsent(key, id);
        } catch (DuplicateKeyException e) {
            id = clazzCache.get(key);
            if (id != null) {
                return id;
            }
            SysOrgUnits grade = sysOrgUnitsFacade.querySysOrgUnitsByName(gradeName, schoolId);
            if (grade != null) {
                gradeCache.putIfAbsent(key, grade.getId());
                return grade.getId();
            } else {
                throw new RuntimeException("并发插入失败且二次查询无结果", e);
            }
        }
        return id;
    }

    private Long getOrSaveClazz(String clazzName, Long schoolId, Long gradeId, String schoolName) {
        if (StringUtils.isEmpty(clazzName)) return null;

        String key = schoolId + ":" + gradeId + ":" + clazzName;
        if (clazzCache.containsKey(key)) {
            return clazzCache.get(key);
        }

        SysOrgUnitsAddDTO dto = new SysOrgUnitsAddDTO();
        dto.setName(clazzName);
        dto.setParentId(gradeId);
        dto.setAncestors("0," + gradeId);
        dto.setSchoolId(Convert.toStr(schoolId));
        dto.setSchoolName(schoolName);
        Long id = null;
        try {
            id = sysOrgUnitsFacade.add(dto);
            clazzCache.putIfAbsent(key, id);
        } catch (DuplicateKeyException e) {
            id = clazzCache.get(key);
            if (id != null) {
                return id;
            }
            // 捕获唯一键冲突异常
            SysOrgUnits clazz = sysOrgUnitsFacade.querySysOrgUnitsByName(clazzName, schoolId);
            if (clazz != null) {
                clazzCache.putIfAbsent(key, clazz.getId());
                return clazz.getId();
            } else {
                throw new RuntimeException("并发插入失败且二次查询无结果", e);
            }
        }
        return id;
    }

    private Pair<String, String> getClazzStr(String clazzNameStr, Long schoolId, JSONObject schoolObj) {
        if (StringUtils.isEmpty(clazzNameStr)) {
            return Pair.of("", "");
        }

        StringBuilder clazzIdStr = new StringBuilder();
        StringBuilder clazzStr = new StringBuilder();

        for (String clazzName : clazzNameStr.split("，")) {
            if (StringUtils.isEmpty(clazzName)) continue;
            String[] gradeAndClazzName = matchGradeAndClazz(clazzName);
            if (gradeAndClazzName.length != 2) continue;

            String gradeName = gradeAndClazzName[0];
            String cName = gradeAndClazzName[1];

            String schoolName = schoolObj.getString("schoolName");
            Long gradeId = getOrSaveGrade(gradeName, schoolId, schoolName);
            Long clazzId = getOrSaveClazz(cName, schoolId, gradeId, schoolName);

            clazzIdStr.append(gradeId).append("_").append(clazzId).append(",");
            clazzStr.append(gradeName).append("_").append(cName).append(",");
        }

        if (!clazzIdStr.isEmpty()) {
            clazzIdStr.deleteCharAt(clazzIdStr.length() - 1);
            clazzStr.deleteCharAt(clazzStr.length() - 1);
        }

        return Pair.of(clazzIdStr.toString(), clazzStr.toString());
    }

    private String[] matchGradeAndClazz(String clazzName) {
        int index = StringUtils.indexOfAny(clazzName, '(', '（');
        if (index == -1) {
            return new String[]{clazzName, ""};
        }
        String grade = clazzName.substring(0, index).trim();
        String clazz = clazzName.substring(index);
        return new String[]{grade, clazz};
    }

    private BizCompositionLogAddDTO convertToBizCompositionLogBO(JSONObject item, SysUserAddDTO sysUserAddDTO, Long userId) {
        BizCompositionLogAddDTO log = new BizCompositionLogAddDTO();
        Long initId = item.getLong("initId");
        Long mappingId = item.getLong("Id");
        if (ObjectUtil.equal(initId, 0L)) {
            initId = mappingId;
        }
        String key = userId + ":" + initId;
        COMPOSITION_LOG_MAP.computeIfAbsent(key, k -> IdUtil.getSnowflakeNextId());

        if (ObjectUtil.equal(initId, mappingId)) {
            log.setId(COMPOSITION_LOG_MAP.get(key));
        }else{
            log.setId(IdUtil.getSnowflakeNextId());
        }
        log.setTitle(item.getString("Title"));
        log.setRequireTitle(item.getString("TitleLimit"));
        log.setRequireWords(item.getInteger("WordCountLimit"));
        log.setRequireWenTi(item.getString("StyleLimit"));
        log.setWenTi(item.getString("Style"));
        log.setScore(item.getDouble("score"));
        log.setPercentage(item.getDouble("Percentage"));
        log.setComments(item.getString("Comments"));
        log.setParagraphs(item.getInteger("Paragraphcount"));
        log.setWords(item.getInteger("Wordcount"));
        log.setSentences(item.getInteger("Sentencecount"));
        log.setFullScore(item.getDouble("Sfullmarks"));
        log.setClassified(item.getString("Classified"));
        log.setSensitive(item.getString("Sensitive"));
        log.setStar(item.getInteger("Star"));
        log.setGrade(item.getString("Grade"));
        log.setInitLogId(COMPOSITION_LOG_MAP.get(key));
        log.setMappingId(mappingId);
        log.setSchoolId(sysUserAddDTO.getSchoolId());
        log.setSchoolName(sysUserAddDTO.getSchoolName());
        log.setSubmitTime(LocalDateTime.parse(item.getString("createTime"), FORMATTER));
        log.setCompositionId(compositionBatchCache.get(item.getLong("compositionId")));
        log.setSource("2");
        log.setEvaluateSources(item.getInteger("evaluateSources"));
        log.setAnswerStatus(item.getInteger("answerStatus"));
        log.setAuthor(item.getString("author"));
        if ("4".equals(sysUserAddDTO.getUserType())) {
            log.setClazzId(sysUserAddDTO.getClazzId());
            log.setClazzName(sysUserAddDTO.getClazzName());
            log.setGradeId(sysUserAddDTO.getGradeId());
            log.setGradeName(sysUserAddDTO.getGradeName());
            log.setAnswerStudentNum(sysUserAddDTO.getUserName());
        }

        log.setAnswerId(userId);
        log.setAnswerName(sysUserAddDTO.getRealName());

        log.setContentList(convertToList(item.getJSONArray("ContentList"), BizCompositionContent.class));
        log.setErrorList(convertToList(item.getJSONArray("ErrorList"), BizCompositionErrorLog.class));
        log.setJiList(convertToList(item.getJSONArray("JiList"), BizCompositionJi.class));
        log.setMeiList(convertToList(item.getJSONArray("MeiList"), BizCompositionMeipi.class));
        log.setCtList(convertToList(item.getJSONArray("CTList"), BizCompositionCt.class));
        log.setGoodWordsList(convertToList(item.getJSONArray("GoodWordsList"), BizCompositionGoodWords.class));

        return log;
    }

    private <T> List<T> convertToList(JSONArray array, Class<T> clazz) {
        if (array == null || array.isEmpty()) return Collections.emptyList();
        return array.toJavaList(clazz);
    }

    private BizCompositionReleaseAddDTO convertToBizCompositionReleaseBO(JSONObject item, Long schoolId, String schoolName) {
        BizCompositionReleaseAddDTO release = new BizCompositionReleaseAddDTO();
        release.setId(IdUtil.getSnowflakeNextId());
        Long mappingId = item.getLong("Id");
        compositionBatchCache.putIfAbsent(mappingId, release.getId());
        release.setMappingId(mappingId);
        release.setCompositionTitle(item.getString("compositionTitle"));
        release.setReleaseType(item.getInteger("releaseType"));
        release.setCompositionType(item.getInteger("compositionType"));
        release.setCompositionStatus(item.getInteger("compositionStatus"));
        release.setCompositionWordCount(item.getInteger("compositionWordCount"));
        release.setCompositionBeginTime(LocalDateTime.parse(item.getString("compositionBeginTime"), FORMATTER));
        release.setCompositionEndTime(LocalDateTime.parse(item.getString("compositionEndTime"), FORMATTER));
        release.setCompositionFullScore(item.getInteger("compositionFullScore"));
        release.setCompositionContent(item.getString("compositionContent"));
        release.setCompositionWenTi(item.getString("compositionWenTi"));
        release.setSchoolId(Convert.toStr(schoolId));
        release.setSchoolName(schoolName);
        String gradeName = item.getString("gradeName");
        Long gradeId = getOrSaveGrade(gradeName, schoolId, schoolName);
        release.setGradeId(Convert.toStr(gradeId));
        release.setGradeName(gradeName);

        List<BizCompositionReleaseExtend> bizCompositionReleaseExtends = convertToBizCompositionReleaseExtendBO(release, item.getString("clazzName"));
        release.setExtendList(bizCompositionReleaseExtends);

        return release;
    }

    private List<BizCompositionReleaseExtend> convertToBizCompositionReleaseExtendBO(BizCompositionReleaseAddDTO release, String clazzName) {

        String[] clazzNameArr = clazzName.split("，");
        if (clazzNameArr.length == 0) {
            return Collections.emptyList();
        }

        List<BizCompositionReleaseExtend> extendList = new ArrayList<>();
        for (String clazz : clazzNameArr) {
            String[] gradeAndClazz = matchGradeAndClazz(clazz);
            BizCompositionReleaseExtend extend = new BizCompositionReleaseExtend();
            extend.setCompositionId(release.getId());
            extend.setGradeId(release.getGradeId());
            extend.setGradeName(release.getGradeName());
            Long clazzId = getOrSaveClazz(gradeAndClazz[1], Convert.toLong(release.getSchoolId()), Convert.toLong(release.getGradeId()), release.getSchoolName());
            extend.setClazzId(Convert.toStr(clazzId));
            extend.setClazzName(gradeAndClazz[1]);
            extend.setSchoolId(release.getSchoolId());
            extend.setSchoolName(release.getSchoolName());
            extendList.add(extend);
        }
        return extendList;
    }
}