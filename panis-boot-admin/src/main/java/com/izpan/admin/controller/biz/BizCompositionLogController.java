/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.biz;

import cn.hutool.core.date.DateUtil;
import com.izpan.common.api.Result;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.biz.domain.dto.composition.log.*;
import com.izpan.modules.biz.domain.entity.BizCompositionLog;
import com.izpan.modules.biz.domain.vo.BizCompBatchEvaluateVO;
import com.izpan.modules.biz.domain.vo.BizCompositionHistoryLogVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogExportVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;
import com.izpan.modules.biz.facade.IBizCompositionLogFacade;
import com.izpan.admin.service.IBizWordExportService;
import com.izpan.starter.excel.util.ExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 作文记录表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.biz.BizCompositionLogController
 * @CreateTime 2025-03-23 - 12:45:30
 */

@RestController
@Tag(name = "作文记录表")
@RequiredArgsConstructor
@RequestMapping("biz_composition_log")
public class BizCompositionLogController {

    private static final Logger log = LoggerFactory.getLogger(BizCompositionLogController.class);

    private static final String ESSAY_TEMPLATE_PATH = "templates/essay_template.docx";
    private static final String CONTENT_TYPE_HTML = "text/html;charset=utf-8";
    private static final String CONTENT_TYPE_DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    private static final String CONTENT_DISPOSITION = "Content-Disposition";
    private static final String ATTACHMENT_FILENAME = "attachment; filename=";

    @NonNull
    private IBizCompositionLogFacade bizCompositionLogFacade;

    @NonNull
    private IBizWordExportService bizWordExportService;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取作文记录表列表")
    public Result<RPage<BizCompositionLogVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                   @Parameter(description = "查询对象") BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        return Result.data(bizCompositionLogFacade.listBizCompositionLogPage(pageQuery, bizCompositionLogSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取作文记录表详细信息")
    public Result<BizCompositionLogVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bizCompositionLogFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增测评作文记录")
    public Result<Long> add(@Parameter(description = "新增对象") @RequestBody BizCompositionLogAddDTO bizCompositionLogAddDTO) {
        return Result.data(bizCompositionLogFacade.add(bizCompositionLogAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新作文记录表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BizCompositionLogUpdateDTO bizCompositionLogUpdateDTO) {
        return Result.status(bizCompositionLogFacade.update(bizCompositionLogUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除作文记录表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BizCompositionLogDeleteDTO bizCompositionLogDeleteDTO) {
        return Result.status(bizCompositionLogFacade.batchDelete(bizCompositionLogDeleteDTO));
    }

    @GetMapping("/teacher_list")
    @Operation(operationId = "6", summary = "获取老师获取作文记录表列表")
    public Result<List<BizCompositionLog>> list(@Parameter(description = "查询对象") BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        return Result.data(bizCompositionLogFacade.list(bizCompositionLogSearchDTO));
    }

    /**
     * 统计某个班级下的作文提交情况
     */
    @GetMapping("/count_by_clazz")
    @Operation(operationId = "7", summary = "统计某个班级下的作文提交情况")
    public Result<Map<String, Object>> countByClazz(@Parameter(description = "查询对象") BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        return Result.data(bizCompositionLogFacade.countByClazz(bizCompositionLogSearchDTO));
    }

    /**
     * 导出作文记录表列表
     */
    @GetMapping("/export_excel")
    @Operation(operationId = "8", summary = "导出作文记录表列表")
    public void exportList(@Parameter(description = "查询对象") BizCompositionLogExportSearchDTO bizCompositionLogSearchDTO,
                           HttpServletResponse response) {
        // 直接获取导出VO列表，包含初始分数、当前分数和分数提高值
        List<BizCompositionLogExportVO> exportVOList = bizCompositionLogFacade.listForExport(bizCompositionLogSearchDTO);

        // 如果有数据，执行导出操作
        if (exportVOList != null && !exportVOList.isEmpty()) {
            // 计算统计信息
            int totalCount = exportVOList.size();  // 测评总篇数

            // 计算总修改次数
            int totalEditCount = exportVOList.stream()
                    .mapToInt(vo -> vo.getEvaluationCount() != null ? vo.getEvaluationCount() : 0)
                    .sum();

            // 计算单篇最高修改次数
            int maxEditCount = exportVOList.stream()
                    .mapToInt(vo -> vo.getEvaluationCount() != null ? vo.getEvaluationCount() : 0)
                    .max()
                    .orElse(0);

            // 计算单篇最高提分
            double maxImproveScore = exportVOList.stream()
                    .mapToDouble(vo -> vo.getImproveScore() != null ? vo.getImproveScore() : 0.0)
                    .max()
                    .orElse(0.0);

            // 计算全体学生平均提分
            double avgImproveScore = exportVOList.stream()
                    .mapToDouble(vo -> vo.getImproveScore() != null ? vo.getImproveScore() : 0.0)
                    .average()
                    .orElse(0.0);

            // 格式化统计摘要
            String summary = String.format("共测评:%d篇,总修改次数为%d次,单篇最高修改次数为%d次,单篇提分最高为%.2f分,全体学生平均提分%.2f分",
                    totalCount, totalEditCount, maxEditCount, maxImproveScore, avgImproveScore);

            // 创建统计行
            BizCompositionLogExportVO statsRow = new BizCompositionLogExportVO();
            statsRow.setTitle(summary); // 将统计信息放在标题字段中

            // 添加到导出列表末尾
            exportVOList.add(statsRow);

            BizCompositionLogSearchDTO searchDTO = CglibUtil.convertObj(bizCompositionLogSearchDTO, BizCompositionLogSearchDTO::new);

            BizCompositionLog firstItem = bizCompositionLogFacade.list(searchDTO)
                    .stream()
                    .findFirst()
                    .orElse(null);

            String fileName = "作文记录导出";
            if (firstItem != null) {
                fileName = firstItem.getRequireTitle() + firstItem.getGradeName() + firstItem.getClazzName();
            }

            ExcelUtil.export(BizCompositionLogExportVO.class)
                    .fileName(fileName)
                    .data(exportVOList)
                    .toResponse(response);
        } else {
            // 无数据时，提供一个空的Excel文件
            ExcelUtil.export(BizCompositionLogExportVO.class)
                    .fileName("作文记录导出")
                    .data(exportVOList)
                    .toResponse(response);
        }
    }

    @GetMapping("/batch_evaluate_page")
    @Operation(operationId = "9", summary = "批量测评记录")
    public Result<RPage<BizCompBatchEvaluateVO>> batchEvaluatePage(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                                   @Parameter(description = "查询对象") BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        return Result.data(bizCompositionLogFacade.listBatchEvaluatePage(pageQuery, bizCompositionLogSearchDTO));
    }

    @PostMapping("/match_student_and_composition")
    @Operation(operationId = "10", summary = "手动匹配 学生 和 作文")
    public Result<Boolean> matchStudentAndComposition(@RequestBody BizCompositionLogMatchStudentDTO bizCompositionLogMatchStudentDTO) {
        return Result.status(bizCompositionLogFacade.matchStudentAndComposition(bizCompositionLogMatchStudentDTO));
    }


    @GetMapping("/user_page")
    @Operation(operationId = "11", summary = "获取登录者的作文记录表列表-篇")
    public Result<RPage<BizCompositionHistoryLogVO>> userPage(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                              @Parameter(description = "查询对象") BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        return Result.data(bizCompositionLogFacade.userPage(pageQuery, bizCompositionLogSearchDTO));
    }

    @PostMapping("/user_list")
    @Operation(operationId = "12", summary = "获取登录者的作文记录表列表-次")
    public Result<List<BizCompositionHistoryLogVO>> userList(@RequestBody BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        return Result.data(bizCompositionLogFacade.userList(bizCompositionLogSearchDTO));
    }

    @GetMapping("/export_word/{id}")
    @Operation(operationId = "13", summary = "导出作文为Word文档")
    public void exportWord(@Parameter(description = "作文记录ID") @PathVariable("id") Long id,
                           HttpServletResponse response) {
        log.info("开始导出作文Word文档，ID: {}", id);
        try {
            // 先获取文件名，准备响应头
            String fileName = bizWordExportService.getCompositionFileName(id);
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            // 在写入数据之前设置响应头
            response.setContentType(CONTENT_TYPE_DOCX);
            response.setHeader(CONTENT_DISPOSITION, ATTACHMENT_FILENAME + encodedFileName);

            // 导出数据到响应流
            bizWordExportService.exportSingleComposition(id, response.getOutputStream());

            log.info("成功导出作文Word文档，ID: {}, 文件名: {}", id, fileName);

        } catch (IOException e) {
            log.error("导出Word文档时发生IO错误: {}", e.getMessage(), e);
            try {
                response.setContentType(CONTENT_TYPE_HTML);
                response.getWriter().write("导出Word文档时发生错误: " + e.getMessage());
            } catch (IOException ex) {
                log.error("无法写入错误响应: {}", ex.getMessage());
            }
        } catch (Exception e) {
            log.error("导出Word文档时发生错误: {}", e.getMessage(), e);
            try {
                response.setContentType(CONTENT_TYPE_HTML);
                response.getWriter().write("导出Word文档时发生错误: " + e.getMessage());
            } catch (IOException ex) {
                log.error("无法写入错误响应: {}", ex.getMessage());
            }
        }
    }

    @PostMapping("/export_word_batch")
    @Operation(operationId = "14", summary = "批量导出作文为Word压缩包")
    public void exportWordBatch(@Parameter(description = "批量导出对象") @RequestBody @Valid BizCompositionLogBatchExportDTO batchExportDTO,
                                HttpServletResponse response) {
        log.info("开始批量导出作文Word文档，数量: {}", batchExportDTO.getCompositionIds().size());

        try {
            // 使用Word导出服务进行批量导出
            byte[] zipBytes = bizWordExportService.exportCompositionBatch(
                    batchExportDTO.getCompositionIds(),
                    batchExportDTO.getZipFileName()
            );

            // 生成压缩包文件名
            String zipFileName = batchExportDTO.getZipFileName();
            if (zipFileName == null || zipFileName.trim().isEmpty()) {
                zipFileName = "作文批量导出_" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd_HHmmss") + ".zip";
            } else if (!zipFileName.endsWith(".zip")) {
                zipFileName += ".zip";
            }

            // 设置响应头并输出文件
            String encodedFileName = URLEncoder.encode(zipFileName, StandardCharsets.UTF_8);
            response.setContentType("application/zip");
            response.setHeader(CONTENT_DISPOSITION, ATTACHMENT_FILENAME + encodedFileName);
            response.addHeader("Content-Length", String.valueOf(zipBytes.length));

            response.getOutputStream().write(zipBytes);
            response.getOutputStream().flush();

            log.info("成功批量导出作文Word文档，文件名: {}, 大小: {} bytes", zipFileName, zipBytes.length);

        } catch (IOException e) {
            log.error("批量导出Word文档时发生IO错误: {}", e.getMessage(), e);
            try {
                response.setContentType(CONTENT_TYPE_HTML);
                response.getWriter().write("批量导出Word文档时发生错误: " + e.getMessage());
            } catch (IOException ex) {
                log.error("无法写入错误响应: {}", ex.getMessage());
            }
        } catch (Exception e) {
            log.error("批量导出Word文档时发生错误: {}", e.getMessage(), e);
            try {
                response.setContentType(CONTENT_TYPE_HTML);
                response.getWriter().write("批量导出Word文档时发生错误: " + e.getMessage());
            } catch (IOException ex) {
                log.error("无法写入错误响应: {}", ex.getMessage());
            }
        }
    }

//    /**
//     * 获取作文评测统计信息
//     */
//    @GetMapping("/statistics")
//    @Operation(operationId = "14", summary = "获取作文评测统计信息")
//    public Result<BizCompositionStatsVO> getStatistics(@Parameter(description = "查询对象") BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
//        // 复用现有逻辑获取导出数据列表
//        List<BizCompositionLogExportVO> exportVOList = bizCompositionLogFacade.listForExport(bizCompositionLogSearchDTO);
//
//        // 如果没有数据，返回空统计信息
//        if (exportVOList == null || exportVOList.isEmpty()) {
//            return Result.data(BizCompositionStatsVO.builder()
//                    .totalCount(0)
//                    .totalEditCount(0)
//                    .maxEditCount(0)
//                    .maxImproveScore(0.0)
//                    .avgImproveScore(0.0)
//                    .summary("没有找到符合条件的评测数据")
//                    .build());
//        }
//
//        // 计算统计信息
//        int totalCount = exportVOList.size();  // 测评总篇数
//
//        // 计算总修改次数
//        int totalEditCount = exportVOList.stream()
//                .mapToInt(vo -> vo.getEvaluationCount() != null ? vo.getEvaluationCount() : 0)
//                .sum();
//
//        // 计算单篇最高修改次数
//        int maxEditCount = exportVOList.stream()
//                .mapToInt(vo -> vo.getEvaluationCount() != null ? vo.getEvaluationCount() : 0)
//                .max()
//                .orElse(0);
//
//        // 计算单篇最高提分
//        double maxImproveScore = exportVOList.stream()
//                .mapToDouble(vo -> vo.getImproveScore() != null ? vo.getImproveScore() : 0.0)
//                .max()
//                .orElse(0.0);
//
//        // 计算全体学生平均提分
//        double avgImproveScore = exportVOList.stream()
//                .mapToDouble(vo -> vo.getImproveScore() != null ? vo.getImproveScore() : 0.0)
//                .average()
//                .orElse(0.0);
//
//        // 格式化统计摘要
//        String summary = String.format("共测评:%d篇,总修改次数为%d次,单篇最高修改次数为%d次,单篇提分最高为%.2f分,全体学生平均提分%.2f分",
//                totalCount, totalEditCount, maxEditCount, maxImproveScore, avgImproveScore);
//
//        // 构建并返回统计VO对象
//        BizCompositionStatsVO statsVO = BizCompositionStatsVO.builder()
//                .totalCount(totalCount)
//                .totalEditCount(totalEditCount)
//                .maxEditCount(maxEditCount)
//                .maxImproveScore(maxImproveScore)
//                .avgImproveScore(avgImproveScore)
//                .summary(summary)
//                .build();
//
//        return Result.data(statsVO);
//    }

}