package com.izpan.admin.controller.tools;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.izpan.common.api.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 代理控制器 - 用于解决跨域问题
 *
 * <AUTHOR>
 * @CreateTime 2024-03-02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/proxy")
@Tag(name = "代理控制器", description = "用于解决跨域问题的代理接口")
public class ProxyController {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${ceping.ocr}")
    private String ocrUrl;

    @Value("${ceping.cp}")
    private String cpUrl;

    @Value("${ceping.fw}")
    private String fwUrl;

    @Value("${ceping.sc}")
    private String scUrl;

    @Value("${ceping.dsc}")
    private String dscUrl;

    @Value("${ceping.pocr}")
    private String pOcrUrl;

    @Value("${ceping.pcp}")
    private String pCpUrl;

    /**
     * OCR识别代理接口
     *
     * @param requestBody 请求体
     * @return 代理响应结果
     */
    @PostMapping("/ocr")
    @Operation(summary = "OCR识别代理接口")
    public Result<Object> proxyOcrRequest(@RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            // 将Map转换为JSON字符串
            String jsonString = objectMapper.writeValueAsString(requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.add("Accept", "application/json");

            // 创建请求实体，使用JSON字符串
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonString, headers);

            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    ocrUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理OCR请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

    /**
     * 测评提交代理接口
     *
     * @param requestBody 请求体
     * @return 代理响应结果
     */
    @PostMapping("/ceping")
    @Operation(summary = "测评提交代理接口")
    public Result<Object> proxyCepingRequest(@RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            // 将Map转换为JSON字符串
            String jsonString = objectMapper.writeValueAsString(requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.add("Accept", "application/json");

            // 创建请求实体，使用JSON字符串
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonString, headers);

            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    cpUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理测评请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

     @PostMapping("/pocr")
    @Operation(summary = "OCR识别代理接口-批量测评")
    public Result<Object> proxyPOcrRequest(@RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            // 将Map转换为JSON字符串
            String jsonString = objectMapper.writeValueAsString(requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.add("Accept", "application/json");

            // 创建请求实体，使用JSON字符串
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonString, headers);

            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    pOcrUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理OCR请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

    @PostMapping("/pcp")
    @Operation(summary = "测评提交代理接口-批量测评")
    public Result<Object> proxyPCepingRequest(@RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            // 将Map转换为JSON字符串
            String jsonString = objectMapper.writeValueAsString(requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.add("Accept", "application/json");

            // 创建请求实体，使用JSON字符串
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonString, headers);

            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    pCpUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理测评提交请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

    /**
     * 获取范文
     */
    @GetMapping("/fw")
    @Operation(summary = "获取范文")
    public Result<Object> proxyFwRequest(@RequestParam(required = false) Map<String, String> params) {
       try {
            // 构建请求URL (带查询参数)
            StringBuilder urlBuilder = new StringBuilder(fwUrl);
            if (!params.isEmpty()) {
                urlBuilder.append("?");
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
                urlBuilder.deleteCharAt(urlBuilder.length() - 1);
            }
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Accept", "application/json");
            
            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            
            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    urlBuilder.toString(),
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );

            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理范文请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

    /**
     * 获取素材
     */
    @GetMapping("/sc")
    @Operation(summary = "获取素材")
    public Result<Object> proxyScRequest(@RequestParam(required = false) Map<String, String> params) {
        try {
            // 构建请求URL (带查询参数)
            StringBuilder urlBuilder = new StringBuilder(scUrl);
            if (!params.isEmpty()) {
                urlBuilder.append("?");
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
                urlBuilder.deleteCharAt(urlBuilder.length() - 1);
            }
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Accept", "application/json");
            
            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            
            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    urlBuilder.toString(),
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );
            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理素材请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

    /**
     * 获取素材详情
     */
    @GetMapping("/dsc")
    @Operation(summary = "获取素材详情")
    public Result<Object> proxyDscRequest(@RequestParam(required = false) Map<String, String> params) {
        try {
            // 构建请求URL (带查询参数)
            StringBuilder urlBuilder = new StringBuilder(dscUrl);
            if (!params.isEmpty()) {
                urlBuilder.append("?");
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
                urlBuilder.deleteCharAt(urlBuilder.length() - 1);
            }
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Accept", "application/json");
            
            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            
            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                    urlBuilder.toString(),
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );
            return Result.data(response.getBody());
        } catch (Exception e) {
            log.error("代理素材详情请求失败: {}", e.getMessage());
            return Result.failure("代理请求失败: " + e.getMessage());
        }
    }

    /**
     * 处理预检请求
     */
    @RequestMapping(value = {"/**"}, method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Access-Control-Allow-Origin", "*");
        headers.add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        headers.add("Access-Control-Allow-Headers", "Content-Type, Authorization");
        headers.add("Access-Control-Max-Age", "3600");

        return ResponseEntity
                .status(HttpStatus.OK)
                .headers(headers)
                .build();
    }

} 