/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanAddDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanDeleteDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanSearchDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanUpdateDTO;
import com.izpan.modules.base.domain.vo.BseDanyuanVO;
import com.izpan.modules.base.facade.IBseDanyuanFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import java.util.List;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 单元教学信息表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BseDanyuanController
 * @CreateTime 2025-03-25 - 20:45:00
 */

@RestController
@Tag(name = "单元教学信息表")
@RequiredArgsConstructor
@RequestMapping("bse_danyuan")
public class BseDanyuanController {

    @NonNull
    private IBseDanyuanFacade bseDanyuanFacade;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取单元教学信息表列表")
    public Result<RPage<BseDanyuanVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                            @Parameter(description = "查询对象") BseDanyuanSearchDTO bseDanyuanSearchDTO) {
        return Result.data(bseDanyuanFacade.listBseDanyuanPage(pageQuery, bseDanyuanSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取单元教学信息表详细信息")
    public Result<BseDanyuanVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bseDanyuanFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增单元教学信息表")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BseDanyuanAddDTO bseDanyuanAddDTO) {
        return Result.status(bseDanyuanFacade.add(bseDanyuanAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新单元教学信息表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BseDanyuanUpdateDTO bseDanyuanUpdateDTO) {
        return Result.status(bseDanyuanFacade.update(bseDanyuanUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除单元教学信息表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BseDanyuanDeleteDTO bseDanyuanDeleteDTO) {
        return Result.status(bseDanyuanFacade.batchDelete(bseDanyuanDeleteDTO));
    }

    @PostMapping("/list")
    @Operation(operationId = "6", summary = "获取单元教学信息表列表")
    public Result<List<BseDanyuanVO>> getList(@Parameter(description = "查询对象") @RequestBody BseDanyuanSearchDTO bseDanyuanSearchDTO) {
        return Result.data(bseDanyuanFacade.list(bseDanyuanSearchDTO));
    }

}