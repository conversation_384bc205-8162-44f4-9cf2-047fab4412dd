/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.school.*;
import com.izpan.modules.base.domain.entity.AreaInfo;
import com.izpan.modules.base.domain.entity.BseSchool;
import com.izpan.modules.base.domain.entity.CityInfo;
import com.izpan.modules.base.domain.entity.ProvinceInfo;
import com.izpan.modules.base.domain.vo.BseSchoolInfoVO;
import com.izpan.modules.base.domain.vo.BseSchoolVO;
import com.izpan.modules.base.facade.IBseSchoolFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学校管理 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BseSchoolController
 * @CreateTime 2024-12-05 - 22:49:40
 */

@RestController
@Tag(name = "学校管理")
@RequiredArgsConstructor
@RequestMapping("bse_school")
public class BseSchoolController {

    @NonNull
    private IBseSchoolFacade bseSchoolFacade;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取学校管理列表")
    public Result<RPage<BseSchoolVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                           @Parameter(description = "查询对象") BseSchoolSearchDTO bseSchoolSearchDTO) {
        return Result.data(bseSchoolFacade.listBseSchoolPage(pageQuery, bseSchoolSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取学校管理详细信息")
    public Result<BseSchoolInfoVO> get(@Parameter(description = "ID") @PathVariable("id") Long id,
                                       @Parameter(description = "类型") @RequestParam(value = "type", defaultValue = "2") String type) {
        return Result.data(bseSchoolFacade.get(id, type));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增学校管理")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BseSchoolAddDTO bseSchoolAddDTO) {
        bseSchoolFacade.add(bseSchoolAddDTO);
        return Result.status(true);
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新学校管理信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BseSchoolUpdateDTO bseSchoolUpdateDTO) {
        return Result.status(bseSchoolFacade.update(bseSchoolUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除学校管理信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BseSchoolDeleteDTO bseSchoolDeleteDTO) {
        return Result.status(bseSchoolFacade.batchDelete(bseSchoolDeleteDTO));
    }

    @GetMapping("/getCityList")
    @Operation(operationId = "6", summary = "获取城市列表")
    public Result<List<CityInfo>> getCityList(@Parameter(description = "省份编码") String provinceCode, @Parameter(description = "父级ID") Long parentId) {
        return Result.data(bseSchoolFacade.getCityList(provinceCode, parentId));
    }

    @GetMapping("/getProvinceList")
    @Operation(operationId = "7", summary = "获取所属省列表")
    public Result<List<ProvinceInfo>> getProvinceList(@Parameter(description = "父级ID") Long parentId) {
        return Result.data(bseSchoolFacade.getProvinceList(parentId));
    }

    @GetMapping("/getAreaList")
    @Operation(operationId = "8", summary = "获取区域列表")
    public Result<List<AreaInfo>> getAreaList(@Parameter(description = "城市编码") String cityCode, @Parameter(description = "父级ID") Long parentId) {
        return Result.data(bseSchoolFacade.getAreaList(cityCode, parentId));
    }

    @GetMapping("/getSchoolListByArea")
    @Operation(operationId = "9", summary = "获取区域学校列表")
    public Result<List<BseSchool>> getSchoolListByArea(@Parameter(description = "区域编码") String areaCode, @Parameter(description = "父级ID") Long parentId) {
        return Result.data(bseSchoolFacade.getSchoolListByArea(areaCode, parentId));
    }

    @GetMapping("/getSchoolLists")
    @Operation(operationId = "10", summary = "获取学校集合")
    public Result<List<BseSchool>> getSchoolLists(@Parameter(description = "学校名称") String schoolName) {
        return Result.data(bseSchoolFacade.getSchoolLists(schoolName));
    }

    @GetMapping("/getSchoolNumberInfo")
    @Operation(operationId = "11", summary = "获取当前登录用户所在学校的微课次数和测评次数")
    public Result<BseSchoolVO> getSchoolNumberInfo() {
        return Result.data(bseSchoolFacade.getSchoolNumberInfo());
    }

    @PostMapping("/batch_assign_counts")
    @Operation(operationId = "12", summary = "批量分配次数", 
               description = "支持学校给用户分配(type=1)和供应商给学校分配(type=2)两种模式。" +
                          "当type=2时，可设置needValidate参数控制是否校验供应商的次数是否足够。")
    public Result<Boolean> batchAssignCounts(@Parameter(description = "分配参数") @RequestBody BseSchoolBatchAssignCountsDTO dto) {
        return Result.status(bseSchoolFacade.batchAssignCounts(dto));
    }

}