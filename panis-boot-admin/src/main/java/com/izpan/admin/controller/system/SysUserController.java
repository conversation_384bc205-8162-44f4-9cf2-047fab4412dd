package com.izpan.admin.controller.system;

import com.izpan.common.api.Result;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.infrastructure.util.GsonUtil;
import com.izpan.modules.system.domain.dto.user.*;
import com.izpan.modules.system.domain.vo.*;
import com.izpan.starter.excel.listener.DataListener;
import com.izpan.starter.excel.util.ExcelUtil;
import com.izpan.modules.system.facade.ISysUserFacade;
import com.izpan.modules.system.service.ISysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 系统管理 - 用户管理 Controller 控制层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.system.SysUserController
 * @CreateTime 2023/7/6 - 14:25
 */

@Slf4j
@RestController
@Tag(name = "用户管理")
@RequiredArgsConstructor
@RequestMapping("/sys_user")
public class SysUserController {

    @NonNull
    private ISysUserFacade sysUserFacade;

    @NonNull
    private ISysUserService sysUserService;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取用户管理列表")
    public Result<RPage<SysUserVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                         @Parameter(description = "查询对象") SysUserSearchDTO sysUserSearchDTO) {
        return Result.data(sysUserFacade.listSysUserPage(pageQuery, sysUserSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取用户详细信息")
    public Result<SysUserVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(sysUserFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增用户")
    public Result<Boolean> addUser(@Parameter(description = "新增用户对象") @RequestBody SysUserAddDTO sysUserAddDTO) {
        sysUserFacade.addUser(sysUserAddDTO);
        return Result.status(true);
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新用户信息")
    public Result<Boolean> updateUser(@Parameter(description = "更新用户对象") @RequestBody SysUserUpdateDTO sysUserUpdateDTO) {
        return Result.status(sysUserFacade.updateUser(sysUserUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除用户信息")
    public Result<Boolean> batchDeleteUser(@Parameter(description = "删除用户对象") @RequestBody SysUserDeleteDTO sysUserDeleteDTO) {
        return Result.status(sysUserFacade.batchDeleteUser(sysUserDeleteDTO));
    }

    @PutMapping("/reset_password/{userId}")
    @Operation(operationId = "6", summary = "重置密码")
    public Result<String> resetPassword(@Parameter(description = "用户ID") @PathVariable("userId") Long userId) {
        return Result.data(sysUserFacade.resetPassword(userId));
    }

    @GetMapping("/responsibilities/{userId}")
    @Operation(operationId = "7", summary = "根据用户ID获取用户职责信息")
    public Result<SysUserResponsibilitiesVO> queryUserResponsibilities(@Parameter(description = "ID") @PathVariable("userId") Long userId) {
        return Result.data(sysUserFacade.queryUserResponsibilitiesWithUserId(userId));
    }

    @PutMapping("/responsibilities")
    @Operation(operationId = "8", summary = "更新用户职责信息")
    public Result<Boolean> updateUserResponsibilities(@Parameter(description = "用户职责对象") @RequestBody SysUserResponsibilitiesUpdateDTO updateDTO) {
        return Result.data(sysUserFacade.updateUserResponsibilities(updateDTO));
    }
    
    @GetMapping("/download/template")
    @Operation(operationId = "11", summary = "下载学生信息导入模板")
    @SneakyThrows
    public void downloadTemplate(HttpServletResponse response) {
        ExcelUtil.export(UserImportTemplateVO.class).toResponse(response);
    }

    @PostMapping("/import")
    @Operation(operationId = "12", summary = "导入用户信息")
    @SneakyThrows
    public Result<ImportResult> importUsers(
            @Parameter(description = "导入文件") @RequestParam("file") MultipartFile file) {
        DataListener<UserImportTemplateVO> listener = new DataListener<>();
        ExcelUtil.read(UserImportTemplateVO.class)
                .listener(listener)
                .fromInputStream(file.getInputStream());

        log.info("导入的数据: {}", GsonUtil.toJson(listener.getRows()));
        ImportResult result = sysUserFacade.importUsers(listener.getRows());
        return Result.data(result);
    }

    /**
     * 修改密码
     * @param passwordData 密码数据
     * @return 修改结果
     */
    @PostMapping("/changePassword")
    @Operation(operationId = "13", summary = "修改密码")
    public Result<Boolean> changePassword(@RequestBody Map<String, String> passwordData) {
        try {
            // 从请求中获取信息
            String userName = passwordData.get("userName");
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");

            // 验证必填字段
            if (userName == null || userName.trim().isEmpty()) {
                return Result.failure("账号不能为空");
            }
            if (oldPassword == null || oldPassword.trim().isEmpty()) {
                return Result.failure("原密码不能为空");
            }
            if (newPassword == null || newPassword.trim().isEmpty()) {
                return Result.failure("新密码不能为空");
            }

            // 验证用户是否存在
            SysUserVO existingUser = sysUserService.getUserByUserName(userName);
            if (existingUser == null) {
                return Result.failure("用户不存在");
            }

            // 调用服务修改密码
            boolean result = sysUserService.changePassword(userName, oldPassword, newPassword);

            if (result) {
                return Result.success("密码修改成功");
            } else {
                return Result.failure("原密码不正确");
            }
        } catch (Exception e) {
            return Result.failure("修改密码失败：" + e.getMessage());
        }
    }

    /**
     * 移除用户：将学生变为用户、移出班级学校
     */
    @PostMapping("/remove_student")
    @Operation(operationId = "14", summary = "移除用户")
    public Result<Boolean> removeUser(@RequestBody SysUserDeleteDTO ids) {
        return Result.data(sysUserFacade.removeUser(ids));
    }

    /**
     * 更新次数接口
     */
    @PostMapping("/updateTimes")
    @Operation(operationId = "15", summary = "更新次数")
    public Result<Integer> updateTimes(@RequestBody SysUserUpdateNumberDto dto) {
        return Result.data(sysUserFacade.updateTimes(dto));
    }

    /**
     * 根据年级、班级查询学生或老师
     */
    @PostMapping("/queryByGradeAndClass")
    @Operation(operationId = "16", summary = "根据年级、班级查询学生或老师")
    public Result<List<EvaluationUserVO>> queryByGradeAndClass(@RequestBody EvaluationUserSearchDTO sysUserSearchDTO) {
        return Result.data(sysUserFacade.queryByGradeAndClass(sysUserSearchDTO));
    }

    /**
     * 批量更新学生年级班级信息
     */
    @PostMapping("/batchUpdateGradeClass")
    @Operation(operationId = "17", summary = "批量更新学生年级班级信息")
    public Result<Boolean> batchUpdateGradeClass(@Parameter(description = "批量更新年级班级对象") @RequestBody SysUserBatchUpdateGradeClassDTO batchUpdateDTO) {
        return Result.data(sysUserFacade.batchUpdateGradeClass(batchUpdateDTO));
    }

    /**
     * 用户注册
     * @param registerDTO 注册数据
     * @return 注册结果
     */
    @PostMapping("/register")
    @Operation(operationId = "18", summary = "用户注册")
    public Result<Boolean> register(@Parameter(description = "用户注册对象") @RequestBody @Valid SysUserRegisterDTO registerDTO) {
        try {
            SysUserAddDTO userAddDTO = CglibUtil.convertObj(registerDTO, SysUserAddDTO::new);

            Long userId = sysUserFacade.addUser(userAddDTO);

            return Result.status(userId != null);
        } catch (Exception e) {
            return Result.failure("注册失败：" + e.getMessage());
        }
    }

}
