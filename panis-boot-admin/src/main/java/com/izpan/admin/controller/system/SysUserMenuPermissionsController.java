/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsAddDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsDeleteDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsSearchDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsUpdateDTO;
import com.izpan.modules.system.domain.vo.SysUserMenuPermissionsVO;
import com.izpan.modules.system.facade.ISysUserMenuPermissionsFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 用户菜单权限表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.system.SysUserMenuPermissionsController
 * @CreateTime 2024-12-13 - 22:21:55
 */

@RestController
@Tag(name = "用户菜单权限表")
@RequiredArgsConstructor
@RequestMapping("sys_user_menu_permissions")
public class SysUserMenuPermissionsController {

    @NonNull
    private ISysUserMenuPermissionsFacade sysUserMenuPermissionsFacade;

    @GetMapping("/page")
    @SaCheckPermission("sys:user:menu:permissions:page")
    @Operation(operationId = "1", summary = "获取用户菜单权限表列表")
    public Result<RPage<SysUserMenuPermissionsVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                        @Parameter(description = "查询对象") SysUserMenuPermissionsSearchDTO sysUserMenuPermissionsSearchDTO) {
        return Result.data(sysUserMenuPermissionsFacade.listSysUserMenuPermissionsPage(pageQuery, sysUserMenuPermissionsSearchDTO));
    }

    @GetMapping("/{id}")
    @SaCheckPermission("sys:user:menu:permissions:get")
    @Operation(operationId = "2", summary = "根据ID获取用户菜单权限表详细信息")
    public Result<SysUserMenuPermissionsVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(sysUserMenuPermissionsFacade.get(id));
    }

    @PostMapping("/")
    @SaCheckPermission("sys:user:menu:permissions:add")
    @Operation(operationId = "3", summary = "新增用户菜单权限表")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody SysUserMenuPermissionsAddDTO sysUserMenuPermissionsAddDTO) {
        return Result.status(sysUserMenuPermissionsFacade.add(sysUserMenuPermissionsAddDTO));
    }

    @PutMapping("/")
    @SaCheckPermission("sys:user:menu:permissions:update")
    @Operation(operationId = "4", summary = "更新用户菜单权限表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody SysUserMenuPermissionsUpdateDTO sysUserMenuPermissionsUpdateDTO) {
        return Result.status(sysUserMenuPermissionsFacade.update(sysUserMenuPermissionsUpdateDTO));
    }

    @DeleteMapping("/")
    @SaCheckPermission("sys:user:menu:permissions:delete")
    @Operation(operationId = "5", summary = "批量删除用户菜单权限表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody SysUserMenuPermissionsDeleteDTO sysUserMenuPermissionsDeleteDTO) {
        return Result.status(sysUserMenuPermissionsFacade.batchDelete(sysUserMenuPermissionsDeleteDTO));
    }

    @PostMapping("/batchAdd")
    @SaCheckPermission("sys:user:menu:permissions:batchAdd")
    @Operation(operationId = "6", summary = "新增用户菜单权限表")
    public Result<Boolean> batchAdd(@Parameter(description = "新增对象") @RequestBody List<SysUserMenuPermissionsAddDTO> sysUserMenuPermissionsAddDTOs) {
        return Result.status(sysUserMenuPermissionsFacade.batchAdd(sysUserMenuPermissionsAddDTOs));
    }

    @PostMapping("/getUserPermission/{userId}")
    @SaCheckPermission("sys:user:menu:permissions:getUserPermission")
    @Operation(operationId = "7", summary = "获取用户权限信息")
    public Result<List<SysUserMenuPermissionsVO>> getUserPermission(@Parameter(description = "userId") @PathVariable("userId") Long userId) {
        return Result.data(sysUserMenuPermissionsFacade.getByUserId(userId));
    }
}