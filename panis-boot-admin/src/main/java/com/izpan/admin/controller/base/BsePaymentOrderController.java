/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderAddDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderDeleteDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderSearchDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderUpdateDTO;
import com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO;
import com.izpan.modules.base.domain.dto.payment.WechatRefundDTO;
import com.izpan.modules.base.domain.vo.BsePaymentOrderVO;
import com.izpan.modules.base.domain.vo.WechatPayResultVO;
import com.izpan.modules.base.facade.IBsePaymentOrderFacade;
import com.izpan.modules.base.service.IWechatPayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

/**
 * 基础服务-支付与退款订单表 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BsePaymentOrderController
 * @CreateTime 2025-07-20 - 18:05:44
 */

@RestController
@Tag(name = "基础服务-支付与退款订单表")
@RequiredArgsConstructor
@RequestMapping("bse_payment_order")
public class BsePaymentOrderController {

    @NonNull
    private IBsePaymentOrderFacade bsePaymentOrderFacade;

    @NonNull
    private IWechatPayService wechatPayService;

    @GetMapping("/page")
    @Operation(operationId = "1", summary = "获取基础服务-支付与退款订单表列表")
    public Result<RPage<BsePaymentOrderVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                                 @Parameter(description = "查询对象") BsePaymentOrderSearchDTO bsePaymentOrderSearchDTO) {
        return Result.data(bsePaymentOrderFacade.listBsePaymentOrderPage(pageQuery, bsePaymentOrderSearchDTO));
    }

    @GetMapping("/{id}")
    @Operation(operationId = "2", summary = "根据ID获取基础服务-支付与退款订单表详细信息")
    public Result<BsePaymentOrderVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bsePaymentOrderFacade.get(id));
    }

    @PostMapping("/")
    @Operation(operationId = "3", summary = "新增基础服务-支付与退款订单表")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BsePaymentOrderAddDTO bsePaymentOrderAddDTO) {
        return Result.status(bsePaymentOrderFacade.add(bsePaymentOrderAddDTO));
    }

    @PutMapping("/")
    @Operation(operationId = "4", summary = "更新基础服务-支付与退款订单表信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BsePaymentOrderUpdateDTO bsePaymentOrderUpdateDTO) {
        return Result.status(bsePaymentOrderFacade.update(bsePaymentOrderUpdateDTO));
    }

    @DeleteMapping("/")
    @Operation(operationId = "5", summary = "批量删除基础服务-支付与退款订单表信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BsePaymentOrderDeleteDTO bsePaymentOrderDeleteDTO) {
        return Result.status(bsePaymentOrderFacade.batchDelete(bsePaymentOrderDeleteDTO));
    }

    // ==================== 微信支付相关接口 ====================

    @PostMapping("/wechat/create")
    @Operation(operationId = "6", summary = "创建微信支付订单")
    public Result<WechatPayResultVO> createWechatPayOrder(@Parameter(description = "微信支付订单对象") @RequestBody @Valid WechatPayOrderDTO wechatPayOrderDTO) {
        WechatPayResultVO result = wechatPayService.createPayOrder(wechatPayOrderDTO);
        return Result.data(result);
    }

    @GetMapping("/wechat/status/{orderNo}")
    @Operation(operationId = "7", summary = "查询微信支付订单状态")
    public Result<String> queryWechatPayStatus(@Parameter(description = "订单号") @PathVariable("orderNo") String orderNo) {
        String status = wechatPayService.queryOrderStatus(orderNo);
        return Result.data(status);
    }

    @PostMapping("/wechat/refund")
    @Operation(operationId = "8", summary = "申请微信支付退款")
    public Result<Boolean> applyWechatRefund(@Parameter(description = "退款对象") @RequestBody @Valid WechatRefundDTO wechatRefundDTO) {
        boolean result = wechatPayService.applyRefund(wechatRefundDTO);
        return Result.status(result);
    }

    @GetMapping("/wechat/refund/status/{refundNo}")
    @Operation(operationId = "9", summary = "查询微信退款状态")
    public Result<String> queryWechatRefundStatus(@Parameter(description = "退款单号") @PathVariable("refundNo") String refundNo) {
        String status = wechatPayService.queryRefundStatus(refundNo);
        return Result.data(status);
    }

}