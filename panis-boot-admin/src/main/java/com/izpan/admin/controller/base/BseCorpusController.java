/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.izpan.common.api.Result;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusAddDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusDeleteDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusSearchDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusUpdateDTO;
import com.izpan.modules.base.domain.vo.BseCorpusVO;
import com.izpan.modules.base.facade.IBseCorpusFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import lombok.NonNull;
import jakarta.validation.Valid;

/**
 *  Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.BseCorpusController
 * @CreateTime 2024-12-14 - 14:49:29
 */

@RestController
@Tag(name = "语料管理")
@RequiredArgsConstructor
@RequestMapping("bse_corpus")
public class BseCorpusController {

    @NonNull
    private IBseCorpusFacade bseCorpusFacade;

    @GetMapping("/page")
//    @SaCheckPermission("bse:corpus:page")
    @Operation(operationId = "1", summary = "获取列表")
    public Result<RPage<BseCorpusVO>> page(@Parameter(description = "分页对象", required = true) @Valid PageQuery pageQuery,
                                           @Parameter(description = "查询对象") BseCorpusSearchDTO bseCorpusSearchDTO) {
        return Result.data(bseCorpusFacade.listBseCorpusPage(pageQuery, bseCorpusSearchDTO));
    }

    @GetMapping("/{id}")
//    @SaCheckPermission("bse:corpus:get")
    @Operation(operationId = "2", summary = "根据ID获取详细信息")
    public Result<BseCorpusVO> get(@Parameter(description = "ID") @PathVariable("id") Long id) {
        return Result.data(bseCorpusFacade.get(id));
    }

    @PostMapping("/")
//    @SaCheckPermission("bse:corpus:add")
    @Operation(operationId = "3", summary = "新增")
    public Result<Boolean> add(@Parameter(description = "新增对象") @RequestBody BseCorpusAddDTO bseCorpusAddDTO) {
        return Result.status(bseCorpusFacade.add(bseCorpusAddDTO));
    }

    @PutMapping("/")
//    @SaCheckPermission("bse:corpus:update")
    @Operation(operationId = "4", summary = "更新信息")
    public Result<Boolean> update(@Parameter(description = "更新对象") @RequestBody BseCorpusUpdateDTO bseCorpusUpdateDTO) {
        return Result.status(bseCorpusFacade.update(bseCorpusUpdateDTO));
    }

    @DeleteMapping("/")
//    @SaCheckPermission("bse:corpus:delete")
    @Operation(operationId = "5", summary = "批量删除信息")
    public Result<Boolean> batchDelete(@Parameter(description = "删除对象") @RequestBody BseCorpusDeleteDTO bseCorpusDeleteDTO) {
        return Result.status(bseCorpusFacade.batchDelete(bseCorpusDeleteDTO));
    }

}