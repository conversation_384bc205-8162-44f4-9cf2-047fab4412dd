/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.infrastructure.config.WechatPayV3ClientFactory;
import com.izpan.infrastructure.constants.WechatPayConstants;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import com.izpan.modules.system.domain.dto.user.SysUserUpdateNumberDto;
import com.izpan.modules.system.service.ISysUserService;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.model.RefundNotification;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付回调控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.WechatPayCallbackController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@Tag(name = "微信支付回调")
@RequiredArgsConstructor
@RequestMapping("wechat/pay")
public class WechatPayCallbackController {

    private final WechatPayV3ClientFactory wechatPayV3ClientFactory;
    private final IBsePaymentOrderService bsePaymentOrderService;
    private final ISysUserService sysUserService;

    /**
     * 处理微信支付回调通知
     */
    @PostMapping("/notify")
    @Operation(operationId = "1", summary = "微信支付回调通知")
    public Map<String, String> handlePayNotify(HttpServletRequest request, @RequestBody String requestBody) {
        Map<String, String> response = new HashMap<>();

        try {
            Config config = wechatPayV3ClientFactory.getConfig();
            NotificationParser parser = new NotificationParser((com.wechat.pay.java.core.notification.NotificationConfig) config);

            // 构造请求参数
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .signature(request.getHeader("Wechatpay-Signature"))
                    .body(requestBody)
                    .build();

            // 解析回调通知，这里需要根据实际的回调类型来解析
            // 支付回调
            if (requestBody.contains("TRANSACTION.SUCCESS")) {
                Transaction transaction = parser.parse(requestParam, Transaction.class);
                handlePaymentSuccess(transaction);
            }
            // 退款回调
            else if (requestBody.contains("REFUND.SUCCESS") || requestBody.contains("REFUND.ABNORMAL") || requestBody.contains("REFUND.CLOSED")) {
                RefundNotification refund = parser.parse(requestParam, RefundNotification.class);
                handleRefundCallback(refund);
            }

            // 返回成功
            response.put("code", "SUCCESS");
            response.put("message", "成功");

        } catch (Exception e) {
            log.error("处理微信支付回调通知失败", e);
            response.put("code", "FAIL");
            response.put("message", "处理失败");
        }

        return response;
    }
    
    /**
     * 处理支付成功通知
     */
    @Transactional
    private void handlePaymentSuccess(Transaction transaction) {
        try {
            String orderNo = transaction.getOutTradeNo();
            String transactionId = transaction.getTransactionId();

            log.info("处理支付成功通知，订单号: {}，微信订单号: {}，状态: {}",
                    orderNo, transactionId, transaction.getTradeState());

            // 1. 根据商户订单号查询订单
            BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
                    .eq(BsePaymentOrder::getOrderNo, orderNo)
                    .one();

            if (paymentOrder == null) {
                log.error("订单不存在，订单号: {}", orderNo);
                return;
            }

            // 防止重复处理
            if (WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
                log.info("订单已处理过，订单号: {}", orderNo);
                return;
            }

            // 2. 更新订单状态为已支付
            paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PAID);
            paymentOrder.setChannelOrderNo(transactionId);
            paymentOrder.setPaidAt(LocalDateTime.now());

            // 更新实付金额（从微信返回的金额信息）
            if (transaction.getAmount() != null && transaction.getAmount().getTotal() != null) {
                paymentOrder.setAmountPaid(transaction.getAmount().getTotal().intValue());
            } else {
                // 如果微信没有返回金额，使用应付金额
                paymentOrder.setAmountPaid(paymentOrder.getAmountPayable());
            }

            boolean updateResult = bsePaymentOrderService.updateById(paymentOrder);

            if (updateResult) {
                log.info("订单状态更新成功，订单号: {}，微信交易号: {}，金额: {}分",
                        orderNo, transactionId, paymentOrder.getAmountPaid());
            } else {
                log.error("订单状态更新失败，订单号: {}", orderNo);
            }

        } catch (Exception e) {
            log.error("处理支付成功通知失败，订单号: {}", transaction.getOutTradeNo(), e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    /**
     * 处理退款回调通知
     */
    @Transactional
    private void handleRefundCallback(RefundNotification refund) {
        try {
            String refundNo = refund.getOutRefundNo();
            String refundId = refund.getRefundId();
            String refundStatus = refund.getRefundStatus().name();

            log.info("处理退款回调通知，退款单号: {}，微信退款单号: {}，状态: {}",
                    refundNo, refundId, refundStatus);

            // 1. 根据商户退款单号查询订单
            BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
                    .eq(BsePaymentOrder::getRefundNo, refundNo)
                    .one();

            if (paymentOrder == null) {
                log.error("退款订单不存在，退款单号: {}", refundNo);
                return;
            }

            // 2. 更新退款单状态
            String newRefundStatus = convertWechatRefundStatusToOrderStatus(refundStatus);

            // 防止重复处理
            if (newRefundStatus.equals(paymentOrder.getRefundStatus())) {
                log.info("退款状态已处理过，退款单号: {}，状态: {}", refundNo, newRefundStatus);
                return;
            }

            paymentOrder.setRefundStatus(newRefundStatus);
            paymentOrder.setChannelRefundNo(refundId);

            // 如果退款成功，设置退款成功时间
            if (WechatPayConstants.REFUND_STATUS_SUCCESS.equals(newRefundStatus)) {
                paymentOrder.setRefundedAt(LocalDateTime.now());

                // 更新实际退款金额（从微信返回的金额信息）
                if (refund.getAmount() != null && refund.getAmount().getRefund() != null) {
                    paymentOrder.setRefundAmount(refund.getAmount().getRefund().intValue());
                }

                // 退款成功后扣减用户次数
                handleRefundUserDeduction(paymentOrder);
            }

            boolean updateResult = bsePaymentOrderService.updateById(paymentOrder);

            if (updateResult) {
                log.info("退款状态更新成功，退款单号: {}，微信退款单号: {}，状态: {}",
                        refundNo, refundId, newRefundStatus);
            } else {
                log.error("退款状态更新失败，退款单号: {}", refundNo);
            }

        } catch (Exception e) {
            log.error("处理退款回调通知失败，退款单号: {}", refund.getOutRefundNo(), e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    /**
     * 转换微信退款状态为订单退款状态
     */
    private String convertWechatRefundStatusToOrderStatus(String wechatRefundStatus) {
        return switch (wechatRefundStatus) {
            case "SUCCESS" -> WechatPayConstants.REFUND_STATUS_SUCCESS;
            case "CLOSED" -> WechatPayConstants.REFUND_STATUS_FAILED;
            case "PROCESSING" -> WechatPayConstants.REFUND_STATUS_PENDING;
            case "ABNORMAL" -> WechatPayConstants.REFUND_STATUS_ABNORMAL;
            default -> WechatPayConstants.REFUND_STATUS_FAILED;
        };
    }

    /**
     * 处理退款成功后的用户次数扣减
     */
    private void handleRefundUserDeduction(BsePaymentOrder paymentOrder) {
        try {
            Long userId = paymentOrder.getUserId();
            if (userId == null) {
                log.warn("订单用户ID为空，无法扣减用户次数，订单号: {}", paymentOrder.getOrderNo());
                return;
            }

            // 解析套餐快照信息
            String packageSnapshot = paymentOrder.getPackageSnapshot();
            if (packageSnapshot == null || packageSnapshot.trim().isEmpty()) {
                log.warn("订单套餐快照为空，无法确定扣减类型，订单号: {}", paymentOrder.getOrderNo());
                return;
            }

            try {
                // 使用 Hutool 解析 JSON
                JSONObject packageInfo = JSONUtil.parseObj(packageSnapshot);
                String packageType = packageInfo.getStr("type");
                Integer giftCount = packageInfo.getInt("giftCount");
                String packageName = packageInfo.getStr("name");

                if (packageType == null || giftCount == null) {
                    log.warn("套餐快照信息不完整，订单号: {}，快照: {}", paymentOrder.getOrderNo(), packageSnapshot);
                    return;
                }

                // 根据套餐类型扣减相应的次数
                String countType;
                String description;

                switch (packageType) {
                    case "microLessons":
                        countType = "microLessonsNumber";
                        description = "微课次数";
                        break;
                    case "evaluation":
                        countType = "evaluationNumber";
                        description = "测评次数";
                        break;
                    default:
                        log.warn("未知的套餐类型: {}，订单号: {}", packageType, paymentOrder.getOrderNo());
                        return;
                }

                // 扣减对应类型的次数
                deductUserCount(userId, countType, giftCount,
                        String.format("退款扣减-%s", packageName), paymentOrder.getOrderNo());

                log.info("退款成功，已扣减用户{}，用户ID: {}，订单号: {}，扣减数量: {}",
                        description, userId, paymentOrder.getOrderNo(), giftCount);

            } catch (Exception parseException) {
                log.error("解析套餐快照失败，订单号: {}，快照: {}", paymentOrder.getOrderNo(), packageSnapshot, parseException);
            }

        } catch (Exception e) {
            log.error("处理退款用户次数扣减失败，订单号: {}", paymentOrder.getOrderNo(), e);
            // 这里不抛出异常，避免影响退款状态更新
        }
    }

    /**
     * 扣减用户指定类型的次数
     */
    private void deductUserCount(Long userId, String countType, Integer count, String remark, String businessId) {
        try {
            SysUserUpdateNumberDto dto = new SysUserUpdateNumberDto();
            dto.setId(userId);
            dto.setType("sub"); // 减少操作
            dto.setRowType(countType); // 次数类型：microLessonsNumber 或 evaluationNumber
            dto.setNumbers(count); // 扣减数量
            dto.setRemark(remark); // 备注
            dto.setBusinessId(businessId); // 业务ID（订单号）

            Integer remainingCount = sysUserService.updateTimes(dto);

            // 如果剩余次数小于0，设置为0
            if (remainingCount < 0) {
                SysUserUpdateNumberDto resetDto = new SysUserUpdateNumberDto();
                resetDto.setId(userId);
                resetDto.setType("add"); // 增加操作
                resetDto.setRowType(countType);
                resetDto.setNumbers(-remainingCount); // 加回负数部分，使其变为0
                resetDto.setRemark("退款扣减后次数调整为0");
                resetDto.setBusinessId(businessId);

                sysUserService.updateTimes(resetDto);
                log.info("用户次数不足，已调整为0，用户ID: {}，类型: {}，原剩余次数: {}",
                        userId, countType, remainingCount);
            }

            log.info("扣减用户次数成功，用户ID: {}，类型: {}，扣减数量: {}，剩余次数: {}",
                    userId, countType, count, Math.max(remainingCount, 0));

        } catch (Exception e) {
            log.error("扣减用户次数失败，用户ID: {}，类型: {}，扣减数量: {}", userId, countType, count, e);
        }
    }
}
