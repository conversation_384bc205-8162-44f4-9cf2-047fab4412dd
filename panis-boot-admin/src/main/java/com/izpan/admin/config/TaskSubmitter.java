package com.izpan.admin.config;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Semaphore;

@Slf4j
public class TaskSubmitter {

    private final ExecutorService executor;
    private final Semaphore semaphore;

    public TaskSubmitter(ExecutorService executor, Semaphore semaphore) {
        this.executor = executor;
        this.semaphore = semaphore;
    }

    public CompletableFuture<Void> submit(Runnable task) {
        try {
            semaphore.acquire();
            return CompletableFuture.runAsync(() -> {
                try {
                    task.run();
                } finally {
                    semaphore.release(); // 释放信号量
                }
            }, executor)
            .exceptionally(ex -> {
                log.error("任务执行失败", ex);
                semaphore.release(); // 出现异常也要释放信号量
                return null;
            });
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            log.warn("任务提交被中断");
        }
        return CompletableFuture.completedFuture(null);
    }
}