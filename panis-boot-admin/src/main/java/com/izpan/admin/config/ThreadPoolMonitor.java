package com.izpan.admin.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class ThreadPoolMonitor {

    @Autowired
    @Qualifier("dataSyncExecutor")
    private ExecutorService executor;

    // 每隔 10 秒打印一次线程池信息
    //@Scheduled(fixedRate = 10000)
    public void monitor() {
        if (!(executor instanceof ThreadPoolExecutor)) {
            log.warn("线程池类型不匹配，无法监控");
            return;
        }

        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) executor;

        long taskCount = threadPoolExecutor.getTaskCount();
        long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();
        int activeCount = threadPoolExecutor.getActiveCount();
        int poolSize = threadPoolExecutor.getPoolSize();
        int queueSize = threadPoolExecutor.getQueue().size();
        int largestPoolSize = threadPoolExecutor.getLargestPoolSize();
        int corePoolSize = threadPoolExecutor.getCorePoolSize();
        int maximumPoolSize = threadPoolExecutor.getMaximumPoolSize();

        log.info("[线程池监控] 核心线程数: {}, 最大线程数: {}, 当前线程数: {}, 最大并发记录: {}",
                corePoolSize, maximumPoolSize, poolSize, largestPoolSize);
        log.info("[线程池监控] 总任务数: {}, 已完成任务数: {}, 队列任务数: {}",
                taskCount, completedTaskCount, queueSize);

        if (queueSize > 0) {
            log.warn("⚠️ 警告：线程池任务队列中有等待任务，请注意负载情况");
        }

        if (activeCount >= maximumPoolSize) {
            log.warn("❗警告：线程池已达到最大线程数，请考虑扩容或优化任务执行时间");
        }
    }
}