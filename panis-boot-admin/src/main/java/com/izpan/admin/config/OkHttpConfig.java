package com.izpan.admin.config;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class OkHttpConfig {

    @Bean
    public ConnectionPool connectionPool() {
        return new ConnectionPool(
                200,                // 最大空闲连接数，建议略大于最大线程数
                60, TimeUnit.MINUTES // 空闲连接存活时间
        );
    }

    @Bean
    public OkHttpClient okHttpClient(ConnectionPool connectionPool) {
        return new OkHttpClient.Builder()
                .connectionPool(connectionPool)
                .connectTimeout(Duration.ofHours(2))
                .readTimeout(Duration.ofHours(1))
                .writeTimeout(Duration.ofHours(1))
                .retryOnConnectionFailure(true)
                .build();
    }
}
