/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.service;

import java.io.OutputStream;
import java.util.List;

/**
 * 作文Word导出服务接口
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName IBizWordExportService
 * @CreateTime 2025-01-15
 */
public interface IBizWordExportService {

    /**
     * 导出单个作文为Word文档
     *
     * @param compositionId 作文记录ID
     * @param outputStream 输出流
     * @return 生成的文件名
     * @throws RuntimeException 如果导出失败
     */
    String exportSingleComposition(Long compositionId, OutputStream outputStream);

    /**
     * 批量导出作文为压缩包
     *
     * @param compositionIds 作文记录ID列表
     * @param zipFileName 压缩包文件名（可选，为空时自动生成）
     * @return 压缩包字节数组
     * @throws RuntimeException 如果导出失败
     */
    byte[] exportCompositionBatch(List<Long> compositionIds, String zipFileName);

    /**
     * 验证作文ID是否存在
     *
     * @param compositionId 作文记录ID
     * @return 是否存在
     */
    boolean validateCompositionExists(Long compositionId);

    /**
     * 获取作文导出文件名
     *
     * @param compositionId 作文记录ID
     * @return 文件名
     */
    String getCompositionFileName(Long compositionId);
} 