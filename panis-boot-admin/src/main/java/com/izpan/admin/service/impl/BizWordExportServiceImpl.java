/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import com.izpan.common.exception.BizException;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;
import com.izpan.modules.biz.facade.IBizCompositionLogFacade;
import com.izpan.admin.service.IBizWordExportService;
import com.izpan.admin.util.WordTemplateUtil;
import com.izpan.starter.common.util.ZipUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作文Word导出服务实现类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName BizWordExportServiceImpl
 * @CreateTime 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BizWordExportServiceImpl implements IBizWordExportService {

    private static final String ESSAY_TEMPLATE_PATH = "templates/essay_template.docx";
    
    @NonNull
    private IBizCompositionLogFacade bizCompositionLogFacade;

    @Override
    public String exportSingleComposition(Long compositionId, OutputStream outputStream) {
        log.info("开始导出单个作文Word文档，ID: {}", compositionId);
        
        try {
            // 0. 验证模板文件
            String validationResult = WordTemplateUtil.validateTemplate(ESSAY_TEMPLATE_PATH);
            log.info("模板验证结果: {}", validationResult);
            if (validationResult.contains("失败") || validationResult.contains("不存在")) {
                throw new BizException("模板文件验证失败: " + validationResult);
            }
            
            // 1. 获取作文详情
            BizCompositionLogVO compositionLog = bizCompositionLogFacade.get(compositionId);
            if (compositionLog == null) {
                throw new BizException("未找到ID为" + compositionId + "的作文记录");
            }

            // 2. 构建数据模型
            Map<String, Object> dataModel = buildCompositionDataModel(compositionLog);

            // 3. 自动填充缺失的模板变量
            WordTemplateUtil.autoFillMissingVariables(ESSAY_TEMPLATE_PATH, dataModel);

            // 4. 渲染模板
            WordTemplateUtil.renderTemplate(ESSAY_TEMPLATE_PATH, dataModel, outputStream);
            
            // 5. 生成文件名
            String fileName = compositionLog.getTitle() + "-" + compositionLog.getAuthor() + ".docx";
            log.info("成功导出作文Word文档，ID: {}, 标题: {}", compositionId, compositionLog.getTitle());
            
            return fileName;
            
        } catch (IOException e) {
            log.error("导出Word文档时发生IO错误: {}", e.getMessage(), e);
            throw new BizException("导出Word文档失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导出Word文档时发生未知错误: {}", e.getMessage(), e);
            throw new BizException("导出Word文档失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportCompositionBatch(List<Long> compositionIds, String zipFileName) {
        log.info("开始批量导出作文Word文档，数量: {}", compositionIds.size());
        
        Path tempDir = null;
        try {
            // 1. 创建临时目录
            tempDir = Files.createTempDirectory("composition_export_");
            log.debug("创建临时目录: {}", tempDir);

            // 2. 为每个作文生成Word文档
            for (Long compositionId : compositionIds) {
                try {
                    generateWordFile(compositionId, tempDir);
                } catch (Exception e) {
                    log.error("生成作文ID: {} 的Word文档失败: {}", compositionId, e.getMessage());
                    // 继续处理其他作文，不中断整个批量导出
                }
            }

            // 3. 压缩目录
            try (ByteArrayOutputStream zipStream = ZipUtil.createZipFileAsStream(tempDir.toString())) {
                byte[] zipBytes = zipStream.toByteArray();
                log.info("成功批量导出作文Word文档，压缩包大小: {} bytes", zipBytes.length);
                return zipBytes;
            }

        } catch (IOException e) {
            log.error("批量导出作文时发生IO错误: {}", e.getMessage(), e);
            throw new BizException("批量导出失败: " + e.getMessage());
        } finally {
            // 4. 清理临时目录
            if (tempDir != null) {
                cleanupTempDirectory(tempDir);
            }
        }
    }

    @Override
    public boolean validateCompositionExists(Long compositionId) {
        try {
            BizCompositionLogVO composition = bizCompositionLogFacade.get(compositionId);
            return composition != null;
        } catch (Exception e) {
            log.warn("验证作文ID: {} 存在性时发生错误: {}", compositionId, e.getMessage());
            return false;
        }
    }

    @Override
    public String getCompositionFileName(Long compositionId) {
        try {
            BizCompositionLogVO compositionLog = bizCompositionLogFacade.get(compositionId);
            if (compositionLog == null) {
                return "作文_" + compositionId + ".docx";
            }
            return URLEncoder.encode(
                compositionLog.getTitle() + "-" + compositionLog.getAuthor() + ".docx", 
                StandardCharsets.UTF_8
            );
        } catch (Exception e) {
            log.warn("获取作文ID: {} 文件名时发生错误: {}", compositionId, e.getMessage());
            return "作文_" + compositionId + ".docx";
        }
    }

    /**
     * 构建作文数据模型
     */
    private Map<String, Object> buildCompositionDataModel(BizCompositionLogVO compositionLog) {
        Map<String, Object> dataModel = new HashMap<>();

        // 基本信息
        dataModel.put("title", StringUtils.defaultString(compositionLog.getTitle(), ""));
        dataModel.put("author", StringUtils.defaultString(compositionLog.getAuthor(), ""));
        dataModel.put("score", compositionLog.getScore() != null ? compositionLog.getScore().toString() : "0");
        dataModel.put("grade", StringUtils.defaultString(compositionLog.getGrade(), ""));
        dataModel.put("classified", StringUtils.defaultString(compositionLog.getClassified(), ""));
        dataModel.put("wenti", StringUtils.defaultString(compositionLog.getWenTi(), ""));
        dataModel.put("words", compositionLog.getWords() != null ? compositionLog.getWords().toString() : "0");
        dataModel.put("sentences", compositionLog.getSentences() != null ? compositionLog.getSentences().toString() : "0");
        dataModel.put("paragraphs", compositionLog.getParagraphs() != null ? compositionLog.getParagraphs().toString() : "0");
        dataModel.put("percentage", compositionLog.getPercentage() != null ? compositionLog.getPercentage().toString() : "0");
        dataModel.put("fullScore", compositionLog.getFullScore() != null ? compositionLog.getFullScore().toString() : "100");
        dataModel.put("scoreRanking", compositionLog.getScoreRanking() != null ? compositionLog.getScoreRanking().toString() : "0");
        dataModel.put("submitTime", compositionLog.getSubmitTime() != null ? 
            DateUtil.format(compositionLog.getSubmitTime(), "YYYY-MM-DD hh:mm:ss") : "");

        // 作文内容处理
        if (compositionLog.getContentList() != null && !compositionLog.getContentList().isEmpty()) {
            dataModel.put("content", WordTemplateUtil.formatRichCompositionContent(
                    compositionLog.getContentList(),
                    compositionLog.getMeiList(),
                    compositionLog.getErrorList(),
                    compositionLog.getGoodWordsList()
            ));
        } else if (compositionLog.getContent() != null) {
            dataModel.put("content", WordTemplateUtil.createParagraphs(compositionLog.getContent()));
        } else {
            dataModel.put("content", WordTemplateUtil.createParagraphs(""));
        }

        dataModel.put("advice", StringUtils.defaultString(compositionLog.getAdvice(), ""));

        // 处理评语
        if (compositionLog.getComments() != null) {
            dataModel.put("comments", WordTemplateUtil.formatComments(compositionLog.getComments()));
        } else {
            dataModel.put("comments", WordTemplateUtil.formatComments(""));
        }

        // 处理好词好句
        if (compositionLog.getGoodWordsList() != null && !compositionLog.getGoodWordsList().isEmpty()) {
            StringBuilder goodWords = new StringBuilder();
            compositionLog.getGoodWordsList().forEach(item -> {
                if (item != null && StringUtils.isNotBlank(item.getGcontent())) {
                    goodWords.append(item.getGname())
                            .append("：")
                            .append(item.getGcontent())
                            .append("\n");
                }
            });
            dataModel.put("goodWords", goodWords.toString());
        } else {
            dataModel.put("goodWords", "");
        }

        // 处理错别字
        if (compositionLog.getErrorList() != null && !compositionLog.getErrorList().isEmpty()) {
            StringBuilder errorWords = new StringBuilder();
            compositionLog.getErrorList().forEach(item -> {
                if (item != null && StringUtils.isNotBlank(item.getEsentence())) {
                    errorWords.append(item.getEsentence())
                            .append("(")
                            .append(StringUtils.defaultString(item.getEanswer(), ""))
                            .append(")")
                            .append("\n");
                }
            });
            dataModel.put("errorWords", errorWords.toString());
        } else {
            dataModel.put("errorWords", "");
        }

        return dataModel;
    }

    /**
     * 生成单个Word文件到临时目录
     */
    private void generateWordFile(Long compositionId, Path tempDir) throws IOException {
        BizCompositionLogVO compositionLog = bizCompositionLogFacade.get(compositionId);
        if (compositionLog == null) {
            log.warn("跳过不存在的作文ID: {}", compositionId);
            return;
        }

        // 构建数据模型
        Map<String, Object> dataModel = buildCompositionDataModel(compositionLog);
        WordTemplateUtil.autoFillMissingVariables(ESSAY_TEMPLATE_PATH, dataModel);

        // 生成文件名，确保文件名安全
        String safeFileName = sanitizeFileName(compositionLog.getTitle() + "-" + compositionLog.getAuthor()) + ".docx";
        Path outputPath = tempDir.resolve(safeFileName);

        // 生成Word文档
        try (FileOutputStream fos = new FileOutputStream(outputPath.toFile())) {
            WordTemplateUtil.renderTemplate(ESSAY_TEMPLATE_PATH, dataModel, fos);
            log.debug("成功生成Word文件: {}", outputPath);
        }
    }

    /**
     * 清理临时目录
     */
    private void cleanupTempDirectory(Path tempDir) {
        try {
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                    .sorted((a, b) -> b.compareTo(a)) // 反向排序，先删除文件再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除临时文件失败: {}", path, e);
                        }
                    });
                log.debug("清理临时目录完成: {}", tempDir);
            }
        } catch (IOException e) {
            log.warn("清理临时目录时发生错误: {}", tempDir, e);
        }
    }

    /**
     * 安全化文件名，移除非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "untitled";
        }
        // 移除文件名中的非法字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_").trim();
    }
} 