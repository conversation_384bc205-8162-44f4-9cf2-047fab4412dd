package com.izpan.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * PanisBoot Application 项目启动
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.PanisBootAdminApplication
 * @CreateTime 2023/7/6 - 11:11
 */

@EnableRetry
@EnableScheduling
@MapperScan("com.izpan.modules.**.repository.mapper")
@SpringBootApplication(scanBasePackages = "com.izpan.**")
public class PanisBootAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(PanisBootAdminApplication.class, args);
    }

}
