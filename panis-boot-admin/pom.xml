<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.izpan</groupId>
        <artifactId>panis-boot</artifactId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>

    <version>1.0.5-SNAPSHOT</version>
    <name>panis-boot-admin</name>
    <artifactId>panis-boot-admin</artifactId>
    <description>PanisBoot 后台管理系统 - 后台 Application 模块</description>

    <dependencies>
        <!-- Modules -->
        <dependency>
            <groupId>com.izpan</groupId>
            <artifactId>panis-boot-modules</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>

        <!-- POI-TL -->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
        </dependency>

        <!-- fastjson2 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
                <groupId>com.github.wechatpay-apiv3</groupId>
                <artifactId>wechatpay-java</artifactId>
            </dependency>
    </dependencies>

    <build>
        <resources>
            <!-- 需要过滤的资源文件，排除二进制文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.docx</exclude>
                    <exclude>**/*.doc</exclude>
                    <exclude>**/*.p12</exclude>
                    <exclude>**/*.pem</exclude>
                </excludes>
            </resource>
            <!-- 不需要过滤的二进制文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.docx</include>
                    <include>**/*.doc</include>
                    <include>**/*.p12</include>
                    <include>**/*.pem</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
