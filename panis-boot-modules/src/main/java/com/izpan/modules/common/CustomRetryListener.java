package com.izpan.modules.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CustomRetryListener implements RetryListener {

    private static final int MAX_ATTEMPTS = 3; // 设置最大重试次数

    @Override
    public <T, E extends Throwable> void onError(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
        int currentAttempt = context.getRetryCount() + 1; // retryCount 是从 0 开始的

        if (currentAttempt == MAX_ATTEMPTS) {
            log.error("达到最大重试次数，不再重试", throwable);
        }
    }
}
