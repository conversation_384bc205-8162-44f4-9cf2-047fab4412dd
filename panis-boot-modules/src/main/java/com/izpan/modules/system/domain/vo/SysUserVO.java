package com.izpan.modules.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.izpan.infrastructure.domain.BaseVO;
import com.izpan.modules.system.domain.bo.SysRoleBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户管理 VO 类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.vo.SysUserVO
 * @CreateTime 2023/7/10 - 14:45
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysUserVO", description = "用户管理 VO 对象")
public class SysUserVO extends BaseVO {

    @Serial
    private static final long serialVersionUID = -3621474283154332722L;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "性别 0保密 1男 2女 ")
    private String gender;

    @Schema(description = "状态 0禁用 1启用")
    private String status;

    @Schema(description = "最近登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最近修改密码时间")
    private LocalDateTime updatePasswordTime;

    @Schema(description = "更新用户名称")
    private String updateUser;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "路由权限按钮")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> permissions;

    /**
     * 学校编码
     */
    private String schoolId;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 年级编码
     */
    private String gradeId;
    /**
     * 年级名称
     */
    private String gradeName;
    /**
     * 班级编码
     */
    private String clazzId;
    /**
     * 班级名称
     */
    private String clazzName;
    /**
     * 用户类别（0:超级管理员 1:管理员 2:教师 3:用户）
     */
    private String userType = "2";

    @Schema(description = "角色 Ids 集合")
    private List<Long> roleIds;

    @Schema(description = "当前使用角色角色Id")
    private Long currentRoleId;

    @Schema(description = "当前使用角色名称")
    private String currentRoleName;

    @Schema(description = "角色标识")
    private Long positionIdentify;

    private List<SysRoleBO> roleList;

    @Schema(description = "学号")
    private String studentNum;

    @Schema(description = "测评次数")
    private Integer evaluationNumber;

    @Schema(description = "微课次数")
    private Integer microLessonsNumber;
}
