package com.izpan.modules.system.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 用户角色管理 Entity 实体类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.entity.SysUserRole
 * @CreateTime 2023-07-24
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user_role")
public class SysUserRole extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -2483703153476327443L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    private String status;

    public SysUserRole(Long userId, Long roleId,String schoolId,String schoolName) {
        this.userId = userId;
        this.roleId = roleId;
        this.schoolId = schoolId;
        this.schoolName = schoolName;
    }
    /**
     * 学校编码
     */
    private String  schoolId;
    /**
     * 学校名称
     */
    private String  schoolName;
}