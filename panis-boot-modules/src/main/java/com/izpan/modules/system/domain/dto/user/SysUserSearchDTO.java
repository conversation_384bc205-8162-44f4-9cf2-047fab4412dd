package com.izpan.modules.system.domain.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户管理 查询 DTO 对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.SysUserSearchDTO
 * @CreateTime 2023/7/10 - 15:04
 */

@Getter
@Setter
public class SysUserSearchDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8914866620633001207L;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "年级ID")
    private String gradeId;

    @Schema(description = "班级ID")
    private String clazzId;

    @Schema(description = "身份类别")
    private String userType;

    @Schema(description = "班级Ids")
    private List<Long> clazzIds;

    @Schema(description = "年级Ids")
    private List<Long> gradeIds;
}
