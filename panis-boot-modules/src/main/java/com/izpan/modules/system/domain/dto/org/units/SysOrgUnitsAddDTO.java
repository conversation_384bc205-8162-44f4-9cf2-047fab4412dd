/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.domain.dto.org.units;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 年级/班级管理 新增 DTO 对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsAddDTO
 * @CreateTime 2024-07-16 - 16:35:30
 */

@Getter
@Setter
@Schema(name = "SysOrgUnitsAddDTO", description = "年级/班级管理 新增 DTO 对象")
public class SysOrgUnitsAddDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2330658363903838033L;

    @Schema(description = "父年级/班级ID")
    private Long parentId;

    @Schema(description = "年级/班级名称")
    private String name;

    @Schema(description = "年级/班级编码")
    private String code;

    @Schema(description = "年级/班级名称简写")
    private String abbr;

    @Schema(description = "年级/班级层级")
    private Integer level;

    @Schema(description = "祖先节点")
    private String ancestors;

    @Schema(description = "年级/班级描述")
    private String description;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "是否启用(0:禁用,1:启用)")
    private String status;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "学校名称")
    private String schoolName;

}