/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.domain.dto.dict.item;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据字典子项管理 全局查询 DTO 对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.dict.item.SysDictItemStoreSearchDTO
 * @CreateTime 2024/7/27 - 18:22
 */

@Getter
@Setter
@Schema(name = "SysDictItemStoreSearchDTO", description = "数据字典子项管理 全局查询 DTO 对象")
public class SysDictItemStoreSearchDTO {

    @Schema(description = "字典编码")
    private String dictCode;

    @Schema(description = "语言")
    @NotBlank(message = "语言不能为空")
    private String language;
}
