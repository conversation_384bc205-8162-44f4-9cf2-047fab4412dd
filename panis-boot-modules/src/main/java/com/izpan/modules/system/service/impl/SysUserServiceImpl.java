package com.izpan.modules.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izpan.common.constants.RequestConstant;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.exception.BizException;
import com.izpan.common.pool.StringPools;
import com.izpan.common.util.CglibUtil;
import com.izpan.common.util.CollectionUtil;
import com.izpan.common.util.IPUtil;
import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.util.RedisUtil;
import com.izpan.infrastructure.util.ServletHolderUtil;
import com.izpan.modules.base.domain.entity.BseUserNumberRecord;
import com.izpan.modules.base.service.IBseSchoolService;
import com.izpan.modules.base.service.IBseUserNumberRecordService;
import com.izpan.modules.monitor.domain.entity.MonLogsLogin;
import com.izpan.modules.monitor.service.IMonLogsLoginService;
import com.izpan.modules.system.domain.bo.*;
import com.izpan.modules.system.domain.dto.user.SysUserDeleteDTO;
import com.izpan.modules.system.domain.dto.user.SysUserUpdateNumberDto;
import com.izpan.modules.system.domain.entity.SysUser;
import com.izpan.modules.system.domain.vo.SysUserVO;
import com.izpan.modules.system.repository.mapper.SysUserMapper;
import com.izpan.modules.system.service.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 用户管理 Service 服务接口实现层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.service.impl.SysUserServiceImpl
 * @CreateTime 2023/7/6 - 16:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @NonNull
    private ISysRoleService sysRoleService;

    @NonNull
    private ISysUserRoleService sysUserRoleService;

    @NonNull
    private ISysUserOrgService sysUserOrgService;

    @NonNull
    private IMonLogsLoginService monLogsLoginService;

    @NonNull
    private IBseSchoolService bseSchoolService;

    @NonNull
    private IBseUserNumberRecordService bseUserNumberRecordService;

    @Override
    public IPage<SysUser> listSysUserPage(PageQuery pageQuery, SysUserBO sysUserBO) {
        IPage<SysUser> iPage = pageQuery.buildPage();
        List<SysUser> sysUserList = baseMapper.listSysUserPage(iPage, sysUserBO);
        if (CollectionUtil.isEmpty(sysUserList)) {
            sysUserList = new ArrayList<>();
        }
        iPage.setRecords(sysUserList);
        return iPage;
    }

    @Override
    public SysUserBO currentUserInfo() {
        SysUser byId = super.getById(GlobalUserHolder.getUserId());
        return CglibUtil.convertObj(byId, SysUserBO::new);
    }

    @Override
    public boolean addUser(SysUserBO sysUserBO) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUserName, sysUserBO.getUserName());
        if (super.count(queryWrapper) > 0) {
            throw new BizException("用户名已存在，请更换其他用户名");
        }
        return super.save(sysUserBO);
    }

    @Override
    public boolean updateUser(SysUserBO sysUserBO) {
        boolean updateById = super.updateById(sysUserBO);
        // 用户管理修改用户，则退出用户，要求重登
        StpUtil.logout(sysUserBO.getId());
        return updateById;
    }

    @Override
    public boolean updateCurrentUserInfo(SysUserBO sysUserBO) {
        boolean updateById = super.updateById(sysUserBO);
        // 自我更新个人资料，需要更新缓存资料
        saveUserToSession(sysUserBO, true);
        return updateById;
    }

    @Override
    public boolean removeBatchByIds(List<Long> ids) {
        if (!StpUtil.hasRole(StringPools.ADMIN.toUpperCase())) {
            throw new BizException("非管理员角色禁止删除用户");
        }
        boolean containAdmin = baseMapper.queryIsContainAdmin(ids);
        if (containAdmin) {
            throw new BizException("禁止删除《管理员》用户");
        }
        return super.removeBatchByIds(ids, true);
    }

    @Override
    public Map<String, String> userLogin(SysUserBO sysUserBO) {
        MonLogsLogin loginLogs = initLoginLog(sysUserBO);
        SysUser userForUserName = baseMapper.getUserByUserName(sysUserBO.getUserName());
        try {
            if (ObjectUtils.isEmpty(userForUserName)) {
                throw new BizException("查找不到用户名 %s".formatted(sysUserBO.getUserName()));
            }
            if (StringPools.ZERO.equals(userForUserName.getStatus())) {
                throw new BizException("当前用户 %s 已被禁止登录".formatted(sysUserBO.getUserName()));
            }

            // 判断学校是否过期
            isSchoolExpired(userForUserName);

            // 密码拼接
            String inputPassword = sysUserBO.getPassword() + userForUserName.getSalt();
            // 密码比对
            if (!DigestUtils.sha256Hex(inputPassword).equals(userForUserName.getPassword())) {
                throw new BizException("登录失败，请核实用户名以及密码");
            }
            // sa token 进行登录
            StpUtil.login(userForUserName.getId());
            // 更新用户登录时间
            userForUserName.setLastLoginTime(LocalDateTime.now());
            saveUserToSession(userForUserName, false);
            loginLogs.setUserId(userForUserName.getId());
            loginLogs.setUserRealName(userForUserName.getRealName());
            super.updateById(userForUserName);
        } catch (BizException e) {
            loginLogs.setStatus(StringPools.ZERO);
            loginLogs.setMessage(e.getMessage());
            throw e;
        } finally {
            monLogsLoginService.save(loginLogs);
        }
        return Map.of("token", StpUtil.getTokenValue());
    }

    /**
     * 初始化登录日志
     *
     * @param sysUserBO 用户对象
     * @return {@linkplain MonLogsLogin} 登录日志对象
     * <AUTHOR>
     * @CreateTime 2024-05-05 18:44
     */
    /**
     * 初始化登录日志
     *
     * @param sysUserBO 用户对象
     * @return {@linkplain MonLogsLogin} 登录日志对象
     * <AUTHOR>
     * @CreateTime 2024-05-05 18:44
     */
    private MonLogsLogin initLoginLog(SysUserBO sysUserBO) {
        String ip = JakartaServletUtil.getClientIP(ServletHolderUtil.getRequest());
        return MonLogsLogin.builder()
                .userName(sysUserBO.getUserName())
                .status(StringPools.ONE)
                .userAgent(ServletHolderUtil.getRequest().getHeader(RequestConstant.USER_AGENT))
                .ip(ip)
                .ipAddr(IPUtil.getIpAddr(ip))
                .message("登陆成功")
                .build();
    }

    private void isSchoolExpired(SysUser userForUserName) {
        if (StrUtil.isNotEmpty(userForUserName.getSchoolId()) && bseSchoolService.isSchoolExpired(userForUserName.getSchoolId())) {
            throw new BizException("当前学校 %s 已到期未续费，请联系管理员".formatted(userForUserName.getSchoolName()));
        }
    }

    /**
     * 将用户信息存入 Session
     *
     * @param sysUser   用户对象
     * @param needCheck 是否需要查找数据库用户信息
     * <AUTHOR>
     * @CreateTime 2024-04-21 22:19
     */
    private void saveUserToSession(SysUser sysUser, boolean needCheck) {
        if (needCheck) {
            sysUser = super.getById(sysUser.getId());
        }
        // 用户转换
        LoginUser loginUser = CglibUtil.convertObj(sysUser, LoginUser::new);
        // 获取用户角色
        List<SysRoleBO> sysRoleBOS = sysRoleService.queryRoleListWithUserId(sysUser.getId());
        loginUser.setRoleIds(sysRoleBOS.stream().map(SysRoleBO::getId).toList());
        loginUser.setRoleCodes(sysRoleBOS.stream().map(SysRoleBO::getRoleCode).toList());
        if (CollectionUtil.isNotEmpty(sysRoleBOS)) {
            // 当前使用角色ID
            loginUser.setCurrentRoleId(sysRoleBOS.get(0).getId());
            loginUser.setCurrentRoleName(sysRoleBOS.get(0).getRoleName());
            loginUser.setPositionIdentify(sysRoleBOS.get(0).getPositionIdentify());
        }

        // Session 放入用户对象
        StpUtil.getSessionByLoginId(sysUser.getId()).set("user", loginUser);
    }

    @Override
    public Map<String, String> refreshToken(String refreshToken, String refreshTokenCacheKey, LoginUser loginUser) {
        // 删除 旧的 refresh token
        RedisUtil.del(refreshTokenCacheKey);
        return Map.of();
    }

    @Override
    public String resetPassword(Long userId) {
        SysUser sysUser = baseMapper.selectById(userId);
        if (ObjectUtils.isEmpty(sysUser)) {
            throw new BizException("查找不到用户信息");
        }
        if (StringPools.ADMIN.equalsIgnoreCase(sysUser.getUserName())) {
            throw new BizException("禁止重置《%s》账户密码".formatted(StringPools.ADMIN));
        }
        // 密码盐值
        sysUser.setSalt(RandomStringUtils.randomAlphabetic(6));
        String randomPwd = "123456";
        String sha256HexPwd = DigestUtils.sha256Hex(randomPwd);
        String password = DigestUtils.sha256Hex(sha256HexPwd + sysUser.getSalt());
        sysUser.setPassword(password);
        sysUser.setUpdatePasswordTime(LocalDateTime.now());
        super.updateById(sysUser);
        return randomPwd;
    }

    @Override
    public SysUserResponsibilitiesBO queryUserResponsibilitiesWithUserId(Long userId) {
        List<Long> userRoleIds = sysUserRoleService.queryRoleIdsWithUserId(userId);
        // 用户所属组织
        List<SysUserOrgBO> sysUserOrgBOList = sysUserOrgService.queryOrgUnitsListWithUserId(userId);
        List<Long> userOrgUnitsPrincipalIds = sysUserOrgBOList.stream().filter(item -> StringPools.ONE.equals(item.getPrincipal())).map(SysUserOrgBO::getOrgId).toList();
        List<Long> userOrgUnitsIds = sysUserOrgBOList.stream().map(SysUserOrgBO::getOrgId).toList();
        return SysUserResponsibilitiesBO.builder().userId(userId).roleIds(userRoleIds).orgUnitsIds(userOrgUnitsIds).orgUnitsPrincipalIds(userOrgUnitsPrincipalIds).build();
    }

    @Override
    public boolean updateUserResponsibilities(SysUserResponsibilitiesBO responsibilitiesBO) {
        Long userId = responsibilitiesBO.getUserId();
        boolean role = sysUserRoleService.updateUserRole(userId, responsibilitiesBO.getRoleIds());
        boolean userOrg = sysUserOrgService.updateUserOrg(userId, responsibilitiesBO.getOrgUnitsIds(), responsibilitiesBO.getOrgUnitsPrincipalIds());
        return role && userOrg;
    }

    @Override
    public SysUserVO getUserByUserName(String userName) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUserName, userName);
        SysUser user = this.getOne(queryWrapper);
        if (user == null) {
            return null;
        }
        return CglibUtil.convertObj(user, SysUserVO::new);
    }

    @Override
    public boolean changePassword(String userName, String oldPassword, String newPassword) {
        // 获取用户信息
        SysUser user = baseMapper.getUserByUserName(userName);
        if (user == null) {
            return false;
        }

        // 验证原密码
        String oldPasswordWithSalt = DigestUtils.sha256Hex(oldPassword) + user.getSalt();
        String oldPasswordEncrypted = DigestUtils.sha256Hex(oldPasswordWithSalt);

        // 如果原密码不匹配，返回失败
        if (!oldPasswordEncrypted.equals(user.getPassword())) {
            return false;
        }

        // 更新密码
        // 生成新的盐值
        String newSalt = RandomStringUtils.randomAlphabetic(6);
        user.setSalt(newSalt);

        // 加密新密码
        String newPasswordWithSalt = DigestUtils.sha256Hex(newPassword) + newSalt;
        String newPasswordEncrypted = DigestUtils.sha256Hex(newPasswordWithSalt);
        user.setPassword(newPasswordEncrypted);

        // 更新密码修改时间
        user.setUpdatePasswordTime(LocalDateTime.now());

        // 保存更新
        boolean result = this.updateById(user);

        // 如果更新成功，让用户重新登录
        if (result) {
            StpUtil.logout(user.getId());
        }

        return result;
    }

    @Override
    public boolean removeUser(SysUserDeleteDTO userIds) {
        List<SysUser> sysUsers = baseMapper.selectBatchIds(userIds.getIds());
        for (SysUser sysUser : sysUsers) {
            sysUser.setUserType(SystemUserTypeEnum.USER.getValue());
            sysUser.setSchoolId(StringPools.EMPTY);
            sysUser.setSchoolName(StringPools.EMPTY);
            sysUser.setClazzId(StringPools.EMPTY);
            sysUser.setClazzName(StringPools.EMPTY);
            sysUser.setGradeId(StringPools.EMPTY);
            sysUser.setGradeName(StringPools.EMPTY);
        }
        return super.updateBatchById(sysUsers);
    }

    /**
     * 更新用户计数
     * 通用计数更新方法，支持多种类型的计数字段
     *
     * @param dto 包含更新信息的DTO
     * @return 更新后的计数值
     */
    @Override
    public Integer updateTimes(SysUserUpdateNumberDto dto) {
        if (dto.getId() == null) {
            LoginUser loginUser = GlobalUserHolder.getUser();
            dto.setId(loginUser.getId());
        }

        // 获取用户信息
        SysUser sysUser = this.getById(dto.getId());
        if (sysUser == null) {
            throw new BizException("用户不存在");
        }

        // 根据字段类型动态处理不同的计数器
        Integer updatedValue = null;

        BseUserNumberRecord record = new BseUserNumberRecord();
        record.setOperationTime(LocalDateTime.now());
        record.setUserId(dto.getId());
        record.setBusinessType(dto.getRowType());
        record.setOperationType(dto.getType());
        record.setDeductionCount(dto.getNumbers());

        record.setBusinessId(dto.getBusinessId());

        switch (dto.getRowType()) {
            case "microLessonsNumber":
                updatedValue = updateCounterValue(sysUser.getMicroLessonsNumber(), dto);
                sysUser.setMicroLessonsNumber(updatedValue);
                record.setRemainingCount(updatedValue);
                break;
            case "evaluationNumber":
                updatedValue = updateCounterValue(sysUser.getEvaluationNumber(), dto);
                record.setRemainingCount(updatedValue);
                sysUser.setEvaluationNumber(updatedValue);
                break;
            // 可以根据需要添加更多的计数类型
            default:
                throw new BizException("不支持的计数类型: " + dto.getRowType());
        }
        if (updatedValue >= 0) {
            bseUserNumberRecordService.save(record);
            this.updateById(sysUser);
        }


        return updatedValue;
    }

    /**
     * 根据操作类型更新计数值
     *
     * @param currentValue 当前值
     * @param dto          更新DTO
     * @return 更新后的值
     */
    private Integer updateCounterValue(Integer currentValue, SysUserUpdateNumberDto dto) {
        // 如果当前值为null，初始化为0
        if (currentValue == null) {
            currentValue = 0;
        }

        // 根据类型进行加减操作
        if ("add".equals(dto.getType())) {
            // 加法操作
            return currentValue + dto.getNumbers();
        } else if ("sub".equals(dto.getType())) {
            // 减法操作，确保不会小于0
            int result = currentValue - dto.getNumbers();
            return result;
        } else {
            throw new BizException("不支持的操作类型: " + dto.getType());
        }
    }

}