/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.service;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.modules.system.domain.bo.SysUserMenuPermissionsBO;
import com.izpan.modules.system.domain.entity.SysUserMenuPermissions;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 用户菜单权限表 Service 服务接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.service.ISysUserMenuPermissionsService
 * @CreateTime 2024-12-13 - 22:21:55
 */

public interface ISysUserMenuPermissionsService extends IService<SysUserMenuPermissions> {

    /**
     * 用户菜单权限表 - 分页查询
     *
     * @param pageQuery 分页对象
     * @param sysUserMenuPermissionsBO BO 查询对象
     * @return {@link IPage} 分页结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:21:55
     */
    IPage<SysUserMenuPermissions> listSysUserMenuPermissionsPage(PageQuery pageQuery, SysUserMenuPermissionsBO sysUserMenuPermissionsBO);

    List<SysUserMenuPermissions> getByUserId(Long userId);

    boolean deleteByUserIdAndRoleId(Long userId, Long roleId);
}
