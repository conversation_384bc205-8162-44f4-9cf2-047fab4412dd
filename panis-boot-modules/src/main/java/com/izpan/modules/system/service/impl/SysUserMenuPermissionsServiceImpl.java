/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.service.impl;

import com.izpan.modules.system.domain.bo.SysUserMenuPermissionsBO;
import com.izpan.modules.system.domain.entity.SysUserMenuPermissions;
import com.izpan.modules.system.repository.mapper.SysUserMenuPermissionsMapper;
import com.izpan.modules.system.service.ISysUserMenuPermissionsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * 用户菜单权限表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.service.impl.SysUserMenuPermissionsServiceImpl
 * @CreateTime 2024-12-13 - 22:21:55
 */

@Service
public class SysUserMenuPermissionsServiceImpl extends ServiceImpl<SysUserMenuPermissionsMapper, SysUserMenuPermissions> implements ISysUserMenuPermissionsService {

    @Override
    public IPage<SysUserMenuPermissions> listSysUserMenuPermissionsPage(PageQuery pageQuery, SysUserMenuPermissionsBO sysUserMenuPermissionsBO) {
        LambdaQueryWrapper<SysUserMenuPermissions> queryWrapper = new LambdaQueryWrapper<SysUserMenuPermissions>();
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

    @Override
    public List<SysUserMenuPermissions> getByUserId(Long userId) {
        LambdaQueryWrapper<SysUserMenuPermissions> queryWrapper = new LambdaQueryWrapper<SysUserMenuPermissions>();
        queryWrapper.eq(SysUserMenuPermissions::getUserId,userId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public boolean deleteByUserIdAndRoleId(Long userId, Long roleId) {
        return baseMapper.deleteByUserIdAndRoleId(userId,roleId) > 0;
    }

}

