package com.izpan.modules.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.izpan.infrastructure.domain.BaseVO;
import com.izpan.modules.system.domain.bo.SysRoleBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户管理 VO 类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.vo.SysUserVO
 * @CreateTime 2023/7/10 - 14:45
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysUserVO", description = "用户管理 VO 对象")
public class EvaluationUserVO extends BaseVO {

    @Serial
    private static final long serialVersionUID = -3621474283154332722L;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "性别 0保密 1男 2女 ")
    private String gender;

    /**
     * 学校编码
     */
    private String schoolId;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 年级编码
     */
    private String gradeId;
    /**
     * 年级名称
     */
    private String gradeName;
    /**
     * 班级编码
     */
    private String clazzId;
    /**
     * 班级名称
     */
    private String clazzName;

    @Schema(description = "学号")
    private String studentNum;

    @Schema(description = "测评次数")
    private Integer evaluationNumber;

    @Schema(description = "微课次数")
    private Integer microLessonsNumber;

    @Schema(description = "是否已匹配过作文")
    private Boolean isMatchComposition;
}
