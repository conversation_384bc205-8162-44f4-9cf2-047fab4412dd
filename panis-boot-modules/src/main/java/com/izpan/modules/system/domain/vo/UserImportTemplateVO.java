package com.izpan.modules.system.domain.vo;


import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentRowHeight;
import cn.idev.excel.annotation.write.style.HeadRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@ColumnWidth(20)  // 全局列宽
@HeadRowHeight(value = 20) // 头部行高
@ContentRowHeight(value = 18) // 内容行高
@ExcelIgnoreUnannotated // 忽略未注解的类
@Schema(description = "学生信息导入导出 VO 展示类")
public class UserImportTemplateVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8944710832771980786L;

    @ExcelProperty(value = "学号", index = 0)
    @Schema(description = "学号")
    private String studentNum;

    @ExcelProperty(value = "登录账号", index = 1)
    @Schema(description = "登录账号")
    private String userName;

    @ExcelProperty(value = "姓名", index = 2)
    @Schema(description = "姓名")
    private String realName;

    @ExcelProperty(value = "年级名称", index = 3)
    @Schema(description = "年级名称")
    private String gradeName;

    @ExcelProperty(value = "班级名称", index = 4)
    @Schema(description = "班级名称")
    private String clazzName;

}

