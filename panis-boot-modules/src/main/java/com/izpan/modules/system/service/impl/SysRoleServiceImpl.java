package com.izpan.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.exception.BizException;
import com.izpan.common.pool.StringPools;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.modules.system.domain.bo.SysRoleBO;
import com.izpan.modules.system.domain.entity.SysRole;
import com.izpan.modules.system.repository.mapper.SysRoleMapper;
import com.izpan.modules.system.service.ISysRoleService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 角色管理 Service 服务接口实现层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.service.impl.SysRoleServiceImpl
 * @CreateTime 2023-07-15
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {

    @Override
    public IPage<SysRole> listSysRolePage(PageQuery pageQuery, SysRoleBO sysRoleBO) {
        var queryWrapper = new LambdaQueryWrapper<SysRole>()
                .like(ObjectUtils.isNotEmpty(sysRoleBO.getRoleName()), SysRole::getRoleName, sysRoleBO.getRoleName())
                .like(ObjectUtils.isNotEmpty(sysRoleBO.getRoleCode()), SysRole::getRoleCode, sysRoleBO.getRoleCode())
                .eq(ObjectUtils.isNotEmpty(sysRoleBO.getStatus()), SysRole::getStatus, sysRoleBO.getStatus())
                .orderByAsc(SysRole::getSort);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
//        LoginUser loginUser = GlobalUserHolder.getUser();
//        // 用户如果是超级管理员，则只查询其创建德角色数据
//        if(StringPools.ADMIN.equals(loginUser.getUserName())){
//            var queryWrapper = new LambdaQueryWrapper<SysRole>()
//                    .like(ObjectUtils.isNotEmpty(sysRoleBO.getRoleName()), SysRole::getRoleName, sysRoleBO.getRoleName())
//                    .like(ObjectUtils.isNotEmpty(sysRoleBO.getRoleCode()), SysRole::getRoleCode, sysRoleBO.getRoleCode())
//                    .eq(ObjectUtils.isNotEmpty(sysRoleBO.getStatus()), SysRole::getStatus, sysRoleBO.getStatus())
//                    //添加学校编码条件
//                    .eq(ObjectUtils.isNotEmpty(loginUser.getUserName()), SysRole::getCreateUser, loginUser.getUserName())
//                    .orderByAsc(SysRole::getSort);
//            return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
//        }else{
//            // 获取当前用户的学校编码
//            String schoolId = loginUser.getSchoolId();
//            var queryWrapper = new LambdaQueryWrapper<SysRole>()
//                    .like(ObjectUtils.isNotEmpty(sysRoleBO.getRoleName()), SysRole::getRoleName, sysRoleBO.getRoleName())
//                    .like(ObjectUtils.isNotEmpty(sysRoleBO.getRoleCode()), SysRole::getRoleCode, sysRoleBO.getRoleCode())
//                    .eq(ObjectUtils.isNotEmpty(sysRoleBO.getStatus()), SysRole::getStatus, sysRoleBO.getStatus())
//                    //添加学校编码条件
//                    .eq(ObjectUtils.isNotEmpty(schoolId), SysRole::getSchoolId, schoolId)
//                    .orderByAsc(SysRole::getSort);
//            return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
//        }
    }

    @Override
    public boolean removeBatchByIds(Collection<?> list) {
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<SysRole>().in(SysRole::getId, list);
        baseMapper.selectList(queryWrapper)
                .stream().filter(sysRole -> StringPools.ADMIN.equalsIgnoreCase(sysRole.getRoleCode())).findFirst()
                .ifPresent(sysRole -> {
                    throw new BizException("系统管理员角色不允许删除");
                });
        return super.removeByIds(list, true);
    }

    @Override
    public List<SysRoleBO> queryAllRoleList() {
        // 获取当前登录用户
        LoginUser loginUser = GlobalUserHolder.getUser();
        // 获取当前用户的学校编码
        String schoolId = loginUser.getSchoolId();
        var queryWrapper = new LambdaQueryWrapper<SysRole>()
//                .eq(ObjectUtils.isNotEmpty(schoolId), SysRole::getSchoolId, schoolId)
                .orderByAsc(SysRole::getSort);
        return CglibUtil.convertList(baseMapper.selectList(queryWrapper), SysRoleBO::new);
    }

    @Override
    public List<SysRoleBO> querySchoolRoleList() {
        var queryWrapper = new LambdaQueryWrapper<SysRole>()
                .like(SysRole::getRoleCode, "SCHOOL-")
                .orderByAsc(SysRole::getSort);
        return CglibUtil.convertList(baseMapper.selectList(queryWrapper), SysRoleBO::new);
    }

    @Override
    public List<String> queryRoleCodesWithUserId(Long userId) {
        List<SysRole> sysRoles = baseMapper.queryRoleListWithUserId(userId);
        return sysRoles.stream().map(SysRole::getRoleCode).toList();
    }

    @Override
    public List<SysRoleBO> queryRoleListWithUserId(Long userId) {
        List<SysRole> sysRoles = baseMapper.queryRoleListWithUserId(userId);
        return CglibUtil.convertList(sysRoles, SysRoleBO::new);
    }
}
