package com.izpan.modules.system.domain.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户管理 - 批量更新年级班级 DTO 对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.user.SysUserBatchUpdateGradeClassDTO
 * @CreateTime 2024-12-19
 */
@Getter
@Setter
@Schema(name = "SysUserBatchUpdateGradeClassDTO", description = "用户管理 - 批量更新年级班级 DTO 对象")
public class SysUserBatchUpdateGradeClassDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID列表", required = true)
    @NotNull(message = "用户ID列表不能为空")
    @NotEmpty(message = "用户ID列表不能为空")
    private List<Long> userIds;

    @Schema(description = "年级ID", required = true)
    @NotNull(message = "年级ID不能为空")
    private String gradeId;

    @Schema(description = "年级名称", required = true)
    @NotNull(message = "年级名称不能为空")
    private String gradeName;

    @Schema(description = "班级ID", required = true)
    @NotNull(message = "班级ID不能为空")
    private String clazzId;

    @Schema(description = "班级名称", required = true)
    @NotNull(message = "班级名称不能为空")
    private String clazzName;
} 