/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.bo.SysMenuBO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuAddDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuDeleteDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuSearchDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuUpdateDTO;
import com.izpan.modules.system.domain.entity.SysMenu;
import com.izpan.modules.system.domain.entity.SysUserRoleMenu;
import com.izpan.modules.system.domain.vo.SysUserRoleMenuVO;

import java.util.List;

/**
 * 用户角色菜单 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.system.facade.ISysUserRoleMenuFacade
 * @CreateTime 2024-12-13 - 22:46:35
 */

public interface ISysUserRoleMenuFacade {

    /**
     * 用户角色菜单 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param sysUserRoleMenuSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:46:35
     */
    RPage<SysUserRoleMenuVO> listSysUserRoleMenuPage(PageQuery pageQuery, SysUserRoleMenuSearchDTO sysUserRoleMenuSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 用户角色菜单ID
     * @return {@link SysUserRoleMenuVO} 用户角色菜单 VO 对象
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:46:35
     */
    SysUserRoleMenuVO get(Long id);

    /**
     * 新增用户角色菜单
     *
     * @param sysUserRoleMenuAddDTO 新增用户角色菜单 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:46:35
     */
    boolean add(SysUserRoleMenuAddDTO sysUserRoleMenuAddDTO);

    /**
     * 编辑更新用户角色菜单信息
     *
     * @param sysUserRoleMenuUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:46:35
     */
    boolean update(SysUserRoleMenuUpdateDTO sysUserRoleMenuUpdateDTO);

    /**
     * 批量删除用户角色菜单信息
     *
     * @param sysUserRoleMenuDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:46:35
     */
    boolean batchDelete(SysUserRoleMenuDeleteDTO sysUserRoleMenuDeleteDTO);

    boolean batchAdd(List<SysUserRoleMenuAddDTO> sysUserRoleMenuAddDTO);

    List<SysUserRoleMenuVO> getUserId(Long userId);

    boolean deleteByUserId(Long userId,Long roleId) ;

    List<SysUserRoleMenu> queryRoleMenuList(Long userId, Long roleId);

    List<SysMenuBO> queryMenuListWithUserIdAndRoleId(Long userId, Long currentRoleId);
}