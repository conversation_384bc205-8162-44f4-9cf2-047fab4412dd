/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.facade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.bo.SysPermissionBO;
import com.izpan.modules.system.domain.bo.SysUserMenuPermissionsBO;
import com.izpan.modules.system.domain.bo.SysUserRoleBO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsAddDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsDeleteDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsSearchDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsUpdateDTO;
import com.izpan.modules.system.domain.entity.SysUserMenuPermissions;
import com.izpan.modules.system.domain.vo.SysUserMenuPermissionsVO;
import com.izpan.modules.system.facade.ISysUserMenuPermissionsFacade;
import com.izpan.modules.system.service.ISysPermissionService;
import com.izpan.modules.system.service.ISysUserMenuPermissionsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 用户菜单权限表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.system.facade.impl.SysUserMenuPermissionsFacadeImpl
 * @CreateTime 2024-12-13 - 22:21:55
 */

@Service
@RequiredArgsConstructor
public class SysUserMenuPermissionsFacadeImpl implements ISysUserMenuPermissionsFacade {

    @NonNull
    private ISysUserMenuPermissionsService sysUserMenuPermissionsService;

    @NonNull
    private ISysPermissionService sysPermissionService;
    @Override
    public RPage<SysUserMenuPermissionsVO> listSysUserMenuPermissionsPage(PageQuery pageQuery, SysUserMenuPermissionsSearchDTO sysUserMenuPermissionsSearchDTO) {
        SysUserMenuPermissionsBO sysUserMenuPermissionsBO = CglibUtil.convertObj(sysUserMenuPermissionsSearchDTO, SysUserMenuPermissionsBO::new);
        IPage<SysUserMenuPermissions> sysUserMenuPermissionsIPage = sysUserMenuPermissionsService.listSysUserMenuPermissionsPage(pageQuery, sysUserMenuPermissionsBO);
        return RPage.build(sysUserMenuPermissionsIPage, SysUserMenuPermissionsVO::new);
    }

    @Override
    public SysUserMenuPermissionsVO get(Long id) {
        SysUserMenuPermissions byId = sysUserMenuPermissionsService.getById(id);
        return CglibUtil.convertObj(byId, SysUserMenuPermissionsVO::new);
    }

    @Override
    @Transactional
    public boolean add(SysUserMenuPermissionsAddDTO sysUserMenuPermissionsAddDTO) {
        SysUserMenuPermissionsBO sysUserMenuPermissionsBO = CglibUtil.convertObj(sysUserMenuPermissionsAddDTO, SysUserMenuPermissionsBO::new);
        return sysUserMenuPermissionsService.save(sysUserMenuPermissionsBO);
    }

    @Override
    @Transactional
    public boolean update(SysUserMenuPermissionsUpdateDTO sysUserMenuPermissionsUpdateDTO) {
        SysUserMenuPermissionsBO sysUserMenuPermissionsBO = CglibUtil.convertObj(sysUserMenuPermissionsUpdateDTO, SysUserMenuPermissionsBO::new);
        return sysUserMenuPermissionsService.updateById(sysUserMenuPermissionsBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(SysUserMenuPermissionsDeleteDTO sysUserMenuPermissionsDeleteDTO) {
        SysUserMenuPermissionsBO sysUserMenuPermissionsBO = CglibUtil.convertObj(sysUserMenuPermissionsDeleteDTO, SysUserMenuPermissionsBO::new);
        return sysUserMenuPermissionsService.removeBatchByIds(sysUserMenuPermissionsBO.getIds(), true);
    }

    /**
     * 添加用户菜单下权限
     * @param sysUserMenuPermissionsAddDTOs
     * @return
     */
    @Override
    @Transactional
    public boolean batchAdd(List<SysUserMenuPermissionsAddDTO> sysUserMenuPermissionsAddDTOs) {
        List<SysUserMenuPermissions> sysUserMenuPermissionsList = CglibUtil.convertList(sysUserMenuPermissionsAddDTOs, SysUserMenuPermissions::new);
        return sysUserMenuPermissionsService.saveOrUpdateBatch(sysUserMenuPermissionsList);
    }

    @Override
    public List<SysUserMenuPermissionsVO> getByUserId(Long userId) {
        List<SysUserMenuPermissions> userMenuPermissionsVOS = sysUserMenuPermissionsService.getByUserId(userId);
        return CglibUtil.convertList(userMenuPermissionsVOS, SysUserMenuPermissionsVO::new);
    }

    @Override
    public boolean deleteByUserId(Long userId,Long roleId) {
        return sysUserMenuPermissionsService.deleteByUserIdAndRoleId(userId,roleId);
    }

    @Override
    public List<SysUserMenuPermissions> queryMenuPermission(Long userId,Long roleId, List<Long> menuIdList) {
        LambdaQueryWrapper<SysUserMenuPermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserMenuPermissions::getUserId,userId)
                .eq(SysUserMenuPermissions::getRoleId,roleId)
                .in(SysUserMenuPermissions::getMenuId,menuIdList);
        return sysUserMenuPermissionsService.list(queryWrapper);
    }

    @Override
    public List<SysPermissionBO> queryPermissionListWithUserIdAndMenuId(Long userId, Long  roleId,List<Long> menuIdList) {
        return sysPermissionService.queryPermissionListWithUserIdAndMenuId(userId,roleId,menuIdList);
    }

}