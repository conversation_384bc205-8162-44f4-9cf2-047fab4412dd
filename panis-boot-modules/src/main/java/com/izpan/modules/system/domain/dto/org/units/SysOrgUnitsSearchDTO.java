/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.domain.dto.org.units;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 年级/班级管理 查询 DTO 对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsSearchDTO
 * @CreateTime 2024-07-16 - 16:35:30
 */

@Getter
@Setter
@Schema(name = "SysOrgUnitsSearchDTO", description = "年级/班级管理 查询 DTO 对象")
public class SysOrgUnitsSearchDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4532115765293175050L;

    @Schema(description = "年级/班级名称")
    private String name;

    @Schema(description = "是否启用(0:禁用,1:启用)")
    private String status;

    @Schema(description = "学校ID")
    private String schoolId;

}