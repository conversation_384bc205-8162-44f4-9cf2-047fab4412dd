/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.facade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.bo.SysMenuBO;
import com.izpan.modules.system.domain.bo.SysUserRoleMenuBO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuAddDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuDeleteDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuSearchDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuUpdateDTO;
import com.izpan.modules.system.domain.entity.SysMenu;
import com.izpan.modules.system.domain.entity.SysUserRoleMenu;
import com.izpan.modules.system.domain.vo.SysUserRoleMenuVO;
import com.izpan.modules.system.facade.ISysUserRoleMenuFacade;
import com.izpan.modules.system.service.ISysMenuService;
import com.izpan.modules.system.service.ISysUserRoleMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 用户角色菜单 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.system.facade.impl.SysUserRoleMenuFacadeImpl
 * @CreateTime 2024-12-13 - 22:46:35
 */

@Service
@RequiredArgsConstructor
public class SysUserRoleMenuFacadeImpl implements ISysUserRoleMenuFacade {

    @NonNull
    private ISysUserRoleMenuService sysUserRoleMenuService;
    @NonNull
    private ISysMenuService sysMenuService;

    @Override
    public RPage<SysUserRoleMenuVO> listSysUserRoleMenuPage(PageQuery pageQuery, SysUserRoleMenuSearchDTO sysUserRoleMenuSearchDTO) {
        SysUserRoleMenuBO sysUserRoleMenuBO = CglibUtil.convertObj(sysUserRoleMenuSearchDTO, SysUserRoleMenuBO::new);
        IPage<SysUserRoleMenu> sysUserRoleMenuIPage = sysUserRoleMenuService.listSysUserRoleMenuPage(pageQuery, sysUserRoleMenuBO);
        return RPage.build(sysUserRoleMenuIPage, SysUserRoleMenuVO::new);
    }

    @Override
    public SysUserRoleMenuVO get(Long id) {
        SysUserRoleMenu byId = sysUserRoleMenuService.getById(id);
        return CglibUtil.convertObj(byId, SysUserRoleMenuVO::new);
    }

    @Override
    @Transactional
    public boolean add(SysUserRoleMenuAddDTO sysUserRoleMenuAddDTO) {
        SysUserRoleMenuBO sysUserRoleMenuBO = CglibUtil.convertObj(sysUserRoleMenuAddDTO, SysUserRoleMenuBO::new);
        return sysUserRoleMenuService.save(sysUserRoleMenuBO);
    }

    @Override
    @Transactional
    public boolean update(SysUserRoleMenuUpdateDTO sysUserRoleMenuUpdateDTO) {
        SysUserRoleMenuBO sysUserRoleMenuBO = CglibUtil.convertObj(sysUserRoleMenuUpdateDTO, SysUserRoleMenuBO::new);
        return sysUserRoleMenuService.updateById(sysUserRoleMenuBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(SysUserRoleMenuDeleteDTO sysUserRoleMenuDeleteDTO) {
        SysUserRoleMenuBO sysUserRoleMenuBO = CglibUtil.convertObj(sysUserRoleMenuDeleteDTO, SysUserRoleMenuBO::new);
        return sysUserRoleMenuService.removeBatchByIds(sysUserRoleMenuBO.getIds(), true);
    }

    /**
     * 添加用户的角色下的菜单
     * @param sysUserRoleMenuAddDTOs
     * @return
     */
    @Override
    @Transactional
    public boolean batchAdd(List<SysUserRoleMenuAddDTO> sysUserRoleMenuAddDTOs) {
        List<SysUserRoleMenu> sysUserRoleMenus = CglibUtil.convertList(sysUserRoleMenuAddDTOs, SysUserRoleMenu::new);
        return sysUserRoleMenuService.saveOrUpdateBatch(sysUserRoleMenus);
    }

    @Override
    public List<SysUserRoleMenuVO> getUserId(Long userId) {
        List<SysUserRoleMenu> sysUserRoleMenuList = sysUserRoleMenuService.getByUserId(userId);
        return CglibUtil.convertList(sysUserRoleMenuList, SysUserRoleMenuVO::new);
    }

    @Override
    public boolean deleteByUserId(Long userId,Long roleId) {
        LambdaQueryWrapper<SysUserRoleMenu> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRoleMenu::getUserId,userId)
                .eq(SysUserRoleMenu::getRoleId,roleId);
        return sysUserRoleMenuService.deleteByUserIdAnsRoleId(userId,roleId);
    }

    @Override
    public List<SysUserRoleMenu> queryRoleMenuList(Long userId, Long roleId) {
        LambdaQueryWrapper<SysUserRoleMenu> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRoleMenu::getUserId,userId).eq(SysUserRoleMenu::getRoleId,roleId);
        return sysUserRoleMenuService.list(queryWrapper);
    }

    @Override
    public List<SysMenuBO> queryMenuListWithUserIdAndRoleId(Long userId, Long currentRoleId) {
        return CglibUtil.convertList(sysMenuService.queryMenuListWithUserIdAndRoleId(userId,currentRoleId),SysMenuBO::new);
    }

}