package com.izpan.modules.system.domain.dto.user;

import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户注册 DTO 对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.user.SysUserRegisterDTO
 * @CreateTime 2024/1/1 - 00:00
 */
@Getter
@Setter
public class SysUserRegisterDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "账号不能为空")
    private String userName;

    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "昵称不能为空")
    private String nickName;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "用户类型", defaultValue = "5")
    private String userType = SystemUserTypeEnum.USER.getValue();
}
