package com.izpan.modules.system.domain.vo;

import com.izpan.infrastructure.domain.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 用户角色管理 VO 展示类
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.vo.SysUserRoleVO
 * @CreateTime 2023-07-24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysUserRoleVO", description = "用户角色管理 VO 对象")
public class SysUserRoleVO extends BaseVO {

    @Serial
    private static final long serialVersionUID = -2186931674170719275L;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "是否启用(0:禁用,1:启用)")
    private String status;
}