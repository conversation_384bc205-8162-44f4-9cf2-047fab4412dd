package com.izpan.modules.system.domain.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户导入DTO
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.user.UserImportDTO
 * @CreateTime 2024/3/2
 */
@Data
@Schema(description = "用户导入DTO")
public class UserImportDTO {

    @Schema(description = "导入文件")
    private MultipartFile file;

    @Schema(description = "年级编码")
    private String gradeId;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "班级编码")
    private String clazzId;

    @Schema(description = "班级名称")
    private String clazzName;
} 