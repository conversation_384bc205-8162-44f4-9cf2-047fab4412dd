package com.izpan.modules.system.domain.vo;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 菜单权限数据结构
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SysRoleMenuPermissionVO {
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 角色名称
     */
    private String roleName;

    private List<MenuVo> menuVoList;

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuVo {
        /**
         * 菜单ID
         */
        private Long menuId;
        /**
         * 菜单名称
         */
        private String menuName;

        private Integer order;
        // 是否选中0未选中 1 选中
        private String isChecked="0";

        List<MenuVo> children;

        List<PermissionVo> permissionList;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static  class PermissionVo{
        /**
         * 权限ID
         */
        private Long permissionId;
        /**
         * 权限名称
         */
        private String permissionName;
        // 是否选中:0未选中 1 选中
        private String isChecked="0";
    }
}
