package com.izpan.modules.system.domain.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 个人资料修改DTO对象
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.domain.dto.SysUserInfoUpdateDTO
 * @CreateTime 2023/12/19 - 22:40
 */

@Getter
@Setter
public class SysUserInfoUpdateDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1468612040566150394L;

    private Long id;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "真名")
    private String realName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机")
    private String phone;

    @Schema(description = "性别 0保密 1男 2女")
    private String gender;
    /**
     * 学校编码
     */
    private  String schoolId;
    /**
     * 学校名称
     */
    private  String schoolName;
    /**
     * 年级编码
     */
    private String gradeId;
    /**
     * 年级名称
     */
    private String gradeName;
    /**
     * 班级编码
     */
    private String clazzId;
    /**
     * 班级名称
     */
    private String clazzName;
    /**
     * 用户类别（0:超级管理员 1:管理员 2:教师 3:用户）
     */
    private String userType="2";
    /**
     * 学生标识:(1:小学 2:初中 3:高中)
     */
    private String studentFlag;
}
