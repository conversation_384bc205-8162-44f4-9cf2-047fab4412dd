/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.pool.StringPools;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.enums.PositionIdentifyEnum;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.infrastructure.util.SchoolUtil;
import com.izpan.modules.base.service.IBseSchoolService;
import com.izpan.modules.system.domain.bo.SysOrgUnitsBO;
import com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsAddDTO;
import com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsDeleteDTO;
import com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsSearchDTO;
import com.izpan.modules.system.domain.dto.org.units.SysOrgUnitsUpdateDTO;
import com.izpan.modules.system.domain.entity.SysOrgUnits;
import com.izpan.modules.system.domain.vo.SysOrgUnitsTreeVO;
import com.izpan.modules.system.domain.vo.SysOrgUnitsVO;
import com.izpan.modules.system.domain.vo.SysOrgVO;
import com.izpan.modules.system.facade.ISysOrgUnitsFacade;
import com.izpan.modules.system.service.ISysOrgUnitsService;
import com.izpan.modules.system.service.ISysRoleService;
import com.izpan.modules.system.service.ISysUserService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 年级/班级管理 门面接口实现层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.facade.impl.SysOrgUnitsFacadeImpl
 * @CreateTime 2024-07-16 - 16:35:30
 */

@Service
@RequiredArgsConstructor
public class SysOrgUnitsFacadeImpl implements ISysOrgUnitsFacade {

    @NonNull
    private ISysOrgUnitsService sysOrgUnitsService;

    @NonNull
    private ISysRoleService sysRoleService;

    @NonNull
    private ISysUserService sysUserService;

    @NonNull
    private IBseSchoolService bseSchoolService;

    /**
     * 初始化组织单位子单位
     *
     * @param parentId    父id
     * @param orgUnitsMap 所有单位数据 Map 结构
     * @return {@link List }<{@link SysOrgUnitsTreeVO }> 子组织集合
     * <AUTHOR>
     * @CreateTime 2024-07-16 - 22:03:21
     */
    private static List<SysOrgUnitsTreeVO> initOrgUnitsChild(Long parentId, Map<Long, List<SysOrgUnits>> orgUnitsMap, SysOrgVO parent) {
        // 获取子单位
        List<SysOrgUnits> childOrgUnits = orgUnitsMap.get(parentId);
        if (CollectionUtils.isEmpty(childOrgUnits)) {
            return Collections.emptyList();
        }
        // 递归初始化子单位
        return childOrgUnits.stream()
                .map(unit -> {
                    SysOrgUnitsTreeVO orgUnitsTreeVO = CglibUtil.convertObj(unit, SysOrgUnitsTreeVO::new);
                    orgUnitsTreeVO.setParent(parent);
                    SysOrgVO orgVO = CglibUtil.convertObj(unit, SysOrgVO::new);
                    if (parent != null && parent.getName() != null) {
                        orgUnitsTreeVO.setAllName(parent.getName() + "_" + orgVO.getName());
                        orgUnitsTreeVO.setAllCode(parent.getId() + "_" + orgVO.getId());
                    } else {
                        orgUnitsTreeVO.setAllName(orgVO.getName());
                        orgUnitsTreeVO.setAllCode(orgVO.getId().toString());
                    }
                    orgUnitsTreeVO.setChildren(initOrgUnitsChild(unit.getId(), orgUnitsMap, orgVO));
                    return orgUnitsTreeVO;
                })
                .sorted(Comparator.comparing(SysOrgUnitsTreeVO::getSort))
                .toList();
    }

    @Override
    public RPage<SysOrgUnitsTreeVO> listSysOrgUnitsPage(PageQuery pageQuery, SysOrgUnitsSearchDTO sysOrgUnitsSearchDTO) {
        SchoolUtil.setSchoolId(sysOrgUnitsSearchDTO.getSchoolId(), sysOrgUnitsSearchDTO);
        SysOrgUnitsBO sysOrgUnitsBO = CglibUtil.convertObj(sysOrgUnitsSearchDTO, SysOrgUnitsBO::new);
        IPage<SysOrgUnits> sysOrgUnitsIPage = sysOrgUnitsService.listSysOrgUnitsPage(pageQuery, sysOrgUnitsBO);
        List<SysOrgUnits> topOrgUnits = sysOrgUnitsIPage.getRecords();
        if (topOrgUnits.isEmpty()) {
            return RPage.build(sysOrgUnitsIPage, SysOrgUnitsTreeVO::new);
        }
        // 查询所有数据
        List<Long> topLevelIds = topOrgUnits.stream().map(SysOrgUnits::getId).toList();
        List<SysOrgUnits> allOrgUnits = sysOrgUnitsService.listAllDescendants(topLevelIds);
        // 按 parentId 分组
        Map<Long, List<SysOrgUnits>> orgUnitsMap = allOrgUnits.stream()
                .collect(Collectors.groupingBy(SysOrgUnits::getParentId));
        // 初始化顶级单位的子单位
        List<SysOrgUnitsTreeVO> topOrgUnitsTreeVOList = topOrgUnits.stream()
                .map(unit -> {
                    SysOrgUnitsTreeVO orgUnitsTreeVO = CglibUtil.convertObj(unit, SysOrgUnitsTreeVO::new);
                    orgUnitsTreeVO.setChildren(initOrgUnitsChild(unit.getId(), orgUnitsMap, null));
                    return orgUnitsTreeVO;
                }).toList();

        RPage<SysOrgUnitsTreeVO> build = RPage.build(sysOrgUnitsIPage, SysOrgUnitsTreeVO::new);
        build.setRecords(topOrgUnitsTreeVOList);
        return build;
    }

    @Override
    public SysOrgUnitsVO get(Long id) {
        SysOrgUnits byId = sysOrgUnitsService.getById(id);
        return CglibUtil.convertObj(byId, SysOrgUnitsVO::new);
    }

    @Override
    @Transactional
    public Long add(SysOrgUnitsAddDTO sysOrgUnitsAddDTO) {
        SysOrgUnitsBO sysOrgUnitsBO = CglibUtil.convertObj(sysOrgUnitsAddDTO, SysOrgUnitsBO::new);
        if (StringUtils.isEmpty(sysOrgUnitsBO.getSchoolId())) {
            SchoolUtil.setSchoolInfo(sysOrgUnitsBO);
        }
        sysOrgUnitsService.save(sysOrgUnitsBO);
        return sysOrgUnitsBO.getId();
    }

    @Override
    @Transactional
    public boolean update(SysOrgUnitsUpdateDTO sysOrgUnitsUpdateDTO) {
        SysOrgUnitsBO sysOrgUnitsBO = CglibUtil.convertObj(sysOrgUnitsUpdateDTO, SysOrgUnitsBO::new);
        SchoolUtil.setSchoolInfo(sysOrgUnitsBO);
        return sysOrgUnitsService.updateById(sysOrgUnitsBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(SysOrgUnitsDeleteDTO sysOrgUnitsDeleteDTO) {
        SysOrgUnitsBO sysOrgUnitsBO = CglibUtil.convertObj(sysOrgUnitsDeleteDTO, SysOrgUnitsBO::new);
        return sysOrgUnitsService.removeBatchByIds(sysOrgUnitsBO.getIds(), true);
    }

    @Override
    public List<SysOrgUnitsVO> getGradeList() {
        // 获取学校ID
        String schoolId = SchoolUtil.getSchoolId();

        // 查询年级单位信息
        LambdaQueryWrapper<SysOrgUnits> queryWrapper = new LambdaQueryWrapper<SysOrgUnits>()
                .eq(SysOrgUnits::getSchoolId, schoolId)
                .eq(SysOrgUnits::getParentId, StringPools.ZERO);
        List<SysOrgUnits> gradeUnits = sysOrgUnitsService.list(queryWrapper);

        return gradeUnits.stream()
                .map(unit -> CglibUtil.convertObj(unit, SysOrgUnitsVO::new))
                .toList();
    }

    @Override
    public List<SysOrgUnitsTreeVO> queryAllOrgUnitsListConvertToTree(String schoolId) {
        String finalSchoolId = SchoolUtil.getSchoolId(schoolId);
        List<SysOrgUnits> allOrgUnits = sysOrgUnitsService.querySysOrgUnitsListWithStatus(StringPools.ONE, finalSchoolId);
        Map<Long, List<SysOrgUnits>> orgUnitsMap = allOrgUnits.stream()
                .collect(Collectors.groupingBy(SysOrgUnits::getParentId));
        return initOrgUnitsChild(0L, orgUnitsMap, null);
    }

    @Override
    public List<SysOrgUnitsTreeVO> queryAllOrgUnitsListConvertToTree() {

        String schoolId = SchoolUtil.getSchoolId();

        LoginUser loginUser = GlobalUserHolder.getUser();

        // 根据职位标识处理不同角色的组织单位树
        PositionIdentifyEnum positionIdentify = PositionIdentifyEnum.getByValue(loginUser.getPositionIdentify());
        if (positionIdentify == null) {
            return Collections.emptyList();
        }

        switch (positionIdentify) {
            case PRINCIPAL:
            case VICE_PRINCIPAL:
                return querySchoolOrgUnitsTree(schoolId);
            case GRADE_DIRECTOR:
                return queryGradeDirectorOrgUnitsTree(schoolId, loginUser.getGradeId());
            case TEACHER:
                return queryTeacherOrgUnitsTree(schoolId, loginUser.getClazzId());
            default:
                return Collections.emptyList();
        }
    }

    /**
     * 查询校长/副校长可见的组织单位树
     */
    private List<SysOrgUnitsTreeVO> querySchoolOrgUnitsTree(String schoolId) {
        return queryAllOrgUnitsListConvertToTree(schoolId);
    }

    /**
     * 查询年级主任可见的组织单位树
     * 年级主任只能看到本年级和本年级下的所有班级
     */
    private List<SysOrgUnitsTreeVO> queryGradeDirectorOrgUnitsTree(String schoolId, String gradeId) {
        if (StrUtil.isEmpty(gradeId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SysOrgUnits> classQueryWrapper = new LambdaQueryWrapper<SysOrgUnits>()
                .eq(SysOrgUnits::getSchoolId, schoolId).and(wrapper -> wrapper.eq(SysOrgUnits::getParentId, gradeId).or().eq(SysOrgUnits::getId, gradeId));
        List<SysOrgUnits> classes = sysOrgUnitsService.list(classQueryWrapper);

        Map<Long, List<SysOrgUnits>> orgUnitsMap = classes.stream()
                .collect(Collectors.groupingBy(SysOrgUnits::getParentId));

        return initOrgUnitsChild(0L, orgUnitsMap, null);
    }

    /**
     * 查询教师可见的组织单位树
     */
    private List<SysOrgUnitsTreeVO> queryTeacherOrgUnitsTree(String schoolId, String clazzId) {
        if (StrUtil.isEmpty(clazzId)) {
            return Collections.emptyList();
        }

        // 解析班级ID列表
        Set<String> unitCodes = parseTeacherClazzIds(clazzId);
        if (unitCodes.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询班级相关的组织单位
        LambdaQueryWrapper<SysOrgUnits> queryWrapper = new LambdaQueryWrapper<SysOrgUnits>()
                .eq(SysOrgUnits::getSchoolId, schoolId)
                .in(SysOrgUnits::getId, unitCodes);
        List<SysOrgUnits> units = sysOrgUnitsService.list(queryWrapper);

        if (CollUtil.isEmpty(units)) {
            return Collections.emptyList();
        }

        // 构建组织单位树
        Map<Long, List<SysOrgUnits>> orgUnitsMap = units.stream()
                .collect(Collectors.groupingBy(SysOrgUnits::getParentId));

        // 这里使用0L作为根节点，可能需要根据实际情况调整
        return initOrgUnitsChild(0L, orgUnitsMap, null);
    }

    /**
     * 解析教师班级ID
     * 格式：gradeCode_clazzCode,gradeCode_clazzCode,...
     * 返回所有年级代码和班级代码
     */
    private Set<String> parseTeacherClazzIds(String clazzId) {
        Set<String> unitCodes = new HashSet<>();

        // 按逗号分割多个班级
        String[] clazzArray = clazzId.split(",");
        for (String clazz : clazzArray) {
            // 按下划线分割年级和班级代码
            String[] parts = clazz.split("_");
            if (parts.length == 2) {
                unitCodes.add(parts[0]); // 年级代码
                unitCodes.add(parts[1]); // 班级代码
            }
        }

        return unitCodes;
    }

    @Override
    public SysOrgUnits querySysOrgUnitsByName(String name, Long schoolId) {
        return sysOrgUnitsService.getOne(new LambdaQueryWrapper<SysOrgUnits>()
                .eq(SysOrgUnits::getName, name)
                .eq(SysOrgUnits::getSchoolId, schoolId)
                .last("limit 1")
        );
    }
}