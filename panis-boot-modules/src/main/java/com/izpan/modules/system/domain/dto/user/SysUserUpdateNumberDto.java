package com.izpan.modules.system.domain.dto.user;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;


@Getter
@Setter
public class SysUserUpdateNumberDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3682144343909390429L;

    @Schema(description = "用户ID, 可不传默认当前用户")
    private Long id;

    @Schema(description = "操作类型： add 增加，sub 减少")
    private String type;// 1: add, 2: sub

    @Schema(description = "操作值类型： microLessonsNumber evaluationNumber")
    private String rowType; // microLessonsNumber evaluationNumber

    @Schema(description = "操作数量: 默认1 。充值多少次填写多少次")
    private Integer numbers = 1;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "业务ID,比如微课ID,消费时传入")
    private String businessId;

}

