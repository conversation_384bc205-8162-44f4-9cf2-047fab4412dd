<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.izpan.modules.system.repository.mapper.SysUserMapper">

    <resultMap id="SysUserResultMap" type="com.izpan.modules.system.domain.entity.SysUser">
        <id column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="nick_name" property="nickName"/>
        <result column="real_name" property="realName"/>
        <result column="avatar" property="avatar"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="gender" property="gender"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="updateTime" property="updateTime"/>
        <result column="salt" property="salt"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="update_password_time" property="updatePasswordTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="deleted"/>
        <result column="school_id" property="schoolId"/>
        <result column="school_name" property="schoolName"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="clazz_id" property="clazzId"/>
        <result column="clazz_name" property="clazzName"/>
        <result column="user_type" property="userType"/>
        <result column="student_num" property="studentNum"/>
        <result column="micro_lessons_number" property="microLessonsNumber"/>
        <result column="evaluation_number" property="evaluationNumber"/>
    </resultMap>

    <sql id="UserColumnList">
        su.id,
        su.user_name,
        su.password,
        su.nick_name,
        su.real_name,
        su.avatar,
        su.email,
        su.phone,
        su.gender,
        su.status,
        su.salt,
        su.last_login_time,
        su.update_password_time,
        su.create_user,
        su.create_user_id,
        su.create_time,
        su.update_user,
        su.update_user_id,
        su.update_time,
        su.school_id,
        su.school_name,
        su.grade_id,
        su.grade_name,
        su.clazz_id,
        su.clazz_name,
        su.user_type,
        su.student_num,
        su.micro_lessons_number,
        su.evaluation_number
    </sql>

    <select id="getUserByUserName" resultMap="SysUserResultMap">
        SELECT <include refid="UserColumnList" />
        FROM sys_user su
        WHERE su.user_name = #{userName}
          AND su.is_deleted = 0
    </select>

    <select id="queryIsContainAdmin" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM sys_user su
        WHERE su.is_deleted = 0
        AND su.status = 1
        AND su.user_name = 'admin'
        AND su.id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="listSysUserPage" resultMap="SysUserResultMap">
        SELECT
            <include refid="UserColumnList" />
        FROM
            sys_user su
        <where>
            <if test="bo.userType != null and bo.userType != ''">
                AND su.user_type = #{bo.userType}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                AND su.user_name LIKE CONCAT(#{bo.userName}, '%')
            </if>
            <if test="bo.realName != null and bo.realName != ''">
                AND su.real_name LIKE CONCAT('%',#{bo.realName}, '%')
            </if>
            <if test="bo.email != null and bo.email != ''">
                AND su.email LIKE CONCAT(#{bo.email}, '%')
            </if>
            <if test="bo.schoolId != null and bo.schoolId != ''">
                AND su.school_id=#{bo.schoolId}
            </if>
            <if test="bo.gradeId != null and bo.gradeId != ''">
                AND su.grade_id LIKE CONCAT(#{bo.gradeId}, '%')
            </if>
            <if test="bo.clazzId != null and bo.clazzId != ''">
                AND su.clazz_id LIKE CONCAT('%',#{bo.clazzId}, '%')
            </if>
            AND su.is_deleted = 0
        </where>

    </select>


</mapper>
