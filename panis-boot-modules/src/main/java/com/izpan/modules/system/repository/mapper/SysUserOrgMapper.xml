<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.system.repository.mapper.SysUserOrgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="SysUserOrgResultMap" type="com.izpan.modules.system.domain.entity.SysUserOrg">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="org_id" property="orgId"/>
        <result column="principal" property="principal"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="SysUserOrgColumnList">
        id,
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        is_deleted,
        user_id, org_id, principal
    </sql>

    <select id="listUserOrgWithUserId" resultMap="SysUserOrgResultMap">
        SELECT suo.*
        FROM sys_user_org suo
                 INNER JOIN sys_org_units sou ON sou.id = suo.org_id
            AND sou.is_deleted = 0
            AND sou.status = 1
        WHERE suo.is_deleted = 0
          AND suo.user_id = #{userId}
    </select>

    <update id="updatePrincipal">
        UPDATE sys_user_org suo SET suo.principal = 0 WHERE suo.user_id = #{userId} AND suo.is_deleted = 0;
        <if test="orgIds != null and orgIds.size() > 0">
            UPDATE sys_user_org suo
            SET suo.principal = 1
            WHERE suo.user_id = #{userId}
            AND suo.is_deleted = 0
            AND suo.org_id IN
            <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
    </update>

</mapper>
