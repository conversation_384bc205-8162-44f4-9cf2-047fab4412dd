package com.izpan.modules.system.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.dto.user.*;
import com.izpan.modules.system.domain.vo.*;

import java.util.List;

/**
 * 用户管理 门面接口层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.facade.ISysUserFacade
 * @CreateTime 2023/7/6 - 16:06
 */
public interface ISysUserFacade {

    /**
     * 用户管理 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param sysUserSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2023-07-13 15:10
     */
    RPage<SysUserVO> listSysUserPage(PageQuery pageQuery, SysUserSearchDTO sysUserSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 用户ID
     * @return {@link SysUserVO} 用户个人信息 VO 对象
     * <AUTHOR>
     * @CreateTime 2023-07-15 16:33
     */
    SysUserVO get(Long id);

    /**
     * 新增用户
     *
     * @param sysUserAddDTO 新增用户 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2023-07-17 17:26
     */
    Long addUser(SysUserAddDTO sysUserAddDTO);

    /**
     * 编辑更新用户信息
     *
     * @param sysUserUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2023-07-17 17:27
     */
    boolean updateUser(SysUserUpdateDTO sysUserUpdateDTO);

    /**
     * 批量删除用户信息
     *
     * @param sysUserDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2023-07-17 17:27
     */
    boolean batchDeleteUser(SysUserDeleteDTO sysUserDeleteDTO);


    /**
     * 根据用户ID进行重置密码，并返回加密密码
     *
     * @param userId 用户 Id
     * @return {@link String} 加密后的密码
     * <AUTHOR>
     * @CreateTime 2023-12-18 22:04
     */
    String resetPassword(Long userId);

    /**
     * 根据用户ID查询用户的职责信息
     *
     * @param userId 用户id
     * @return {@link SysUserResponsibilitiesVO } 用户职责信息 VO 对象
     * <AUTHOR>
     * @CreateTime 2024-07-20 - 15:28:23
     */
    SysUserResponsibilitiesVO queryUserResponsibilitiesWithUserId(Long userId);

    /**
     * 更新用户职责信息
     *
     * @param updateDTO 更新dto
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-07-20 - 17:08:03
     */
    boolean updateUserResponsibilities(SysUserResponsibilitiesUpdateDTO updateDTO);

    /**
     * 导入用户数据
     *
     * @param list 输入流
     * @return 导入结果，包含成功和失败的数量
     * <AUTHOR>
     * @CreateTime 2024-03-02
     */
    ImportResult importUsers(List<UserImportTemplateVO> list);

    /**
     * 移除用户
     *
     * @param userIds 用户ID
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-21
     */
    boolean removeUser(SysUserDeleteDTO userIds);

    /**
     * 更新用户计数
     * 通用计数更新方法，支持多种类型的计数字段
     *
     * @param dto 包含更新信息的DTO
     * @return 更新后的计数值
     */
    Integer updateTimes(SysUserUpdateNumberDto dto);

    /**
     * 根据年级、班级查询学生或老师
     *
     * @param sysUserSearchDTO 查询对象
     * @return {@link List} 查询结果
     */
    List<EvaluationUserVO> queryByGradeAndClass(EvaluationUserSearchDTO sysUserSearchDTO);

    /**
     * 批量更新学生年级班级信息
     *
     * @param batchUpdateDTO 批量更新 DTO 对象
     * <AUTHOR>
     * @CreateTime 2024-12-19
     */
    boolean batchUpdateGradeClass(SysUserBatchUpdateGradeClassDTO batchUpdateDTO);
}
