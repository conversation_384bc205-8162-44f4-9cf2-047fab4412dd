package com.izpan.modules.system.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.izpan.common.pool.StringPools;
import com.izpan.common.util.CglibUtil;
import com.izpan.common.util.CollectionUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.infrastructure.util.GsonUtil;
import com.izpan.modules.system.domain.bo.SysMenuBO;
import com.izpan.modules.system.domain.bo.SysPermissionBO;
import com.izpan.modules.system.domain.bo.SysRoleMenuBO;
import com.izpan.modules.system.domain.dto.menu.SysUserRouteVO;
import com.izpan.modules.system.domain.dto.role.menu.SysRoleMenuAddDTO;
import com.izpan.modules.system.domain.dto.role.menu.SysRoleMenuDeleteDTO;
import com.izpan.modules.system.domain.dto.role.menu.SysRoleMenuSearchDTO;
import com.izpan.modules.system.domain.dto.role.menu.SysRoleMenuUpdateDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsAddDTO;
import com.izpan.modules.system.domain.dto.user.role.menu.SysUserRoleMenuAddDTO;
import com.izpan.modules.system.domain.entity.*;
import com.izpan.modules.system.domain.vo.SysPermissionVO;
import com.izpan.modules.system.domain.vo.SysRoleMenuPermissionVO;
import com.izpan.modules.system.domain.vo.SysRoleMenuVO;
import com.izpan.modules.system.facade.ISysRoleMenuFacade;
import com.izpan.modules.system.facade.ISysUserMenuPermissionsFacade;
import com.izpan.modules.system.facade.ISysUserRoleMenuFacade;
import com.izpan.modules.system.service.ISysRoleMenuService;
import com.izpan.modules.system.service.ISysRolePermissionService;
import com.izpan.modules.system.service.ISysRoleService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色菜单管理 门面接口实现层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.facade.impl.SysRoleMenuFacadeImpl
 * @CreateTime 2023-08-05
 */

@Service
@RequiredArgsConstructor
public class SysRoleMenuFacadeImpl implements ISysRoleMenuFacade {

    @NonNull
    private ISysRoleMenuService sysRoleMenuService;

    @NonNull
    private ISysRolePermissionService sysRolePermissionService;

    @NonNull
    private ISysRoleService sysRoleService;

    @NonNull
    private ISysUserMenuPermissionsFacade sysUserMenuPermissionsFacade;

    @NonNull
    private ISysUserRoleMenuFacade sysUserRoleMenuFacade;

    @Override
    public RPage<SysRoleMenuVO> listSysRoleMenuPage(PageQuery pageQuery, SysRoleMenuSearchDTO sysRoleMenuSearchDTO) {
        SysRoleMenuBO sysRoleMenuBO = CglibUtil.convertObj(sysRoleMenuSearchDTO, SysRoleMenuBO::new);
        IPage<SysRoleMenu> sysRoleMenuIPage = sysRoleMenuService.listSysRoleMenuPage(pageQuery, sysRoleMenuBO);
        return RPage.build(sysRoleMenuIPage, SysRoleMenuVO::new);
    }

    @Override
    public SysRoleMenuVO get(Long id) {
        SysRoleMenu byId = sysRoleMenuService.getById(id);
        return CglibUtil.convertObj(byId, SysRoleMenuVO::new);
    }

    @Override
    @Transactional
    public boolean add(SysRoleMenuAddDTO sysRoleMenuAddDTO) {
        SysRoleMenuBO sysRoleMenuBO = CglibUtil.convertObj(sysRoleMenuAddDTO, SysRoleMenuBO::new);
        return sysRoleMenuService.add(sysRoleMenuBO);
    }

    @Override
    @Transactional
    public boolean update(SysRoleMenuUpdateDTO sysRoleMenuUpdateDTO) {
        SysRoleMenuBO sysRoleMenuBO = CglibUtil.convertObj(sysRoleMenuUpdateDTO, SysRoleMenuBO::new);
        return sysRoleMenuService.updateById(sysRoleMenuBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(SysRoleMenuDeleteDTO sysRoleMenuDeleteDTO) {
        SysRoleMenuBO sysRoleMenuBO = CglibUtil.convertObj(sysRoleMenuDeleteDTO, SysRoleMenuBO::new);
        return sysRoleMenuService.removeBatchByIds(sysRoleMenuBO.getIds(), true);
    }

    @Override
    public List<Long> queryMenuIdsWithRoleId(Long roleId) {
        return sysRoleMenuService.queryMenuIdsWithRoleId(roleId);
    }

    @Override
    @Transactional
    public boolean addMenuForRoleId(Long roleId, List<Long> menuIds) {
        return sysRoleMenuService.addMenuForRoleId(roleId, menuIds);
    }

    @Override
    public SysRoleMenuPermissionVO queryMenuAndPermissions(Long roleId) {
        // 获取当前用户使用角色的菜单列表以及权限按钮列表
        List<SysMenuBO> sysMenuBOS = sysRoleMenuService.queryMenuListWithRoleId(roleId);
        List<SysPermissionBO> sysPermissionBOS = sysRolePermissionService.queryPermissionListWithRoleId(roleId);
        SysRole sysRole = sysRoleService.getById(roleId);
        Map<Long, List<SysPermissionBO>>  sysPermissions = transform(sysPermissionBOS);
        Map<Long, List<SysRoleMenuPermissionVO.PermissionVo>> sysPermissionVos =  new HashMap<>();
        sysMenuBOS.forEach(e->{
            List<SysPermissionBO> sysPermissionBOList = sysPermissions.get(e.getId());

            if(!CollectionUtils.isEmpty(sysPermissionBOList)){
                List<SysRoleMenuPermissionVO.PermissionVo> permissionVos = new ArrayList<>();
                sysPermissionBOList.forEach(p->{
                    SysRoleMenuPermissionVO.PermissionVo permissionVo =  SysRoleMenuPermissionVO.PermissionVo.builder().permissionId(p.getId()).permissionName(p.getName()).build();
                    permissionVos.add(permissionVo);
                });
                sysPermissionVos.put(e.getId(),permissionVos);
            }
        });
        return  SysRoleMenuPermissionVO.builder().roleId(roleId).roleName(sysRole.getRoleName()).menuVoList(initMenuRoute(0L,sysMenuBOS,sysPermissionVos)).build();
    }


    /**
     * 将权限集合分组成菜单对应按钮集合
     *
     * @param sysPermissionBOS 权限集合
     * @return {@linkplain Map} 菜单对应按钮集合
     * <AUTHOR>
     * @CreateTime 2024-04-27 19:33
     */
    private Map<Long, List<SysPermissionBO>> transform(List<SysPermissionBO> sysPermissionBOS) {
        return sysPermissionBOS.stream()
                .collect(Collectors.groupingBy(// 以menuId作为分组依据
                        SysPermission::getMenuId));
    }

    /**
     * 初始化菜单路由
     *
     * @param parentId          父级菜单ID
     * @param sysMenus          菜单列表
     * @param menuPermissionMap 菜单对应的权限按钮 Map 结构
     * @return {@link SysUserRouteVO.Route} 路由对象列表
     * <AUTHOR>
     * @CreateTime 2024-02-04 23:42
     */
    private static List<SysRoleMenuPermissionVO.MenuVo> initMenuRoute(Long parentId, List<SysMenuBO> sysMenus,
                                                            Map<Long, List<SysRoleMenuPermissionVO.PermissionVo>> menuPermissionMap) {
        // 根据 parentId 获取菜单列表
        List<SysMenuBO> parentMenuList = sysMenus.stream()
                .filter(item -> item.getParentId().equals(parentId)).toList();
        List<SysRoleMenuPermissionVO.MenuVo> routes = Lists.newArrayList();
        parentMenuList.forEach(menu -> {
            //  路由对象
            SysRoleMenuPermissionVO.MenuVo menuVo =  SysRoleMenuPermissionVO.MenuVo.builder()
                    .menuName(menu.getName())
                    .menuId(menu.getId())
                    .order(menu.getSort())
                    .permissionList(menuPermissionMap.getOrDefault(menu.getId(), Lists.newArrayList()))
                    .children(initMenuRoute(menu.getId(), sysMenus, menuPermissionMap))
                    .build();
            // 添加到路由列表
            routes.add(menuVo);
        });
        // 按照排序值排序
        routes.sort(Comparator.comparing(route -> route.getOrder()));
        return routes;
    }

    @Override
    @Transactional
    public boolean allocationMenuAndPermission(SysUserMenuPermissionDTO sysUserMenuPermissionDTO) {
        Long userId =  sysUserMenuPermissionDTO.getUserId();
        List<Long> menuId =  sysUserMenuPermissionDTO.getMenuIds();
        List<SysUserRoleMenuAddDTO> sysUserRoleMenuAddDTOS = Lists.newArrayList();
        menuId.forEach(e->{
            SysUserRoleMenuAddDTO sysUserRoleMenuAddDTO = new SysUserRoleMenuAddDTO();
            sysUserRoleMenuAddDTO.setUserId(userId);
            sysUserRoleMenuAddDTO.setRoleId(sysUserMenuPermissionDTO.getRoleId());
            sysUserRoleMenuAddDTO.setMenuId(e);
            sysUserRoleMenuAddDTOS.add(sysUserRoleMenuAddDTO);
        });
        List<SysUserMenuPermissionsAddDTO> sysUserMenuPermissionsAddDTOS = sysUserMenuPermissionDTO.getPermissionsAddDTOS();
        sysUserRoleMenuFacade.deleteByUserId(sysUserMenuPermissionDTO.getUserId(),sysUserMenuPermissionDTO.getRoleId());
        sysUserRoleMenuFacade.batchAdd(sysUserRoleMenuAddDTOS);
        sysUserMenuPermissionsFacade.deleteByUserId(sysUserMenuPermissionDTO.getUserId(),sysUserMenuPermissionDTO.getRoleId());

        Long roleId = sysUserMenuPermissionDTO.getRoleId();
        sysUserMenuPermissionsAddDTOS.forEach(e->{
            e.setRoleId(roleId);
        });
        sysUserMenuPermissionsFacade.batchAdd(sysUserMenuPermissionsAddDTOS);
        return true;
    }

    @Override
    public SysRoleMenuPermissionVO getMenuAndPermissions(Long userId, Long roleId) {

        List<SysMenuBO> sysMenuBOS = sysRoleMenuService.queryMenuListWithRoleId(roleId);
        // 获取当前用户使用角色的菜单列表以及权限按钮列表
        List<SysPermissionBO> sysPermissionBOS = sysRolePermissionService.queryPermissionListWithRoleId(roleId);
        // 获取用户对应角色分配的菜单
        List<SysUserRoleMenu> sysUserRoleMenuList = sysUserRoleMenuFacade.queryRoleMenuList(userId,roleId);
        Map<Long, List<SysPermissionBO>>  sysPermissions = transform(sysPermissionBOS);
        Map<Long, List<SysRoleMenuPermissionVO.PermissionVo>> sysPermissionVos =  new HashMap<>();
        List<Long>  menuIdList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(sysUserRoleMenuList)){
            menuIdList = sysUserRoleMenuList.stream().map(e->e.getMenuId()).collect(Collectors.toList());
            // 获取用户对应角色分配的权限
            List<SysUserMenuPermissions> permissionsList =  sysUserMenuPermissionsFacade.queryMenuPermission(userId,roleId,menuIdList);
            if(CollectionUtil.isNotEmpty(permissionsList)){
                List<Long> permissionIdList = permissionsList.stream().map(e->e.getPermissionId()).collect(Collectors.toList());
                sysMenuBOS.forEach(e->{
                    List<SysPermissionBO> sysPermissionBOList = sysPermissions.get(e.getId());
                    if(!CollectionUtils.isEmpty(sysPermissionBOList)){
                        List<SysRoleMenuPermissionVO.PermissionVo> permissionVos = new ArrayList<>();
                        sysPermissionBOList.forEach(p->{
                            SysRoleMenuPermissionVO.PermissionVo permissionVo =  SysRoleMenuPermissionVO.PermissionVo.builder().permissionId(p.getId()).permissionName(p.getName()).build();
                            // 设置选中标志
                            if(!CollectionUtils.isEmpty(permissionIdList)){
                                List<Long> checkedPermissionList =  permissionIdList.stream().filter(checkP->checkP.equals(p.getId())).collect(Collectors.toList());
                                if(!CollectionUtils.isEmpty(checkedPermissionList)){
                                    permissionVo.setIsChecked("1");
                                }
                            }
                            permissionVos.add(permissionVo);
                        });
                        sysPermissionVos.put(e.getId(),permissionVos);
                    }
                });
            }
        }else{
            sysMenuBOS.forEach(e->{
                List<SysPermissionBO> sysPermissionBOList = sysPermissions.get(e.getId());
                if(!CollectionUtils.isEmpty(sysPermissionBOList)){
                    List<SysRoleMenuPermissionVO.PermissionVo> permissionVos = new ArrayList<>();
                    sysPermissionBOList.forEach(p->{
                        SysRoleMenuPermissionVO.PermissionVo permissionVo =  SysRoleMenuPermissionVO.PermissionVo.builder().permissionId(p.getId()).permissionName(p.getName()).isChecked("1").build();
                        permissionVos.add(permissionVo);
                    });
                    sysPermissionVos.put(e.getId(),permissionVos);
                }
            });
        }
        SysRole sysRole = sysRoleService.getById(roleId);
        return  SysRoleMenuPermissionVO.builder().roleId(roleId).roleName(sysRole.getRoleName()).menuVoList(initMenuCheck(0L,sysMenuBOS,sysPermissionVos,menuIdList)).build();

    }

    /**
     * 查看用户拥有的菜单和权限
     * @param userId
     * @return
     */
    @Override
    public SysRoleMenuPermissionVO getMenuAndPermissionsByUserId(Long userId) {

        return null;
    }


    /**
     * 初始化菜单选中情况
     * @param parentId 父级
     * @param sysMenus 角色对应的菜单
     * @param menuPermissionMap 菜单权限map
     * @param menuIdList 已有菜单
     * @return
     */
    private static List<SysRoleMenuPermissionVO.MenuVo> initMenuCheck(Long parentId, List<SysMenuBO> sysMenus,Map<Long, List<SysRoleMenuPermissionVO.PermissionVo>> menuPermissionMap,List<Long> menuIdList) {
        // 根据 parentId 获取菜单列表
        List<SysMenuBO> parentMenuList = sysMenus.stream()
                .filter(item -> item.getParentId().equals(parentId)).toList();
        List<SysRoleMenuPermissionVO.MenuVo> routes = Lists.newArrayList();

        parentMenuList.forEach(menu -> {
            //  路由对象
            SysRoleMenuPermissionVO.MenuVo menuVo =  SysRoleMenuPermissionVO.MenuVo.builder()
                    .menuName(menu.getName())
                    .menuId(menu.getId())
                    .order(menu.getSort())
                    .permissionList(menuPermissionMap.getOrDefault(menu.getId(), Lists.newArrayList()))
                    .children(initMenuRoute(menu.getId(), sysMenus, menuPermissionMap))
                    .build();
            // 设置选中标志
            if(!CollectionUtils.isEmpty(menuIdList)){
                List<Long> checkMenuIdList =  menuIdList.stream().filter(m->m.equals(menu.getId())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(checkMenuIdList)){
                    menuVo.setIsChecked("1");
               }
            }else{
                menuVo.setIsChecked("1");
            }
            routes.add(menuVo);

        });
        // 按照排序值排序
        routes.sort(Comparator.comparing(route -> route.getOrder()));
        return routes;
    }
}