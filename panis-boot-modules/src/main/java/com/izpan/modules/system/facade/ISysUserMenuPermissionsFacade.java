/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.system.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.system.domain.bo.SysPermissionBO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsAddDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsDeleteDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsSearchDTO;
import com.izpan.modules.system.domain.dto.user.menu.permissions.SysUserMenuPermissionsUpdateDTO;
import com.izpan.modules.system.domain.entity.SysUserMenuPermissions;
import com.izpan.modules.system.domain.vo.SysUserMenuPermissionsVO;

import java.util.List;

/**
 * 用户菜单权限表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.system.facade.ISysUserMenuPermissionsFacade
 * @CreateTime 2024-12-13 - 22:21:55
 */

public interface ISysUserMenuPermissionsFacade {

    /**
     * 用户菜单权限表 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param sysUserMenuPermissionsSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:21:55
     */
    RPage<SysUserMenuPermissionsVO> listSysUserMenuPermissionsPage(PageQuery pageQuery, SysUserMenuPermissionsSearchDTO sysUserMenuPermissionsSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 用户菜单权限表ID
     * @return {@link SysUserMenuPermissionsVO} 用户菜单权限表 VO 对象
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:21:55
     */
    SysUserMenuPermissionsVO get(Long id);

    /**
     * 新增用户菜单权限表
     *
     * @param sysUserMenuPermissionsAddDTO 新增用户菜单权限表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:21:55
     */
    boolean add(SysUserMenuPermissionsAddDTO sysUserMenuPermissionsAddDTO);

    /**
     * 编辑更新用户菜单权限表信息
     *
     * @param sysUserMenuPermissionsUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:21:55
     */
    boolean update(SysUserMenuPermissionsUpdateDTO sysUserMenuPermissionsUpdateDTO);

    /**
     * 批量删除用户菜单权限表信息
     *
     * @param sysUserMenuPermissionsDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-13 - 22:21:55
     */
    boolean batchDelete(SysUserMenuPermissionsDeleteDTO sysUserMenuPermissionsDeleteDTO);

    boolean batchAdd(List<SysUserMenuPermissionsAddDTO> sysUserMenuPermissionsAddDTOs);

    List<SysUserMenuPermissionsVO> getByUserId(Long userId);

    boolean deleteByUserId(Long userId,Long roleId);

    List<SysUserMenuPermissions> queryMenuPermission(Long userId, Long roleId,List<Long> menuIdList);

    List<SysPermissionBO> queryPermissionListWithUserIdAndMenuId(Long userId,Long currentRoleId, List<Long> menuIdList);
}