package com.izpan.modules.system.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.infrastructure.util.SchoolUtil;
import com.izpan.modules.biz.service.IBizCompositionLogService;
import com.izpan.modules.system.domain.bo.SysUserBO;
import com.izpan.modules.system.domain.bo.SysUserResponsibilitiesBO;
import com.izpan.modules.system.domain.dto.user.*;
import com.izpan.modules.system.domain.entity.SysOrgUnits;
import com.izpan.modules.system.domain.entity.SysUser;
import com.izpan.modules.system.domain.vo.*;
import com.izpan.modules.system.facade.ISysUserFacade;
import com.izpan.modules.system.service.ISysOrgUnitsService;
import com.izpan.modules.system.service.ISysUserRoleService;
import com.izpan.modules.system.service.ISysUserService;
import io.micrometer.common.util.StringUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户管理 门面接口实现层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.facade.impl.SysUserFacadeImpl
 * @CreateTime 2023/7/6 - 16:06
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysUserFacadeImpl implements ISysUserFacade {

    @NonNull
    private ISysUserService sysUserService;
    @NonNull
    private ISysUserRoleService iSysUserRoleService;
    @NonNull
    private ISysOrgUnitsService sysOrgUnitsService;
    @NonNull
    private IBizCompositionLogService bizCompositionLogService;

    @Override
    public RPage<SysUserVO> listSysUserPage(PageQuery pageQuery, SysUserSearchDTO sysUserSearchDTO) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        SysUserBO sysUserBO = CglibUtil.convertObj(sysUserSearchDTO, SysUserBO::new);
        // 设置学校编码
        if (ObjectUtils.isEmpty(sysUserSearchDTO.getSchoolId())) {
            sysUserBO.setSchoolId(loginUser.getSchoolId());
        }
        IPage<SysUser> sysUserIPage = sysUserService.listSysUserPage(pageQuery, sysUserBO);
        return RPage.build(sysUserIPage, SysUserVO::new);

    }

    @Override
    public SysUserVO get(Long id) {
        SysUser byId = sysUserService.getById(id);
        SysUserVO sysUserVO = CglibUtil.convertObj(byId, SysUserVO::new);
        List<Long> roleIds = iSysUserRoleService.queryRoleIdsWithUserId(sysUserVO.getId());
        sysUserVO.setRoleIds(roleIds);
        return sysUserVO;
    }

    /**
     * 新增用户
     *
     * @param sysUserAddDTO 新增用户 DTO 对象
     * @return
     */
    @Override
    @Transactional
    public Long addUser(SysUserAddDTO sysUserAddDTO) {
        SysUserBO sysUserBO = CglibUtil.convertObj(sysUserAddDTO, SysUserBO::new);

        if (CollUtil.contains(Arrays.asList(SystemUserTypeEnum.USER.getValue(), SystemUserTypeEnum.STUDENT.getValue()), sysUserAddDTO.getUserType())) {
            if (sysUserBO.getStudentNum() == null) {
                sysUserBO.setStudentNum(sysUserBO.getUserName());
            }
            sysUserBO.setEvaluationNumber(3);
            sysUserBO.setMicroLessonsNumber(3);
        }

        // 设置学校ID&学校名称
        if (StringUtils.isEmpty(sysUserAddDTO.getSchoolId())) {
            SchoolUtil.setSchoolInfo(sysUserBO);
        }

        sysUserBO.setSalt(RandomStringUtils.randomAlphabetic(6));
        String generatePwd = null;
        if (StrUtil.isNotEmpty(sysUserBO.getPassword())) {
            generatePwd = sysUserBO.getPassword();
        } else {
            generatePwd = "000000";
        }
        String sha256HexPwd = DigestUtils.sha256Hex(generatePwd);
        String password = DigestUtils.sha256Hex(sha256HexPwd + sysUserBO.getSalt());
        sysUserBO.setPassword(password);

        boolean addFlag = sysUserService.addUser(sysUserBO);
        if (!addFlag) {
            log.error("新增用户失败，用户信息：{}", sysUserBO);
            return null;
        }

        // 如果是学生的话 默认设置用户/学生角色   后面优化不写死
        if (CollUtil.contains(Arrays.asList(SystemUserTypeEnum.USER.getValue(), SystemUserTypeEnum.STUDENT.getValue()), sysUserAddDTO.getUserType())) {
            iSysUserRoleService.updateUserRole(sysUserBO.getId(), new ArrayList<Long>() {{
                add(1899337740918149122L);
            }});
        } else if (sysUserAddDTO.getUserType().equals(SystemUserTypeEnum.TEACHER.getValue())) {
            iSysUserRoleService.updateUserRole(sysUserBO.getId(), sysUserAddDTO.getRoleIds());
        } else if (sysUserAddDTO.getUserType().equals(SystemUserTypeEnum.SCHOOL_ADMIN.getValue())) {
            iSysUserRoleService.updateUserRole(sysUserBO.getId(), new ArrayList<Long>() {{
                add(1868238140100411394L);
            }});
        } else if (sysUserAddDTO.getUserType().equals(SystemUserTypeEnum.AGENT.getValue())) {
            iSysUserRoleService.updateUserRole(sysUserBO.getId(), new ArrayList<Long>() {{
                add(1917883537564569602L);
            }});
        }

        return sysUserBO.getId();

    }

    @Override
    @Transactional
    public boolean updateUser(SysUserUpdateDTO sysUserUpdateDTO) {
        SysUserBO sysUserBO = CglibUtil.convertObj(sysUserUpdateDTO, SysUserBO::new);
        if (StrUtil.equals(sysUserUpdateDTO.getUserType(), SystemUserTypeEnum.TEACHER.getValue())) {
            SchoolUtil.setSchoolInfo(sysUserBO);
            iSysUserRoleService.updateUserRole(sysUserBO.getId(), sysUserBO.getRoleIds());
        }
        return sysUserService.updateUser(sysUserBO);
    }

    @Override
    @Transactional
    public boolean batchDeleteUser(SysUserDeleteDTO sysUserDeleteDTO) {
        SysUserBO sysUserBO = CglibUtil.convertObj(sysUserDeleteDTO, SysUserBO::new);
        return sysUserService.removeBatchByIds(sysUserBO.getIds());
    }

    @Override
    @Transactional
    public String resetPassword(Long userId) {
        return sysUserService.resetPassword(userId);
    }

    @Override
    public SysUserResponsibilitiesVO queryUserResponsibilitiesWithUserId(Long userId) {
        SysUserResponsibilitiesBO responsibilitiesBO = sysUserService.queryUserResponsibilitiesWithUserId(userId);
        return CglibUtil.convertObj(responsibilitiesBO, SysUserResponsibilitiesVO::new);
    }

    @Override
    @Transactional
    public boolean updateUserResponsibilities(SysUserResponsibilitiesUpdateDTO updateDTO) {
        SysUserResponsibilitiesBO responsibilitiesBO = CglibUtil.convertObj(updateDTO, SysUserResponsibilitiesBO::new);
        return sysUserService.updateUserResponsibilities(responsibilitiesBO);
    }

    @Override
    @Transactional
    public ImportResult importUsers(List<UserImportTemplateVO> list) {
        ImportResult.ImportResultBuilder resultBuilder = ImportResult.builder();
        List<String> failMessages = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        // 获取当前登录用户的学校编码
        String schoolId = SchoolUtil.getSchoolId();

        // 获取所有年级列表
        List<SysOrgUnits> gradeList = sysOrgUnitsService.list(new LambdaQueryWrapper<SysOrgUnits>()
                .eq(SysOrgUnits::getSchoolId, schoolId));
        Map<String, SysOrgUnits> gradeNameMap = gradeList.stream()
                .collect(Collectors.toMap(SysOrgUnits::getName, grade -> grade, (existing, replacement) -> existing));

        // 遍历已读取的数据列表
        for (int i = 0; i < list.size(); i++) {
            UserImportTemplateVO data = list.get(i);
            try {
                // 数据校验
                if (StringUtils.isBlank(data.getUserName())) {
                    failCount++;
                    failMessages.add("第" + (i + 1) + "行：学号不能为空");
                    continue;
                }

                if (StringUtils.isBlank(data.getRealName())) {
                    failCount++;
                    failMessages.add("第" + (i + 1) + "行：姓名不能为空");
                    continue;
                }

                // 校验用户名是否已存在
                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysUser::getUserName, data.getUserName());
                if (sysUserService.count(queryWrapper) > 0) {
                    failCount++;
                    failMessages.add("第" + (i + 1) + "行：登录账号 " + data.getUserName() + " 已存在");
                    continue;
                }

                // 构建用户对象
                SysUserAddDTO userAddDTO = new SysUserAddDTO();
                userAddDTO.setUserName(data.getUserName());
                userAddDTO.setRealName(data.getRealName());
                userAddDTO.setStudentNum(data.getStudentNum());

                // 设置年级和班级信息
                String gradeName = data.getGradeName();
                String clazzName = data.getClazzName();

                if (StringUtils.isNotBlank(gradeName)) {
                    // 根据年级名称查找年级信息
                    SysOrgUnits grade = gradeNameMap.get(gradeName);
                    if (grade == null) {
                        failCount++;
                        failMessages.add("第" + (i + 1) + "行：年级 " + gradeName + " 不存在");
                        continue;
                    }

                    userAddDTO.setGradeId(grade.getId().toString());
                    userAddDTO.setGradeName(grade.getName());

                    // 如果有班级名称，则查找班级信息
                    if (StringUtils.isNotBlank(clazzName)) {
                        List<SysOrgUnits> clazzList = sysOrgUnitsService.list(new LambdaQueryWrapper<SysOrgUnits>()
                                .eq(SysOrgUnits::getParentId, grade.getId())
                                .eq(SysOrgUnits::getSchoolId, schoolId));
                        Map<String, SysOrgUnits> clazzNameMap = clazzList.stream()
                                .collect(Collectors.toMap(SysOrgUnits::getName, clazz -> clazz, (existing, replacement) -> existing));

                        SysOrgUnits clazz = clazzNameMap.get(clazzName);
                        if (clazz == null) {
                            failCount++;
                            failMessages.add("第" + (i + 1) + "行：班级 " + clazzName + " 不存在");
                            continue;
                        }

                        userAddDTO.setClazzId(clazz.getId().toString());
                        userAddDTO.setClazzName(clazz.getName());
                    }
                } else if (StringUtils.isNotBlank(clazzName)) {
                    // 如果没有年级名称但有班级名称，则报错
                    failCount++;
                    failMessages.add("第" + (i + 1) + "行：指定班级名称时必须同时指定年级名称");
                    continue;
                }

                userAddDTO.setUserType(SystemUserTypeEnum.STUDENT.getValue());
                userAddDTO.setNickName(data.getRealName());

                // 保存用户
                boolean success = addUser(userAddDTO) != null;
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                    failMessages.add("第" + (i + 1) + "行：保存失败");
                }
            } catch (Exception e) {
                log.error("导入用户失败", e);
                failCount++;
                failMessages.add("第" + (i + 1) + "行：导入异常 - " + e.getMessage());
            }
        }

        // 返回结果
        return resultBuilder
                .successCount(successCount)
                .failCount(failCount)
                .failMessages(failMessages)
                .build();
    }

    @Override
    public boolean removeUser(SysUserDeleteDTO userIds) {
        return sysUserService.removeUser(userIds);
    }

    @Override
    public Integer updateTimes(SysUserUpdateNumberDto dto) {
        return sysUserService.updateTimes(dto);
    }

    @Override
    public List<EvaluationUserVO> queryByGradeAndClass(EvaluationUserSearchDTO sysUserSearchDTO) {

        List<SysUser> sysUserList = sysUserService.list(new LambdaQueryWrapper<SysUser>()
                .in(sysUserSearchDTO.getGradeIds() != null, SysUser::getGradeId, sysUserSearchDTO.getGradeIds())
                .in(sysUserSearchDTO.getClazzIds() != null, SysUser::getClazzId, sysUserSearchDTO.getClazzIds())
                .eq(sysUserSearchDTO.getUserType() != null, SysUser::getUserType, sysUserSearchDTO.getUserType())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getRealName()), SysUser::getRealName, sysUserSearchDTO.getRealName())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getEmail()), SysUser::getEmail, sysUserSearchDTO.getEmail())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getUserName()), SysUser::getUserName, sysUserSearchDTO.getUserName())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getRealName()), SysUser::getRealName, sysUserSearchDTO.getRealName())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getEmail()), SysUser::getEmail, sysUserSearchDTO.getEmail())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getGradeId()), SysUser::getGradeId, sysUserSearchDTO.getGradeId())
                .eq(StringUtils.isNotBlank(sysUserSearchDTO.getClazzId()), SysUser::getClazzId, sysUserSearchDTO.getClazzId())
                .eq(SysUser::getSchoolId, SchoolUtil.getSchoolId())
        );

        List<EvaluationUserVO> evaluationUserVOList = CglibUtil.convertList(sysUserList, EvaluationUserVO::new);

        // 如果提供了作文ID，则检查每个用户是否提交过该作文
        if (sysUserSearchDTO.getCompositionId() != null) {
            try {
                for (EvaluationUserVO userVO : evaluationUserVOList) {
                    // 检查用户是否提交过指定作文
                    Boolean isSubmitted = bizCompositionLogService.checkUserSubmission(sysUserSearchDTO.getCompositionId(), userVO.getId());
                    userVO.setIsMatchComposition(isSubmitted);
                }
            } catch (Exception e) {
                // 如果调用方法失败，默认设置为false
                for (EvaluationUserVO userVO : evaluationUserVOList) {
                    userVO.setIsMatchComposition(false);
                }
                log.error("检查用户作文提交状态时发生错误", e);
            }
        } else {
            // 如果没有提供作文ID，则默认设置为false
            for (EvaluationUserVO userVO : evaluationUserVOList) {
                userVO.setIsMatchComposition(false);
            }
        }

        return evaluationUserVOList;
    }

    @Override
    @Transactional
    public boolean batchUpdateGradeClass(SysUserBatchUpdateGradeClassDTO batchUpdateDTO) {
        String currentSchoolId = SchoolUtil.getSchoolId();
        try {

            // 查询需要更新的用户列表，确保只能更新本校用户
            List<SysUser> usersToUpdate = sysUserService.list(new LambdaQueryWrapper<SysUser>()
                    .in(SysUser::getId, batchUpdateDTO.getUserIds())
                    .eq(SysUser::getSchoolId, currentSchoolId));

            // 更新用户年级班级信息
            for (SysUser user : usersToUpdate) {
                try {
                    user.setGradeId(batchUpdateDTO.getGradeId());
                    user.setGradeName(batchUpdateDTO.getGradeName());
                    user.setClazzId(batchUpdateDTO.getClazzId());
                    user.setClazzName(batchUpdateDTO.getClazzName());
                } catch (Exception e) {
                    log.error("批量更新用户年级班级失败，用户ID：{}", user.getId(), e);
                }
            }

            // 批量更新数据库
            if (!usersToUpdate.isEmpty()) {
                boolean updateResult = sysUserService.updateBatchById(usersToUpdate);
                if (!updateResult) {
                    throw new RuntimeException("批量更新数据库失败");
                }
            }

        } catch (Exception e) {
            log.error("批量更新用户年级班级异常", e);
            return false;
        }

        return true;
    }
}
