package com.izpan.modules.system.facade.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.google.common.collect.Lists;
import com.izpan.common.api.ResultCode;
import com.izpan.common.constants.SystemCacheConstant;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.exception.BizException;
import com.izpan.common.exception.RouteException;
import com.izpan.common.pool.StringPools;
import com.izpan.common.util.CglibUtil;
import com.izpan.common.util.CollectionUtil;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.util.GsonUtil;
import com.izpan.infrastructure.util.RedisUtil;
import com.izpan.modules.system.domain.bo.SysMenuBO;
import com.izpan.modules.system.domain.bo.SysPermissionBO;
import com.izpan.modules.system.domain.bo.SysRoleBO;
import com.izpan.modules.system.domain.bo.SysUserBO;
import com.izpan.modules.system.domain.dto.LoginFormDTO;
import com.izpan.modules.system.domain.dto.menu.SysUserRouteVO;
import com.izpan.modules.system.domain.dto.user.SysUserUpdateCurrentInfoDTO;
import com.izpan.modules.system.domain.dto.user.role.SysUserCurrentRoleDTO;
import com.izpan.modules.system.domain.entity.SysMenu;
import com.izpan.modules.system.domain.entity.SysPermission;
import com.izpan.modules.system.domain.entity.SysRole;
import com.izpan.modules.system.domain.vo.SysUserVO;
import com.izpan.modules.system.facade.*;
import com.izpan.modules.system.service.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户认证门面接口实现层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.system.facade.impl.AuthenticationFacadeImpl
 * @CreateTime 2023/7/17 - 18:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthenticationFacadeImpl implements IAuthenticationFacade {

    @NonNull
    private ISysRoleMenuService sysRoleMenuService;

    @NonNull
    private ISysRolePermissionService sysRolePermissionService;

    @NonNull
    private ISysUserService sysUserService;

    @NonNull
    private ISysRoleService sysRoleService;

    @NonNull
    private ISysUserRoleMenuFacade sysUserRoleMenuFacade;

    @NonNull
    private ISysUserMenuPermissionsFacade sysUserMenuPermissionsFacade;

    @NonNull
    private ISysMenuService sysMenuService;

    @NonNull
    private ISysPermissionService sysPermissionService;

    /**
     * 初始化菜单路由
     *
     * @param parentId          父级菜单ID
     * @param sysMenus          菜单列表
     * @param menuPermissionMap 菜单对应的权限按钮 Map 结构
     * @return {@link SysUserRouteVO.Route} 路由对象列表
     * <AUTHOR>
     * @CreateTime 2024-02-04 23:42
     */
    private static List<SysUserRouteVO.Route> initMenuRoute(Long parentId, List<SysMenuBO> sysMenus,
                                                            Map<Long, List<String>> menuPermissionMap) {
        // 根据 parentId 获取菜单列表
        List<SysMenuBO> parentMenuList = sysMenus.stream()
                .filter(item -> item.getParentId().equals(parentId)).toList();
        List<SysUserRouteVO.Route> routes = Lists.newArrayList();
        parentMenuList.forEach(menu -> {
            // 路由元数据
            SysUserRouteVO.Meta routeMeta = SysUserRouteVO.Meta.builder()
                    .title(menu.getName())
                    .i18nKey(menu.getI18nKey())
                    .order(menu.getSort())
                    .keepAlive(StringPools.Y.equals(menu.getKeepAlive()))
                    .hideInMenu(StringPools.Y.equals(menu.getHide()))
                    .multiTab(StringPools.Y.equals(menu.getMultiTab()))
                    .fixedIndexInTab(menu.getFixedIndexInTab())
                    .href(menu.getHref())
                    .query(GsonUtil.fromJsonList(menu.getQuery()))
                    .permissions(menuPermissionMap.getOrDefault(menu.getId(), Lists.newArrayList()))
                    .build();
            if (menu.getIconType().equals(StringPools.TWO)) {
                routeMeta.setLocalIcon(menu.getIcon());
            } else {
                routeMeta.setIcon(menu.getIcon());
            }
            // 路由道具
            SysUserRouteVO.Props props = SysUserRouteVO.Props.builder()
                    .url(menu.getIframeUrl())
                    .build();
            // 路由对象
            SysUserRouteVO.Route route = SysUserRouteVO.Route.builder()
                    .name(menu.getRouteName())
                    .path(menu.getRoutePath())
                    .component(menu.getComponent().replace(StringPools.HASH, StringPools.DOLLAR))
                    .props(props)
                    .meta(routeMeta)
                    .children(initMenuRoute(menu.getId(), sysMenus, menuPermissionMap))
                    .build();
            // 添加到路由列表
            routes.add(route);
        });
        // 按照排序值排序
        routes.sort(Comparator.comparing(route -> route.getMeta().getOrder()));
        return routes;
    }

    @Override
    @Transactional
    public Map<String, String> userNameLogin(LoginFormDTO loginFormDTO) {
        SysUserBO sysUserBO = CglibUtil.convertObj(loginFormDTO, SysUserBO::new);
        return sysUserService.userLogin(sysUserBO);
    }

    @Override
    public boolean logout() {
        RedisUtil.del(SystemCacheConstant.userRouteKey(GlobalUserHolder.getUserId()));
        StpUtil.logout();
        return true;
    }

    @Override
    public Map<String, String> refreshToken(String refreshToken) {
        return sysUserService.refreshToken(refreshToken, null, null);
    }

    @Override
    public SysUserVO getCurrentUserInfo() {
        SysUserBO sysUserBO = sysUserService.currentUserInfo();
        LoginUser loginUser = GlobalUserHolder.getUser();
        // 获取用户角色
        List<SysRoleBO> sysRoleBOS = sysRoleService.queryRoleListWithUserId(loginUser.getId());
        SysUserVO sysUserVO = CglibUtil.convertObj(sysUserBO, SysUserVO::new);
        // 用户角色不为空
        if (CollectionUtil.isNotEmpty(sysRoleBOS)) {
            if (ObjectUtils.isEmpty(loginUser.getCurrentRoleId())) {
                sysUserVO.setCurrentRoleId(sysRoleBOS.get(0).getId());
                sysUserVO.setCurrentRoleName(sysRoleBOS.get(0).getRoleName());
                sysUserVO.setPositionIdentify(sysRoleBOS.get(0).getPositionIdentify());
            } else {
                sysUserVO.setCurrentRoleId(loginUser.getCurrentRoleId());
                sysUserVO.setCurrentRoleName(loginUser.getCurrentRoleName());
                sysUserVO.setPositionIdentify(loginUser.getPositionIdentify());
            }
            sysUserVO.setRoleList(sysRoleBOS);
        }
        return sysUserVO;
    }

    @Override
    @Transactional
    public SysUserVO updateCurrentUserInfo(SysUserUpdateCurrentInfoDTO currentInfoDTO) {
        SysUserBO sysUserBO = CglibUtil.convertObj(currentInfoDTO, SysUserBO::new);
        boolean updated = sysUserService.updateCurrentUserInfo(sysUserBO);
        if (Boolean.FALSE.equals(updated)) {
            throw new BizException("更新用户信息异常");
        }
        return getCurrentUserInfo();
    }

    @Override
//    @Cacheable(value = SystemCacheConstant.SYSTEM_USER_ROUTE, key = "#userId")
    public SysUserRouteVO queryUserRouteWithUserId(Long userId) {
        try {
            //获取当前使用角色的ID
            Long currentRoleId = GlobalUserHolder.getCurrentRoleId();

            Map<Long, List<String>> menuPermissionMap = null;
            // 获取当前用户使用角色的菜单列表以及权限按钮列表
            List<SysMenuBO> sysMenuBOS = sysUserRoleMenuFacade.queryMenuListWithUserIdAndRoleId(userId, currentRoleId);
            if (CollectionUtil.isNotEmpty(sysMenuBOS)) {
                List<Long> menuIdList = sysMenuBOS.stream().map(e -> e.getId()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(menuIdList)) {
                    List<SysPermissionBO> sysPermissionBOS = sysUserMenuPermissionsFacade.queryPermissionListWithUserIdAndMenuId(userId, currentRoleId, menuIdList);
                    // 将权限集合分组成菜单对应按钮集合
                    menuPermissionMap = transform(sysPermissionBOS);
                }
            } else {
                sysMenuBOS = sysMenuService.queryMenuListWithRoleId(currentRoleId);
                List<SysPermissionBO> sysPermissionBOS = sysPermissionService.queryPermissionListWithRoleId(currentRoleId);
                menuPermissionMap = transform(sysPermissionBOS);
            }
            // 返回路由对象
            return SysUserRouteVO.builder()
                    .home("home")
                    // 组装路由集合
                    .routes(initMenuRoute(0L, sysMenuBOS, menuPermissionMap))
                    .build();
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new RouteException(ResultCode.USER_ROUTE_ERROR.getCode(), ResultCode.USER_ROUTE_ERROR.getValue());
        }
    }

    /**
     * 将权限集合分组成菜单对应按钮集合
     *
     * @param sysPermissionBOS 权限集合
     * @return {@linkplain Map} 菜单对应按钮集合
     * <AUTHOR>
     * @CreateTime 2024-04-27 19:33
     */
    private Map<Long, List<String>> transform(List<SysPermissionBO> sysPermissionBOS) {
        return sysPermissionBOS.stream()
                .collect(Collectors.groupingBy(
                        // 以menuId作为分组依据
                        SysPermission::getMenuId,
                        Collectors.mapping(
                                // 将每个SysPermission对象的resource属性按照分号分割后进行处理
                                permission -> Arrays.stream(permission.getResource().split(StringPools.SEMICOLON))
                                        // 去除空格，过滤空字符串
                                        .map(String::trim).filter(s -> !s.isEmpty())
                                        // 去除重复元素
                                        .distinct().toList(),
                                // 对List<String>进行处理
                                Collectors.collectingAndThen(Collectors.toList(),
                                        // 将多个List<String>合并为一个流
                                        list -> list.stream()
                                                .flatMap(Collection::stream)
                                                // 去除重复元素，排序
                                                .distinct().sorted(String::compareTo).toList()
                                )
                        )
                ));
    }

    /**
     * 切换用户角色
     *
     * @param  sysUserCurrentRoleDTO
     */
    @Override
    public void transferCurrentRole(SysUserCurrentRoleDTO sysUserCurrentRoleDTO) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        loginUser.setCurrentRoleId(sysUserCurrentRoleDTO.getRoleId());
        loginUser.setCurrentRoleName(sysUserCurrentRoleDTO.getRoleName());
        loginUser.setPositionIdentify(sysUserCurrentRoleDTO.getPositionIdentify());
        StpUtil.getSessionByLoginId(loginUser.getId()).set("user", loginUser).update();
    }
}
