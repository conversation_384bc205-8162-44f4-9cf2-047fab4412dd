<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.tools.repository.mapper.ToolGeneratorTableColumnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ToolGeneratorTableColumnResultMap"
               type="com.izpan.modules.tools.domain.entity.ToolGeneratorTableColumn">
        <result column="table_id" property="tableId"/>
        <result column="table_name" property="tableName"/>
        <result column="column_name" property="columnName"/>
        <result column="property_name" property="propertyName"/>
        <result column="column_comment" property="columnComment"/>
        <result column="data_type" property="dataType"/>
        <result column="java_type" property="javaType"/>
        <result column="typescript_type" property="typeScriptType"/>
        <result column="ordinal_position" property="ordinalPosition"/>
        <result column="is_i18n" property="i18n"/>
        <result column="is_required" property="required"/>
        <result column="is_list" property="list"/>
        <result column="is_search" property="search"/>
        <result column="search_type" property="searchType"/>
        <result column="is_added" property="added"/>
        <result column="is_edit" property="edit"/>
        <result column="dict_code" property="dictCode"/>
        <result column="render_type" property="renderType"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="ToolGeneratorTableColumnColumnList">
        id,
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        is_deleted,
        table_id, `table_name`, is_i18n, is_required, is_list, is_search, is_added, is_edit, dict_code, render_type, `status`
    </sql>

</mapper>
