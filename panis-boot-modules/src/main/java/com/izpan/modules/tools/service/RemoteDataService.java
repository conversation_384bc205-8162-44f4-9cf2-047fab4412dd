package com.izpan.modules.tools.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.izpan.modules.common.CustomRetryListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.retry.RetryListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@RequiredArgsConstructor
@Slf4j
public class RemoteDataService {

    private final OkHttpClient okHttpClient;

//    @Retryable(
//            maxAttempts = 3,
//            backoff = @Backoff(delay = 1000, maxDelay = 3000, random = true)
//    )
    public JSONArray fetchJsonArray(String url, String arrayField) {
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("HTTP 请求失败，状态码: {}, URL: {}", response.code(), url);
                throw new IOException("HTTP 请求失败，状态码: " + response.code());
            }

            String responseBody = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(responseBody);

            if (!"0".equals(jsonObject.getString("result"))) {
                return new JSONArray(); // 可选：也可以抛出异常让 retry 生效
            }

            return jsonObject.getJSONArray(arrayField);

        }catch (Exception e){
            log.error("远程数据获取失败,请求接口{}",url,e);
            throw new RuntimeException("远程数据获取失败",e);
        }
    }

    public String[] fetchJsonIds(String url, String arrayField) {
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("HTTP 请求失败，状态码: {}, URL: {}", response.code(), url);
                throw new IOException("HTTP 请求失败，状态码: " + response.code());
            }

            String responseBody = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(responseBody);

            if (!"0".equals(jsonObject.getString("result"))) {
                return null;
            }
            String ids = jsonObject.getString(arrayField);
            return StringUtils.isEmpty(ids) ? null : ids.split(",");

        }catch (Exception e){
            log.error("远程数据获取失败,请求接口{}",url,e);
            throw new RuntimeException("远程数据获取失败",e);
        }
    }

    @Bean
    public RetryListener retryListener() {
        return new CustomRetryListener();
    }
}
