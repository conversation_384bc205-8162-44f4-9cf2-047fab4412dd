/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.dto.payment.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 基础服务-支付与退款订单表 新增 DTO 对象
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderAddDTO
* @CreateTime 2025-07-20 - 18:05:44
*/

@Getter
@Setter
@Schema(name = "BsePaymentOrderAddDTO", description = "基础服务-支付与退款订单表 新增 DTO 对象")
public class BsePaymentOrderAddDTO implements Serializable {

    @Schema(description = "商户内部订单号，全局唯一")
    private String orderNo;

    @Schema(description = "支付用户的ID")
    private Long userId;

    @Schema(description = "关联的套餐ID (bse_package.id)")
    private Long packageId;

    @Schema(description = "套餐信息快照，防止套餐信息变更导致历史订单信息不一致")
    private String packageSnapshot;

    @Schema(description = "应付金额（单位：分）")
    private Integer amountPayable;

    @Schema(description = "实付金额（单位：分），支付成功后由回调更新")
    private Integer amountPaid;

    @Schema(description = "订单支付状态 (PENDING_PAYMENT:待支付, PAID:已支付, CLOSED:已关闭, FAILED:支付失败)")
    private String status;

    @Schema(description = "支付成功时间")
    private LocalDateTime paidAt;

    @Schema(description = "订单过期时间（待支付状态下有效）")
    private LocalDateTime expiresAt;

    @Schema(description = "支付渠道编码 (例如: WECHAT_LITE, ALIPAY_APP)")
    private String paymentChannel;

    @Schema(description = "渠道方订单号 (例如: 微信支付返回的 transaction_id)")
    private String channelOrderNo;

    @Schema(description = "订单退款状态 (NO_REFUND:未退款, REFUND_PENDING:退款中, REFUND_SUCCESS:退款成功, REFUND_FAILED:退款失败)")
    private String refundStatus;

    @Schema(description = "商户内部退款单号，全局唯一")
    private String refundNo;

    @Schema(description = "渠道方退款单号")
    private String channelRefundNo;

    @Schema(description = "退款金额（单位：分）")
    private Integer refundAmount;

    @Schema(description = "退款成功时间")
    private LocalDateTime refundedAt;

}