/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.message.BseMessageAddDTO;
import com.izpan.modules.base.domain.dto.message.BseMessageDeleteDTO;
import com.izpan.modules.base.domain.dto.message.BseMessageSearchDTO;
import com.izpan.modules.base.domain.dto.message.BseMessageUpdateDTO;
import com.izpan.modules.base.domain.vo.BseMessageVO;

/**
 * 消息表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBseMessageFacade
 * @CreateTime 2025-03-28 - 14:31:07
 */

public interface IBseMessageFacade {

    /**
     * 消息表 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bseMessageSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-03-28 - 14:31:07
     */
    RPage<BseMessageVO> listBseMessagePage(PageQuery pageQuery, BseMessageSearchDTO bseMessageSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 消息表ID
     * @return {@link BseMessageVO} 消息表 VO 对象
     * <AUTHOR>
     * @CreateTime 2025-03-28 - 14:31:07
     */
    BseMessageVO get(Long id);

    /**
     * 新增消息表
     *
     * @param bseMessageAddDTO 新增消息表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-28 - 14:31:07
     */
    boolean add(BseMessageAddDTO bseMessageAddDTO);

    /**
     * 编辑更新消息表信息
     *
     * @param bseMessageUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-28 - 14:31:07
     */
    boolean update(BseMessageUpdateDTO bseMessageUpdateDTO);

    /**
     * 批量删除消息表信息
     *
     * @param bseMessageDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-28 - 14:31:07
     */
    boolean batchDelete(BseMessageDeleteDTO bseMessageDeleteDTO);

}