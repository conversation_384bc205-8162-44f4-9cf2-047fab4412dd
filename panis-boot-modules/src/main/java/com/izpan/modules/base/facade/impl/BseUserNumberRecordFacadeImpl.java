/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BseUserNumberRecordBO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordAddDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordDeleteDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordSearchDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordUpdateDTO;
import com.izpan.modules.base.domain.entity.BseUserNumberRecord;
import com.izpan.modules.base.domain.vo.BseUserNumberRecordVO;
import com.izpan.modules.base.facade.IBseUserNumberRecordFacade;
import com.izpan.modules.base.service.IBseUserNumberRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.facade.impl.BseUserNumberRecordFacadeImpl
 * @CreateTime 2025-03-24 - 22:55:48
 */

@Service
@RequiredArgsConstructor
public class BseUserNumberRecordFacadeImpl implements IBseUserNumberRecordFacade {

    @NonNull
    private IBseUserNumberRecordService bseUserNumberRecordService;

    @Override
    public RPage<BseUserNumberRecordVO> listBseUserNumberRecordPage(PageQuery pageQuery, BseUserNumberRecordSearchDTO bseUserNumberRecordSearchDTO) {
        BseUserNumberRecordBO bseUserNumberRecordBO = CglibUtil.convertObj(bseUserNumberRecordSearchDTO, BseUserNumberRecordBO::new);
        IPage<BseUserNumberRecord> bseUserNumberRecordIPage = bseUserNumberRecordService.listBseUserNumberRecordPage(pageQuery, bseUserNumberRecordBO);
        return RPage.build(bseUserNumberRecordIPage, BseUserNumberRecordVO::new);
    }

    @Override
    public BseUserNumberRecordVO get(Long id) {
        BseUserNumberRecord byId = bseUserNumberRecordService.getById(id);
        return CglibUtil.convertObj(byId, BseUserNumberRecordVO::new);
    }

    @Override
    @Transactional
    public boolean add(BseUserNumberRecordAddDTO bseUserNumberRecordAddDTO) {
        BseUserNumberRecordBO bseUserNumberRecordBO = CglibUtil.convertObj(bseUserNumberRecordAddDTO, BseUserNumberRecordBO::new);
        return bseUserNumberRecordService.save(bseUserNumberRecordBO);
    }

    @Override
    @Transactional
    public boolean update(BseUserNumberRecordUpdateDTO bseUserNumberRecordUpdateDTO) {
        BseUserNumberRecordBO bseUserNumberRecordBO = CglibUtil.convertObj(bseUserNumberRecordUpdateDTO, BseUserNumberRecordBO::new);
        return bseUserNumberRecordService.updateById(bseUserNumberRecordBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BseUserNumberRecordDeleteDTO bseUserNumberRecordDeleteDTO) {
        BseUserNumberRecordBO bseUserNumberRecordBO = CglibUtil.convertObj(bseUserNumberRecordDeleteDTO, BseUserNumberRecordBO::new);
        return bseUserNumberRecordService.removeBatchByIds(bseUserNumberRecordBO.getIds(), true);
    }

    @Override
    public List<BseUserNumberRecord> listBseUserNumberRecordByUserId(BseUserNumberRecordSearchDTO bseUserNumberRecordSearchDTO) {
        if (bseUserNumberRecordSearchDTO.getUserId() == null) {
             bseUserNumberRecordSearchDTO.setUserId(GlobalUserHolder.getUser().getId());
        }

        QueryWrapper<BseUserNumberRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", bseUserNumberRecordSearchDTO.getUserId());
        queryWrapper.eq("business_type", bseUserNumberRecordSearchDTO.getBusinessType());
        
        // 添加24小时时间限制
        LocalDateTime twentyFourHoursAgo = LocalDateTime.now().minusHours(24);
        queryWrapper.ge("operation_time", twentyFourHoursAgo);
        
        queryWrapper.orderByDesc("operation_time");

        return bseUserNumberRecordService.list(queryWrapper);
    }

}