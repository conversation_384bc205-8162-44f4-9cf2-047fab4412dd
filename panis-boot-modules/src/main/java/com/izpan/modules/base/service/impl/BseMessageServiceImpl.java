/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BseMessageBO;
import com.izpan.modules.base.domain.entity.BseMessage;
import com.izpan.modules.base.repository.mapper.BseMessageMapper;
import com.izpan.modules.base.service.IBseMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 消息表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BseMessageServiceImpl
 * @CreateTime 2025-03-28 - 14:31:07
 */

@Service
public class BseMessageServiceImpl extends ServiceImpl<BseMessageMapper, BseMessage> implements IBseMessageService {

    @Override
    public IPage<BseMessage> listBseMessagePage(PageQuery pageQuery, BseMessageBO bseMessageBO) {
        LambdaQueryWrapper<BseMessage> queryWrapper = new LambdaQueryWrapper<BseMessage>()
            .eq(ObjectUtils.isNotEmpty(bseMessageBO.getMessageType()), BseMessage::getMessageType, bseMessageBO.getMessageType())
            .eq(ObjectUtils.isNotEmpty(bseMessageBO.getTitle()), BseMessage::getTitle, bseMessageBO.getTitle())
            .eq(ObjectUtils.isNotEmpty(bseMessageBO.getStatus()), BseMessage::getStatus, bseMessageBO.getStatus())
            .eq(ObjectUtils.isNotEmpty(bseMessageBO.getSchoolId()), BseMessage::getSchoolId, bseMessageBO.getSchoolId())
            .eq(ObjectUtils.isNotEmpty(bseMessageBO.getSchoolName()), BseMessage::getSchoolName, bseMessageBO.getSchoolName()).orderByDesc(BseMessage::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

