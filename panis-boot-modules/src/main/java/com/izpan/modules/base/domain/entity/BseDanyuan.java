/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BaseEntity;
import java.io.Serializable;
import com.izpan.infrastructure.domain.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
    import java.io.Serial;

/**
* 单元教学信息表 Entity 实体类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.entity.BseDanyuan
* @CreateTime 2025-03-25 - 20:45:00
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("bse_danyuan")
public class BseDanyuan extends BaseEntity {

    /**
    * 年级
    */
    private String dGrade;

    /**
    * 单元名称
    */
    private String dDanyuan;

    /**
    * 课程名称
    */
    private String dName;

    /**
    * 关键词
    */
    private String dGjc;

    /**
    * 文体类型
    */
    private String dWenti;

    /**
    * 是否显示(0/1)
    */
    private Boolean dIsshow;

    /**
    * 指标版本说明
    */
    private String dZbbb;

    /**
    * 导学内容
    */
    private String dDaoxue;

}