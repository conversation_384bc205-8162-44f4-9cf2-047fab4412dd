/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.vo;

import com.izpan.infrastructure.domain.BaseVO;
import com.izpan.modules.system.domain.vo.SysUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
* 学校管理 VO 展示类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.vo.BseSchoolVO
* @CreateTime 2024-12-05 - 22:49:40
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BseSchoolVO", description = "学校管理 VO 对象")
public class BseSchoolInfoVO extends BaseVO {

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "校长名称")
    private String principalName;

    @Schema(description = "校长电话")
    private String principalPhone;

    @Schema(description = "校长邮箱")
    private String principalMail;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系号码")
    private String contactPhone;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "区名称")
    private String areaName;

    @Schema(description = "所在地址(详细地址)")
    private String belongAddress;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否启用(0:禁用,1:启用)")
    private String status;

    private SysUserVO sysUserVO;

}