/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.school.*;
import com.izpan.modules.base.domain.entity.AreaInfo;
import com.izpan.modules.base.domain.entity.BseSchool;
import com.izpan.modules.base.domain.entity.CityInfo;
import com.izpan.modules.base.domain.entity.ProvinceInfo;
import com.izpan.modules.base.domain.vo.BseSchoolInfoVO;
import com.izpan.modules.base.domain.vo.BseSchoolVO;

import java.util.List;

/**
 * 学校管理 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.facade.IBseSchoolFacade
 * @CreateTime 2024-12-05 - 22:49:40
 */

public interface IBseSchoolFacade {

    /**
     * 学校管理 - 分页查询
     *
     * @param pageQuery          分页对象
     * @param bseSchoolSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-05 - 22:49:40
     */
    RPage<BseSchoolVO> listBseSchoolPage(PageQuery pageQuery, BseSchoolSearchDTO bseSchoolSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 学校管理ID
     * @return {@link BseSchoolVO} 学校管理 VO 对象
     * <AUTHOR>
     * @CreateTime 2024-12-05 - 22:49:40
     */
    BseSchoolInfoVO get(Long id, String type);

    /**
     * 新增学校管理
     *
     * @param bseSchoolAddDTO 新增学校管理 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-05 - 22:49:40
     */
    Long add(BseSchoolAddDTO bseSchoolAddDTO);

    /**
     * 编辑更新学校管理信息
     *
     * @param bseSchoolUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-05 - 22:49:40
     */
    boolean update(BseSchoolUpdateDTO bseSchoolUpdateDTO);

    /**
     * 批量删除学校管理信息
     *
     * @param bseSchoolDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-05 - 22:49:40
     */
    boolean batchDelete(BseSchoolDeleteDTO bseSchoolDeleteDTO);

    List<BseSchool> getSchoolLists(String schoolName);

    List<CityInfo> getCityList(String provinceCode, Long parentId);

    List<ProvinceInfo> getProvinceList(Long parentId);

    List<AreaInfo> getAreaList(String cityCode, Long parentId);

    List<BseSchool> getSchoolListByArea(String areaCode, Long parentId);

    /**
     * 获取当前登录用户所在学校的微课次数和测评次数
     *
     * @return {@link BseSchool} 包含微课次数和测评次数的Map
     * <AUTHOR>
     * @CreateTime 2024-03-21
     */
    BseSchoolVO getSchoolNumberInfo();

    /**
     * 批量分配次数
     *
     * @param dto 包含ID列表和分配次数的DTO
     *           type=1 学校给用户分配次数
     *           type=2 供应商给学校分配次数
     * @return 是否分配成功
     * <AUTHOR>
     * @CreateTime 2024-03-21
     */
    boolean batchAssignCounts(BseSchoolBatchAssignCountsDTO dto);

}