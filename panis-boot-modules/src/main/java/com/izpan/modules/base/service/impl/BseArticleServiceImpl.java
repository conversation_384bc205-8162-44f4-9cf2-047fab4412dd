/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BseArticleBO;
import com.izpan.modules.base.domain.entity.BseArticle;
import com.izpan.modules.base.repository.mapper.BseArticleMapper;
import com.izpan.modules.base.service.IBseArticleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 文章表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BseArticleServiceImpl
 * @CreateTime 2025-03-24 - 11:17:41
 */

@Service
public class BseArticleServiceImpl extends ServiceImpl<BseArticleMapper, BseArticle> implements IBseArticleService {

    @Override
    public IPage<BseArticle> listBseArticlePage(PageQuery pageQuery, BseArticleBO bseArticleBO) {
        LambdaQueryWrapper<BseArticle> queryWrapper = new LambdaQueryWrapper<BseArticle>()
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getTitle()), BseArticle::getTitle, bseArticleBO.getTitle())
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getArticleType()), BseArticle::getArticleType, bseArticleBO.getArticleType())
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getTheme()), BseArticle::getTheme, bseArticleBO.getTheme())
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getWordCount()), BseArticle::getWordCount, bseArticleBO.getWordCount())
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getGrade()), BseArticle::getGrade, bseArticleBO.getGrade())
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getSource()), BseArticle::getSource, bseArticleBO.getSource())
            .eq(ObjectUtils.isNotEmpty(bseArticleBO.getStatus()), BseArticle::getStatus, bseArticleBO.getStatus()).orderByDesc(BseArticle::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

