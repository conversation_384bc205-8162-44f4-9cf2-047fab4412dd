/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderAddDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderDeleteDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderSearchDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderUpdateDTO;
import com.izpan.modules.base.domain.vo.BsePaymentOrderVO;

/**
 * 基础服务-支付与退款订单表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBsePaymentOrderFacade
 * @CreateTime 2025-07-20 - 18:05:44
 */

public interface IBsePaymentOrderFacade {

    /**
     * 基础服务-支付与退款订单表 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bsePaymentOrderSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 18:05:44
     */
    RPage<BsePaymentOrderVO> listBsePaymentOrderPage(PageQuery pageQuery, BsePaymentOrderSearchDTO bsePaymentOrderSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 基础服务-支付与退款订单表ID
     * @return {@link BsePaymentOrderVO} 基础服务-支付与退款订单表 VO 对象
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 18:05:44
     */
    BsePaymentOrderVO get(Long id);

    /**
     * 新增基础服务-支付与退款订单表
     *
     * @param bsePaymentOrderAddDTO 新增基础服务-支付与退款订单表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 18:05:44
     */
    boolean add(BsePaymentOrderAddDTO bsePaymentOrderAddDTO);

    /**
     * 编辑更新基础服务-支付与退款订单表信息
     *
     * @param bsePaymentOrderUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 18:05:44
     */
    boolean update(BsePaymentOrderUpdateDTO bsePaymentOrderUpdateDTO);

    /**
     * 批量删除基础服务-支付与退款订单表信息
     *
     * @param bsePaymentOrderDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 18:05:44
     */
    boolean batchDelete(BsePaymentOrderDeleteDTO bsePaymentOrderDeleteDTO);

}