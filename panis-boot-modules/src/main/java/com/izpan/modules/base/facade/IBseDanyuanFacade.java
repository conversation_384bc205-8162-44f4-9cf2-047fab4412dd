/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanAddDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanDeleteDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanSearchDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanUpdateDTO;
import com.izpan.modules.base.domain.vo.BseDanyuanVO;

import java.util.List;

/**
 * 单元教学信息表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBseDanyuanFacade
 * @CreateTime 2025-03-25 - 20:45:00
 */

public interface IBseDanyuanFacade {

    /**
     * 单元教学信息表 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bseDanyuanSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-03-25 - 20:45:00
     */
    RPage<BseDanyuanVO> listBseDanyuanPage(PageQuery pageQuery, BseDanyuanSearchDTO bseDanyuanSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 单元教学信息表ID
     * @return {@link BseDanyuanVO} 单元教学信息表 VO 对象
     * <AUTHOR>
     * @CreateTime 2025-03-25 - 20:45:00
     */
    BseDanyuanVO get(Long id);

    /**
     * 新增单元教学信息表
     *
     * @param bseDanyuanAddDTO 新增单元教学信息表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-25 - 20:45:00
     */
    boolean add(BseDanyuanAddDTO bseDanyuanAddDTO);

    /**
     * 编辑更新单元教学信息表信息
     *
     * @param bseDanyuanUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-25 - 20:45:00
     */
    boolean update(BseDanyuanUpdateDTO bseDanyuanUpdateDTO);

    /**
     * 批量删除单元教学信息表信息
     *
     * @param bseDanyuanDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-25 - 20:45:00
     */
    boolean batchDelete(BseDanyuanDeleteDTO bseDanyuanDeleteDTO);

    /**
     * 获取单元教学信息表列表
     *
     * @param bseDanyuanSearchDTO 查询对象
     * @return {@link List} 单元教学信息表列表
     * <AUTHOR>
     * @CreateTime 2025-03-25 - 20:45:00
     */
    List<BseDanyuanVO> list(BseDanyuanSearchDTO bseDanyuanSearchDTO);
}