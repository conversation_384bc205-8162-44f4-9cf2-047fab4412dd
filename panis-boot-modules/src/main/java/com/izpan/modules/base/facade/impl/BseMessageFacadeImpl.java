/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BseMessageBO;
import com.izpan.modules.base.domain.dto.message.BseMessageAddDTO;
import com.izpan.modules.base.domain.dto.message.BseMessageDeleteDTO;
import com.izpan.modules.base.domain.dto.message.BseMessageSearchDTO;
import com.izpan.modules.base.domain.dto.message.BseMessageUpdateDTO;
import com.izpan.modules.base.domain.entity.BseMessage;
import com.izpan.modules.base.domain.vo.BseMessageVO;
import com.izpan.modules.base.facade.IBseMessageFacade;
import com.izpan.modules.base.service.IBseMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消息表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.facade.impl.BseMessageFacadeImpl
 * @CreateTime 2025-03-28 - 14:31:07
 */

@Service
@RequiredArgsConstructor
public class BseMessageFacadeImpl implements IBseMessageFacade {

    @NonNull
    private IBseMessageService bseMessageService;

    @Override
    public RPage<BseMessageVO> listBseMessagePage(PageQuery pageQuery, BseMessageSearchDTO bseMessageSearchDTO) {
        BseMessageBO bseMessageBO = CglibUtil.convertObj(bseMessageSearchDTO, BseMessageBO::new);
        LoginUser loginUser = GlobalUserHolder.getUser();

        if (StrUtil.isNotBlank(bseMessageBO.getMessageType())) {
            if (bseMessageBO.getMessageType().equals("school")) {
                bseMessageBO.setSchoolId(loginUser.getSchoolId());
                bseMessageBO.setSchoolName(loginUser.getSchoolName());
            } else {
                bseMessageBO.setSchoolId(null);
                bseMessageBO.setSchoolName(null);
            }
        } else {
            handleMessage(bseMessageBO);
        }

        IPage<BseMessage> bseMessageIPage = bseMessageService.listBseMessagePage(pageQuery, bseMessageBO);
        return RPage.build(bseMessageIPage, BseMessageVO::new);
    }

    @Override
    public BseMessageVO get(Long id) {
        BseMessage byId = bseMessageService.getById(id);
        return CglibUtil.convertObj(byId, BseMessageVO::new);
    }

    @Override
    @Transactional
    public boolean add(BseMessageAddDTO bseMessageAddDTO) {
        BseMessageBO bseMessageBO = CglibUtil.convertObj(bseMessageAddDTO, BseMessageBO::new);
        handleMessage(bseMessageBO);
        return bseMessageService.save(bseMessageBO);
    }

    @Override
    @Transactional
    public boolean update(BseMessageUpdateDTO bseMessageUpdateDTO) {
        BseMessageBO bseMessageBO = CglibUtil.convertObj(bseMessageUpdateDTO, BseMessageBO::new);
        handleMessage(bseMessageBO);
        return bseMessageService.updateById(bseMessageBO);
    }

    public void handleMessage(BseMessageBO bseMessageBO) {
        LoginUser loginUser = GlobalUserHolder.getUser();

        // 消息类型（system:系统通知, school:学校消息）
        if (loginUser.getUserType().equals("0")) {
            bseMessageBO.setMessageType("system");
        } else {
            bseMessageBO.setMessageType("school");
            bseMessageBO.setSchoolId(loginUser.getSchoolId());
            bseMessageBO.setSchoolName(loginUser.getSchoolName());
        }

    }

    @Override
    @Transactional
    public boolean batchDelete(BseMessageDeleteDTO bseMessageDeleteDTO) {
        BseMessageBO bseMessageBO = CglibUtil.convertObj(bseMessageDeleteDTO, BseMessageBO::new);
        return bseMessageService.removeBatchByIds(bseMessageBO.getIds(), true);
    }

}