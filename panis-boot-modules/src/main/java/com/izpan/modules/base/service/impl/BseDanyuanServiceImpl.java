/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BseDanyuanBO;
import com.izpan.modules.base.domain.entity.BseDanyuan;
import com.izpan.modules.base.repository.mapper.BseDanyuanMapper;
import com.izpan.modules.base.service.IBseDanyuanService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 单元教学信息表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BseDanyuanServiceImpl
 * @CreateTime 2025-03-25 - 20:45:00
 */

@Service
public class BseDanyuanServiceImpl extends ServiceImpl<BseDanyuanMapper, BseDanyuan> implements IBseDanyuanService {

    @Override
    public IPage<BseDanyuan> listBseDanyuanPage(PageQuery pageQuery, BseDanyuanBO bseDanyuanBO) {
        LambdaQueryWrapper<BseDanyuan> queryWrapper = new LambdaQueryWrapper<BseDanyuan>()
            .eq(ObjectUtils.isNotEmpty(bseDanyuanBO.getDGrade()), BseDanyuan::getDGrade, bseDanyuanBO.getDGrade())
            .eq(ObjectUtils.isNotEmpty(bseDanyuanBO.getDDanyuan()), BseDanyuan::getDDanyuan, bseDanyuanBO.getDDanyuan())
            .eq(ObjectUtils.isNotEmpty(bseDanyuanBO.getDName()), BseDanyuan::getDName, bseDanyuanBO.getDName())
            .eq(ObjectUtils.isNotEmpty(bseDanyuanBO.getDGjc()), BseDanyuan::getDGjc, bseDanyuanBO.getDGjc())
            .eq(ObjectUtils.isNotEmpty(bseDanyuanBO.getDWenti()), BseDanyuan::getDWenti, bseDanyuanBO.getDWenti())
            .eq(ObjectUtils.isNotEmpty(bseDanyuanBO.getDIsshow()), BseDanyuan::getDIsshow, bseDanyuanBO.getDIsshow()).orderByDesc(BseDanyuan::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

