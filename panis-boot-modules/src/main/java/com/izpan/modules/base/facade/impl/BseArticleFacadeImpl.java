/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BseArticleBO;
import com.izpan.modules.base.domain.dto.article.BseArticleAddDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleDeleteDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleSearchDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleUpdateDTO;
import com.izpan.modules.base.domain.entity.BseArticle;
import com.izpan.modules.base.domain.vo.BseArticleVO;
import com.izpan.modules.base.facade.IBseArticleFacade;
import com.izpan.modules.base.service.IBseArticleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文章表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.impl.BseArticleFacadeImpl
 * @CreateTime 2025-03-24 - 11:17:41
 */

@Service
@RequiredArgsConstructor
public class BseArticleFacadeImpl implements IBseArticleFacade {

    @NonNull
    private IBseArticleService bseArticleService;

    @Override
    public RPage<BseArticleVO> listBseArticlePage(PageQuery pageQuery, BseArticleSearchDTO bseArticleSearchDTO) {
        BseArticleBO bseArticleBO = CglibUtil.convertObj(bseArticleSearchDTO, BseArticleBO::new);
        IPage<BseArticle> bseArticleIPage = bseArticleService.listBseArticlePage(pageQuery, bseArticleBO);
        return RPage.build(bseArticleIPage, BseArticleVO::new);
    }

    @Override
    public BseArticleVO get(Long id) {
        BseArticle byId = bseArticleService.getById(id);
        return CglibUtil.convertObj(byId, BseArticleVO::new);
    }

    @Override
    @Transactional
    public boolean add(BseArticleAddDTO bseArticleAddDTO) {
        BseArticleBO bseArticleBO = CglibUtil.convertObj(bseArticleAddDTO, BseArticleBO::new);
        return bseArticleService.save(bseArticleBO);
    }

    @Override
    @Transactional
    public boolean update(BseArticleUpdateDTO bseArticleUpdateDTO) {
        BseArticleBO bseArticleBO = CglibUtil.convertObj(bseArticleUpdateDTO, BseArticleBO::new);
        return bseArticleService.updateById(bseArticleBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BseArticleDeleteDTO bseArticleDeleteDTO) {
        BseArticleBO bseArticleBO = CglibUtil.convertObj(bseArticleDeleteDTO, BseArticleBO::new);
        return bseArticleService.removeBatchByIds(bseArticleBO.getIds(), true);
    }

}