/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusAddDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusDeleteDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusSearchDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusUpdateDTO;
import com.izpan.modules.base.domain.vo.BseCorpusVO;

/**
 *  门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBseCorpusFacade
 * @CreateTime 2024-12-14 - 14:49:29
 */

public interface IBseCorpusFacade {

    /**
     *  - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bseCorpusSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-14 - 14:49:29
     */
    RPage<BseCorpusVO> listBseCorpusPage(PageQuery pageQuery, BseCorpusSearchDTO bseCorpusSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id ID
     * @return {@link BseCorpusVO}  VO 对象
     * <AUTHOR>
     * @CreateTime 2024-12-14 - 14:49:29
     */
    BseCorpusVO get(Long id);

    /**
     * 新增
     *
     * @param bseCorpusAddDTO 新增 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-14 - 14:49:29
     */
    boolean add(BseCorpusAddDTO bseCorpusAddDTO);

    /**
     * 编辑更新信息
     *
     * @param bseCorpusUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-14 - 14:49:29
     */
    boolean update(BseCorpusUpdateDTO bseCorpusUpdateDTO);

    /**
     * 批量删除信息
     *
     * @param bseCorpusDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-14 - 14:49:29
     */
    boolean batchDelete(BseCorpusDeleteDTO bseCorpusDeleteDTO);

}