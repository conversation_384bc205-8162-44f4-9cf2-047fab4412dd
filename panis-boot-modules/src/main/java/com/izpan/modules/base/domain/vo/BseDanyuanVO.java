/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.vo;

import com.izpan.infrastructure.domain.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
* 单元教学信息表 VO 展示类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.vo.BseDanyuanVO
* @CreateTime 2025-03-25 - 20:45:00
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BseDanyuanVO", description = "单元教学信息表 VO 对象")
public class BseDanyuanVO extends BaseVO {

    @Schema(description = "唯一标识")
    private Integer dId;

    @Schema(description = "年级")
    private String dGrade;

    @Schema(description = "单元名称")
    private String dDanyuan;

    @Schema(description = "课程名称")
    private String dName;

    @Schema(description = "关键词")
    private String dGjc;

    @Schema(description = "文体类型")
    private String dWenti;

    @Schema(description = "是否显示(0/1)")
    private Integer dIsshow;

    @Schema(description = "指标版本说明")
    private String dZbbb;

    @Schema(description = "导学内容")
    private String dDaoxue;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}