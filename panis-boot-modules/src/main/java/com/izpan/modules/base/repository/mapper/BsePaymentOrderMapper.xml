<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.base.repository.mapper.BsePaymentOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BsePaymentOrderResultMap" type="com.izpan.modules.base.domain.entity.BsePaymentOrder">
        <result column="order_no" property="orderNo"/>
        <result column="user_id" property="userId"/>
        <result column="package_id" property="packageId"/>
        <result column="package_snapshot" property="packageSnapshot"/>
        <result column="amount_payable" property="amountPayable"/>
        <result column="amount_paid" property="amountPaid"/>
        <result column="status" property="status"/>
        <result column="paid_at" property="paidAt"/>
        <result column="expires_at" property="expiresAt"/>
        <result column="payment_channel" property="paymentChannel"/>
        <result column="channel_order_no" property="channelOrderNo"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="refund_no" property="refundNo"/>
        <result column="channel_refund_no" property="channelRefundNo"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refunded_at" property="refundedAt"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BsePaymentOrderColumnList">
        id,
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        is_deleted,
        order_no, user_id, package_id, package_snapshot, amount_payable, amount_paid, `status`, paid_at, expires_at, payment_channel, channel_order_no, refund_status, refund_no, channel_refund_no, refund_amount, refunded_at
    </sql>

</mapper>
