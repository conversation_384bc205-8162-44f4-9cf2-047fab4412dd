/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.izpan.infrastructure.config.WechatMiniProgramProperties;
import com.izpan.infrastructure.constants.WechatMiniProgramConstants;
import com.izpan.modules.base.domain.dto.wechat.WechatLoginDTO;
import com.izpan.modules.base.domain.vo.WechatLoginResultVO;
import com.izpan.modules.base.service.IWechatMiniProgramService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序服务实现类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.WechatMiniProgramServiceImpl
 * @CreateTime 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatMiniProgramServiceImpl implements IWechatMiniProgramService {

    private final WechatMiniProgramProperties wechatMiniProgramProperties;
    private final RestTemplate restTemplate;

    @Override
    public WechatLoginResultVO getOpenidByCode(WechatLoginDTO wechatLoginDTO) {
        WechatLoginResultVO result = new WechatLoginResultVO();
        
        try {
            // 1. 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", wechatMiniProgramProperties.getAppId());
            params.put("secret", wechatMiniProgramProperties.getAppSecret());
            params.put("js_code", wechatLoginDTO.getCode());
            params.put("grant_type", WechatMiniProgramConstants.GRANT_TYPE);
            
            log.info("调用微信小程序登录接口，code：{}", wechatLoginDTO.getCode());

            // 2. 构建请求URL
            StringBuilder urlBuilder = new StringBuilder(WechatMiniProgramConstants.JSCODE2SESSION_URL);
            urlBuilder.append("?appid=").append(wechatMiniProgramProperties.getAppId())
                     .append("&secret=").append(wechatMiniProgramProperties.getAppSecret())
                     .append("&js_code=").append(wechatLoginDTO.getCode())
                     .append("&grant_type=").append(WechatMiniProgramConstants.GRANT_TYPE);

            // 3. 发送HTTP GET请求
            String response = restTemplate.getForObject(urlBuilder.toString(), String.class);
            log.info("微信小程序登录接口响应：{}", response);
            
            // 4. 解析响应结果
            if (StrUtil.isNotBlank(response)) {
                JSONObject jsonObject = JSONUtil.parseObj(response);
                
                // 检查是否有错误
                if (jsonObject.containsKey("errcode")) {
                    Integer errcode = jsonObject.getInt("errcode");
                    if (!(WechatMiniProgramConstants.SUCCESS_CODE == errcode)) {
                        result.setErrcode(errcode);
                        result.setErrmsg(jsonObject.getStr("errmsg"));
                        log.error("微信小程序登录失败，错误码：{}，错误信息：{}", errcode, result.getErrmsg());
                        return result;
                    }
                }
                
                // 成功获取openid
                result.setOpenid(jsonObject.getStr("openid"));
                result.setSessionKey(jsonObject.getStr("session_key"));
                result.setUnionid(jsonObject.getStr("unionid"));
                result.setErrcode(WechatMiniProgramConstants.SUCCESS_CODE);
                
                log.info("成功获取用户openid：{}", result.getOpenid());
            } else {
                result.setErrcode(-1);
                result.setErrmsg("微信接口响应为空");
                log.error("微信小程序登录接口响应为空");
            }
            
        } catch (Exception e) {
            log.error("调用微信小程序登录接口异常", e);
            result.setErrcode(-1);
            result.setErrmsg("系统异常：" + e.getMessage());
        }
        
        return result;
    }

}
