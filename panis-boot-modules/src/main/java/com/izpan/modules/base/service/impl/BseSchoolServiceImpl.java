/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.izpan.modules.base.domain.bo.BseSchoolBO;
import com.izpan.modules.base.domain.entity.BseSchool;
import com.izpan.modules.base.domain.vo.BseSchoolVO;
import com.izpan.modules.base.repository.mapper.BseSchoolMapper;
import com.izpan.modules.base.service.IBseSchoolService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 学校管理 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BseSchoolServiceImpl
 * @CreateTime 2024-12-05 - 22:49:40
 */

@Service
public class BseSchoolServiceImpl extends ServiceImpl<BseSchoolMapper, BseSchool> implements IBseSchoolService {

    @Override
    public IPage<BseSchoolVO> listBseSchoolPage(PageQuery pageQuery, BseSchoolBO bseSchoolBO) {
        MPJLambdaWrapper<BseSchool> wrapper = new MPJLambdaWrapper<BseSchool>()
                .selectAll(BseSchool.class)
                .selectSub(BseSchool.class,
                        subQueryWrapper -> subQueryWrapper.selectCount(BseSchool::getId)
                                          .eq(BseSchool::getParentId, BseSchool::getId),
                        BseSchoolVO::getSchoolCount)
                .eq(ObjectUtils.isNotEmpty(bseSchoolBO.getType()),
                        BseSchool::getType, bseSchoolBO.getType())
                .eq(ObjectUtils.isNotEmpty(bseSchoolBO.getParentId()),BseSchool::getParentId,bseSchoolBO.getParentId())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getSchoolName()),
                        BseSchool::getSchoolName, bseSchoolBO.getSchoolName())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getPrincipalName()),
                        BseSchool::getPrincipalName, bseSchoolBO.getPrincipalName())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getPrincipalPhone()),
                        BseSchool::getPrincipalPhone, bseSchoolBO.getPrincipalPhone())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getContactPerson()),
                        BseSchool::getContactPerson, bseSchoolBO.getContactPerson())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getContactPhone()),
                        BseSchool::getContactPhone, bseSchoolBO.getContactPhone())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getProvinceCode()),
                        BseSchool::getProvinceCode, bseSchoolBO.getProvinceCode())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getCityCode()),
                        BseSchool::getCityCode, bseSchoolBO.getCityCode())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getAreaCode()),
                        BseSchool::getAreaCode, bseSchoolBO.getAreaCode())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getProvinceName()),
                        BseSchool::getProvinceName, bseSchoolBO.getProvinceName())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getCityName()),
                        BseSchool::getCityName, bseSchoolBO.getCityName())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getAreaName()),
                        BseSchool::getAreaName, bseSchoolBO.getAreaName())
                .like(ObjectUtils.isNotEmpty(bseSchoolBO.getBelongAddress()),
                        BseSchool::getBelongAddress, bseSchoolBO.getBelongAddress())
                .orderByDesc(BseSchool::getCreateTime);

        return baseMapper.selectJoinPage(pageQuery.buildPage(), BseSchoolVO.class, wrapper);
    }

    @Override
    public boolean isSchoolExpired(String schoolId) {
        return baseMapper.isSchoolExpired(schoolId);
    }

}

