/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BaseEntity;
import java.io.Serializable;
import com.izpan.infrastructure.domain.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
    import java.io.Serial;

/**
* 消息表 Entity 实体类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.entity.BseMessage
* @CreateTime 2025-03-28 - 14:31:07
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("bse_message")
public class BseMessage extends BaseEntity {

    /**
    * 消息类型（system:系统通知, school:学校消息）
    */
    private String messageType;

    /**
    * 消息标题
    */
    private String title;

    /**
    * 消息内容
    */
    private String content;

    /**
    * 状态(0:禁用,1:启用)
    */
    private String status;

    /**
    * 学校编码
    */
    private String schoolId;

    /**
    * 学校名称
    */
    private String schoolName;

}