/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordAddDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordDeleteDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordSearchDTO;
import com.izpan.modules.base.domain.dto.user.number.record.BseUserNumberRecordUpdateDTO;
import com.izpan.modules.base.domain.entity.BseUserNumberRecord;
import com.izpan.modules.base.domain.vo.BseUserNumberRecordVO;

import java.util.List;

/**
 *  门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBseUserNumberRecordFacade
 * @CreateTime 2025-03-24 - 22:55:48
 */

public interface IBseUserNumberRecordFacade {

    /**
     *  - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bseUserNumberRecordSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 22:55:48
     */
    RPage<BseUserNumberRecordVO> listBseUserNumberRecordPage(PageQuery pageQuery, BseUserNumberRecordSearchDTO bseUserNumberRecordSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id ID
     * @return {@link BseUserNumberRecordVO}  VO 对象
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 22:55:48
     */
    BseUserNumberRecordVO get(Long id);

    /**
     * 新增
     *
     * @param bseUserNumberRecordAddDTO 新增 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 22:55:48
     */
    boolean add(BseUserNumberRecordAddDTO bseUserNumberRecordAddDTO);

    /**
     * 编辑更新信息
     *
     * @param bseUserNumberRecordUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 22:55:48
     */
    boolean update(BseUserNumberRecordUpdateDTO bseUserNumberRecordUpdateDTO);

    /**
     * 批量删除信息
     *
     * @param bseUserNumberRecordDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 22:55:48
     */
    boolean batchDelete(BseUserNumberRecordDeleteDTO bseUserNumberRecordDeleteDTO);


    /**
     * 获取用户购买记录列表
     *
     * @param bseUserNumberRecordSearchDTO  用户ID
     * @return {@link List<BseUserNumberRecordVO>}  VO 对象列表
     * @CreateTime 2025-03-24 - 22:55:48
     */
    List<BseUserNumberRecord> listBseUserNumberRecordByUserId(BseUserNumberRecordSearchDTO bseUserNumberRecordSearchDTO);
}