/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.article.BseArticleAddDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleDeleteDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleSearchDTO;
import com.izpan.modules.base.domain.dto.article.BseArticleUpdateDTO;
import com.izpan.modules.base.domain.vo.BseArticleVO;

/**
 * 文章表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBseArticleFacade
 * @CreateTime 2025-03-24 - 11:17:41
 */

public interface IBseArticleFacade {

    /**
     * 文章表 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bseArticleSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 11:17:41
     */
    RPage<BseArticleVO> listBseArticlePage(PageQuery pageQuery, BseArticleSearchDTO bseArticleSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 文章表ID
     * @return {@link BseArticleVO} 文章表 VO 对象
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 11:17:41
     */
    BseArticleVO get(Long id);

    /**
     * 新增文章表
     *
     * @param bseArticleAddDTO 新增文章表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 11:17:41
     */
    boolean add(BseArticleAddDTO bseArticleAddDTO);

    /**
     * 编辑更新文章表信息
     *
     * @param bseArticleUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 11:17:41
     */
    boolean update(BseArticleUpdateDTO bseArticleUpdateDTO);

    /**
     * 批量删除文章表信息
     *
     * @param bseArticleDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-24 - 11:17:41
     */
    boolean batchDelete(BseArticleDeleteDTO bseArticleDeleteDTO);

}