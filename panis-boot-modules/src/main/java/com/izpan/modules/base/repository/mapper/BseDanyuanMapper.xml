<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.base.repository.mapper.BseDanyuanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BseDanyuanResultMap" type="com.izpan.modules.base.domain.entity.BseDanyuan">
        <id column="id" property="id" />
        <result column="d_grade" property="dGrade"/>
        <result column="d_danyuan" property="dDanyuan"/>
        <result column="d_name" property="dName"/>
        <result column="d_gjc" property="dGjc"/>
        <result column="d_wenti" property="dWenti"/>
        <result column="d_isShow" property="dIsshow"/>
        <result column="d_zbbb" property="dZbbb"/>
        <result column="d_daoxue" property="dDaoxue"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BseDanyuanColumnList">
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        is_deleted,
        id, d_grade, d_danyuan, d_name, d_gjc, d_wenti, d_isShow, d_zbbb, d_daoxue
    </sql>

</mapper>
