/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.dto.packages.BsePackageAddDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageDeleteDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageSearchDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageUpdateDTO;
import com.izpan.modules.base.domain.vo.BsePackageVO;
import com.izpan.modules.base.domain.vo.BsePackageMiniVO;

import java.util.List;

/**
 * 基础服务-套餐信息表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.IBsePackageFacade
 * @CreateTime 2025-07-20 - 12:54:18
 */

public interface IBsePackageFacade {

    /**
     * 基础服务-套餐信息表 - 分页查询
     *
     * @param pageQuery        分页对象
     * @param bsePackageSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 12:54:18
     */
    RPage<BsePackageVO> listBsePackagePage(PageQuery pageQuery, BsePackageSearchDTO bsePackageSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 基础服务-套餐信息表ID
     * @return {@link BsePackageVO} 基础服务-套餐信息表 VO 对象
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 12:54:18
     */
    BsePackageVO get(Long id);

    /**
     * 新增基础服务-套餐信息表
     *
     * @param bsePackageAddDTO 新增基础服务-套餐信息表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 12:54:18
     */
    boolean add(BsePackageAddDTO bsePackageAddDTO);

    /**
     * 编辑更新基础服务-套餐信息表信息
     *
     * @param bsePackageUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 12:54:18
     */
    boolean update(BsePackageUpdateDTO bsePackageUpdateDTO);

    /**
     * 批量删除基础服务-套餐信息表信息
     *
     * @param bsePackageDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 12:54:18
     */
    boolean batchDelete(BsePackageDeleteDTO bsePackageDeleteDTO);

    /**
     * 小程序-套餐列表查询
     *
     * @return {@link List} 上架套餐列表，按排序值升序排列
     * <AUTHOR>
     * @CreateTime 2025-07-20 - 12:54:18
     */
    List<BsePackageMiniVO> listMiniPackages();

}