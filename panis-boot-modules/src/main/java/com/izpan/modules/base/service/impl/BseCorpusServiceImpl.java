/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BseCorpusBO;
import com.izpan.modules.base.domain.entity.BseCorpus;
import com.izpan.modules.base.repository.mapper.BseCorpusMapper;
import com.izpan.modules.base.service.IBseCorpusService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 *  Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BseCorpusServiceImpl
 * @CreateTime 2024-12-14 - 14:49:29
 */

@Service
public class BseCorpusServiceImpl extends ServiceImpl<BseCorpusMapper, BseCorpus> implements IBseCorpusService {

    @Override
    public IPage<BseCorpus> listBseCorpusPage(PageQuery pageQuery, BseCorpusBO bseCorpusBO) {
        LambdaQueryWrapper<BseCorpus> queryWrapper = new LambdaQueryWrapper<BseCorpus>()
            .like(ObjectUtils.isNotEmpty(bseCorpusBO.getCategory()), BseCorpus::getCategory, bseCorpusBO.getCategory())
            .like(ObjectUtils.isNotEmpty(bseCorpusBO.getContent()), BseCorpus::getContent, bseCorpusBO.getContent())
            .eq(ObjectUtils.isNotEmpty(bseCorpusBO.getStatus()), BseCorpus::getStatus, bseCorpusBO.getStatus()).orderByDesc(BseCorpus::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

