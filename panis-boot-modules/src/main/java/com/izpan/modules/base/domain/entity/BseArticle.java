/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BaseEntity;
import java.io.Serializable;
import com.izpan.infrastructure.domain.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
    import java.io.Serial;

/**
* 文章表 Entity 实体类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.entity.BseArticle
* @CreateTime 2025-03-24 - 11:17:41
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("bse_article")
public class BseArticle extends BaseEntity {

    /**
    * 文章标题
    */
    private String title;

    /**
    * 文章类型（如记叙文、议论文等）
    */
    private String articleType;

    /**
    * 文章主题（如爱国、节气等）
    */
    private String theme;

    /**
    * 文章字数
    */
    private Integer wordCount;

    /**
    * 适用年级
    */
    private String grade;

    /**
    * 作文内容
    */
    private String content;

    /**
    * 来源（如deepseek、kimi）
    */
    private String source;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 是否启用(0:禁用,1:启用)
    */
    private String status;

}