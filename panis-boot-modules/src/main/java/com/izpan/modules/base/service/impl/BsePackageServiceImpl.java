/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BsePackageBO;
import com.izpan.modules.base.domain.entity.BsePackage;
import com.izpan.modules.base.repository.mapper.BsePackageMapper;
import com.izpan.modules.base.service.IBsePackageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * 基础服务-套餐信息表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BsePackageServiceImpl
 * @CreateTime 2025-07-20 - 12:54:18
 */

@Service
public class BsePackageServiceImpl extends ServiceImpl<BsePackageMapper, BsePackage> implements IBsePackageService {

    @Override
    public IPage<BsePackage> listBsePackagePage(PageQuery pageQuery, BsePackageBO bsePackageBO) {
        LambdaQueryWrapper<BsePackage> queryWrapper = new LambdaQueryWrapper<BsePackage>()
            .eq(ObjectUtils.isNotEmpty(bsePackageBO.getName()), BsePackage::getName, bsePackageBO.getName())
            .eq(ObjectUtils.isNotEmpty(bsePackageBO.getType()), BsePackage::getType, bsePackageBO.getType())
            .eq(ObjectUtils.isNotEmpty(bsePackageBO.getStatus()), BsePackage::getStatus, bsePackageBO.getStatus()).orderByDesc(BsePackage::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

    @Override
    public List<BsePackage> listMiniPackages() {
        LambdaQueryWrapper<BsePackage> queryWrapper = new LambdaQueryWrapper<BsePackage>()
            .eq(BsePackage::getStatus, "1")  // 只查询上架状态的套餐
            .orderByAsc(BsePackage::getSortOrder);  // 按排序值升序排列
        return baseMapper.selectList(queryWrapper);
    }

}

