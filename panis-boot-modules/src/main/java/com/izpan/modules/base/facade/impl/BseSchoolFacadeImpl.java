/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.exception.BizException;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BseSchoolBO;
import com.izpan.modules.base.domain.dto.school.*;
import com.izpan.modules.base.domain.entity.AreaInfo;
import com.izpan.modules.base.domain.entity.BseSchool;
import com.izpan.modules.base.domain.entity.CityInfo;
import com.izpan.modules.base.domain.entity.ProvinceInfo;
import com.izpan.modules.base.domain.vo.BseSchoolInfoVO;
import com.izpan.modules.base.domain.vo.BseSchoolVO;
import com.izpan.modules.base.facade.IBseSchoolFacade;
import com.izpan.modules.base.service.IBseSchoolService;
import com.izpan.modules.system.domain.dto.user.SysUserAddDTO;
import com.izpan.modules.system.domain.dto.user.SysUserUpdateNumberDto;
import com.izpan.modules.system.domain.entity.SysUser;
import com.izpan.modules.system.domain.vo.SysUserVO;
import com.izpan.modules.system.facade.ISysUserFacade;
import com.izpan.modules.system.service.ISysUserService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学校管理 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.facade.impl.BseSchoolFacadeImpl
 * @CreateTime 2024-12-05 - 22:49:40
 */

@Service
@RequiredArgsConstructor
public class BseSchoolFacadeImpl implements IBseSchoolFacade {

    @NonNull
    private IBseSchoolService bseSchoolService;

    @NonNull
    private ISysUserFacade sysUserFacade;

    @NonNull
    private ISysUserService sysUserService;

    @Override
    public RPage<BseSchoolVO> listBseSchoolPage(PageQuery pageQuery, BseSchoolSearchDTO bseSchoolSearchDTO) {
        BseSchoolBO bseSchoolBO = CglibUtil.convertObj(bseSchoolSearchDTO, BseSchoolBO::new);

        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            bseSchoolBO.setParentId(Long.valueOf(loginUser.getSchoolId()));
        }

        IPage<BseSchoolVO> bseSchoolIPage = bseSchoolService.listBseSchoolPage(pageQuery, bseSchoolBO);
        // 需要查询出管理员账号
        return RPage.build(bseSchoolIPage, BseSchoolVO::new);
    }

    @Override
    public BseSchoolInfoVO get(Long id, String type) {
        SysUser sysUser = sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getSchoolId, id).eq(SysUser::getUserType, type));
        SysUserVO sysUserVO = CglibUtil.convertObj(sysUser, SysUserVO::new);
        BseSchool bseSchool = bseSchoolService.getById(id);
        if (bseSchool != null) {
            BseSchoolInfoVO bseSchoolInfoVO = CglibUtil.convertObj(bseSchool, BseSchoolInfoVO::new);
            bseSchoolInfoVO.setSysUserVO(sysUserVO);
            return bseSchoolInfoVO;
        } else {
            throw new BizException("未匹配上学校信息");
        }
    }

    @Override
    @Transactional
    public Long add(BseSchoolAddDTO bseSchoolAddDTO) {
        BseSchoolBO bseSchoolBO = CglibUtil.convertObj(bseSchoolAddDTO, BseSchoolBO::new);
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            bseSchoolBO.setParentId(Long.valueOf(loginUser.getSchoolId()));
        }

        if (bseSchoolService.save(bseSchoolBO)) {
            SysUserAddDTO sysUserAddDTO = new SysUserAddDTO();
            sysUserAddDTO.setSchoolId(bseSchoolBO.getId().toString());
            sysUserAddDTO.setSchoolName(bseSchoolAddDTO.getSchoolName());
            sysUserAddDTO.setUserName(bseSchoolBO.getContactPhone());
            sysUserAddDTO.setUserType(bseSchoolAddDTO.getType().equals(1) ? SystemUserTypeEnum.AGENT.getValue() : SystemUserTypeEnum.SCHOOL_ADMIN.getValue());
            sysUserAddDTO.setNickName(bseSchoolAddDTO.getType().equals(1) ? bseSchoolAddDTO.getSchoolName() : bseSchoolAddDTO.getSchoolName() + "(管理员)");
            sysUserAddDTO.setPhone(bseSchoolAddDTO.getContactPhone());
            sysUserAddDTO.setRealName(bseSchoolAddDTO.getContactPerson());
            sysUserAddDTO.setEmail(bseSchoolAddDTO.getPrincipalMail());
            sysUserAddDTO.setPassword(bseSchoolAddDTO.getPassword());
            sysUserFacade.addUser(sysUserAddDTO);
        }
        return bseSchoolBO.getId();
    }


    @Override
    @Transactional
    public boolean update(BseSchoolUpdateDTO bseSchoolUpdateDTO) {
        BseSchoolBO bseSchoolBO = CglibUtil.convertObj(bseSchoolUpdateDTO, BseSchoolBO::new);
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            bseSchoolBO.setParentId(Long.valueOf(loginUser.getSchoolId()));
        }
        return bseSchoolService.updateById(bseSchoolBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BseSchoolDeleteDTO bseSchoolDeleteDTO) {
        BseSchoolBO bseSchoolBO = CglibUtil.convertObj(bseSchoolDeleteDTO, BseSchoolBO::new);
        return bseSchoolService.removeBatchByIds(bseSchoolBO.getIds(), true);
    }

    public List<BseSchool> getSchoolLists(String schoolName) {
        LambdaQueryWrapper<BseSchool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(!StrUtil.isEmpty(schoolName), BseSchool::getSchoolName, schoolName);
        lambdaQueryWrapper.eq(BseSchool::getStatus, 1);
        return bseSchoolService.list(lambdaQueryWrapper);
    }

    @Override
    public List<ProvinceInfo> getProvinceList(Long parentId) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            parentId = Long.valueOf(loginUser.getSchoolId());
        }
        List<BseSchool> bseSchools = bseSchoolService.list(new LambdaQueryWrapper<BseSchool>().select(BseSchool::getProvinceCode, BseSchool::getProvinceName).eq(BseSchool::getType, 2).eq(ObjectUtil.isNotEmpty(parentId), BseSchool::getParentId, parentId).groupBy(BseSchool::getProvinceCode, BseSchool::getProvinceName));
        return bseSchools.stream().filter(Objects::nonNull).filter(e -> StringUtils.isNotEmpty(e.getProvinceCode())).map(e -> new ProvinceInfo(e.getProvinceCode(), e.getProvinceName())).collect(Collectors.toList());
    }

    @Override
    public List<CityInfo> getCityList(String provinceCode, Long parentId) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            parentId = Long.valueOf(loginUser.getSchoolId());
        }

        LambdaQueryWrapper<BseSchool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(BseSchool::getCityCode, BseSchool::getCityName).eq(BseSchool::getProvinceCode, provinceCode).eq(BseSchool::getType, 2).eq(ObjectUtil.isNotEmpty(parentId), BseSchool::getParentId, parentId).groupBy(BseSchool::getCityCode, BseSchool::getCityName);

        return bseSchoolService.list(lambdaQueryWrapper).stream().map(e -> new CityInfo(e.getCityCode(), e.getCityName())).distinct().collect(Collectors.toList());
    }

    @Override
    public List<AreaInfo> getAreaList(String cityCode, Long parentId) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            parentId = Long.valueOf(loginUser.getSchoolId());
        }

        LambdaQueryWrapper<BseSchool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(BseSchool::getAreaCode, BseSchool::getAreaName).eq(BseSchool::getCityCode, cityCode).eq(BseSchool::getType, 2).eq(ObjectUtil.isNotEmpty(parentId), BseSchool::getParentId, parentId).groupBy(BseSchool::getAreaCode, BseSchool::getAreaName);

        return bseSchoolService.list(lambdaQueryWrapper).stream().map(e -> new AreaInfo(e.getAreaCode(), e.getAreaName())).distinct().collect(Collectors.toList());
    }

    @Override
    public List<BseSchool> getSchoolListByArea(String areaCode, Long parentId) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.AGENT.getValue().equals(loginUser.getUserType())) {
            parentId = Long.valueOf(loginUser.getSchoolId());
        }

        LambdaQueryWrapper<BseSchool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BseSchool::getAreaCode, areaCode)
                .eq(BseSchool::getType, 2).eq(ObjectUtil.isNotEmpty(parentId), BseSchool::getParentId, parentId);
        return bseSchoolService.list(lambdaQueryWrapper);
    }

    @Override
    public BseSchoolVO getSchoolNumberInfo() {
        // 获取当前登录用户
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (loginUser == null || StrUtil.isEmpty(loginUser.getSchoolId())) {
            throw new BizException("当前用户未关联学校信息");
        }

        // 查询学校信息
        BseSchool school = bseSchoolService.getById(loginUser.getSchoolId());
        if (school == null) {
            throw new BizException("未找到对应的学校信息");
        }

        // 返回微课次数和测评次数
        return CglibUtil.convertObj(school, BseSchoolVO::new);
    }

    @Override
    @Transactional
    public boolean batchAssignCounts(BseSchoolBatchAssignCountsDTO dto) {
        // 参数校验
        if (dto.getIds() == null || dto.getIds().isEmpty()) {
            throw new BizException("IDs列表不能为空");
        }

        if (dto.getMicroLessonsNumber() == null && dto.getEvaluationNumber() == null) {
            throw new BizException("微课次数和测评次数至少需要设置一项");
        }

        // 根据不同类型处理
        if (dto.getType() == null || dto.getType() == 1) {
            // 类型1：学校给用户分配次数
            return assignCountsFromSchoolToUser(dto);
        } else if (dto.getType() == 2) {
            // 类型2：代理商给学校分配次数
            return assignCountsFromSupplierToSchool(dto);
        } else {
            throw new BizException("不支持的分配类型: " + dto.getType());
        }
    }

    /**
     * 类型1：学校给用户分配次数
     */
    private boolean assignCountsFromSchoolToUser(BseSchoolBatchAssignCountsDTO dto) {
        // 获取当前登录用户所在学校
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (loginUser == null || StrUtil.isEmpty(loginUser.getSchoolId())) {
            throw new BizException("当前用户未关联学校信息");
        }

        BseSchool school = bseSchoolService.getById(loginUser.getSchoolId());
        if (school == null) {
            throw new BizException("未找到对应的学校信息");
        }

        // 计算需要分配的总次数
        int totalMicroLessonsCount = dto.getMicroLessonsNumber() != null ? dto.getMicroLessonsNumber() * dto.getIds().size() : 0;
        int totalEvaluationCount = dto.getEvaluationNumber() != null ? dto.getEvaluationNumber() * dto.getIds().size() : 0;

        // 检查学校剩余次数是否足够
        if (dto.getMicroLessonsNumber() != null && (school.getMicroLessonsNumber() == null || school.getMicroLessonsNumber() < totalMicroLessonsCount)) {
            throw new BizException("学校微课次数不足，需要" + totalMicroLessonsCount + "次，剩余" +
                    (school.getMicroLessonsNumber() != null ? school.getMicroLessonsNumber() : 0) + "次");
        }

        if (dto.getEvaluationNumber() != null && (school.getEvaluationNumber() == null || school.getEvaluationNumber() < totalEvaluationCount)) {
            throw new BizException("学校测评次数不足，需要" + totalEvaluationCount + "次，剩余" +
                    (school.getEvaluationNumber() != null ? school.getEvaluationNumber() : 0) + "次");
        }

        // 为每个用户分配次数
        for (String userIdStr : dto.getIds()) {
            Long userId = Long.valueOf(userIdStr);

            // 分配微课次数
            if (dto.getMicroLessonsNumber() != null && dto.getMicroLessonsNumber() > 0) {
                SysUserUpdateNumberDto microDto = new SysUserUpdateNumberDto();
                microDto.setId(userId);
                microDto.setType("add");
                microDto.setRowType("microLessonsNumber");
                microDto.setNumbers(dto.getMicroLessonsNumber());
                sysUserService.updateTimes(microDto);
            }

            // 分配测评次数
            if (dto.getEvaluationNumber() != null && dto.getEvaluationNumber() > 0) {
                SysUserUpdateNumberDto evalDto = new SysUserUpdateNumberDto();
                evalDto.setId(userId);
                evalDto.setType("add");
                evalDto.setRowType("evaluationNumber");
                evalDto.setNumbers(dto.getEvaluationNumber());
                sysUserService.updateTimes(evalDto);
            }
        }

        // 更新学校剩余次数
        if (dto.getMicroLessonsNumber() != null && dto.getMicroLessonsNumber() > 0) {
            school.setMicroLessonsNumber(school.getMicroLessonsNumber() - totalMicroLessonsCount);
        }

        if (dto.getEvaluationNumber() != null && dto.getEvaluationNumber() > 0) {
            school.setEvaluationNumber(school.getEvaluationNumber() - totalEvaluationCount);
        }

        return bseSchoolService.updateById(school);
    }

    /**
     * 类型2：代理商给学校分配次数
     * <p>
     * 此处实现代理商给学校批量分配次数的逻辑
     */
    private boolean assignCountsFromSupplierToSchool(BseSchoolBatchAssignCountsDTO dto) {
        // 校验学校ID列表
        if (dto.getIds() == null || dto.getIds().isEmpty()) {
            throw new BizException("学校ID列表不能为空");
        }

        // 校验次数
        if ((dto.getMicroLessonsNumber() == null || dto.getMicroLessonsNumber() <= 0)
                && (dto.getEvaluationNumber() == null || dto.getEvaluationNumber() <= 0)) {
            throw new BizException("微课次数和测评次数至少需要一项大于0");
        }

        // 查询所有需要更新的学校
        List<Long> schoolIds = dto.getIds().stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());

        List<BseSchool> schools = bseSchoolService.listByIds(schoolIds);

        // 检查是否所有学校都存在
        if (schools.size() != schoolIds.size()) {
            throw new BizException("部分学校ID不存在");
        }

        // 如果需要校验次数
        if (dto.getNeedValidate() != null && dto.getNeedValidate()) {
            // 获取当前登录用户（代理商）所在学校
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (loginUser == null || StrUtil.isEmpty(loginUser.getSchoolId())) {
                throw new BizException("当前用户未关联代理商信息");
            }

            BseSchool supplierSchool = bseSchoolService.getById(loginUser.getSchoolId());
            if (supplierSchool == null) {
                throw new BizException("未找到代理商信息");
            }

            // 计算需要分配的总次数
            int totalMicroLessonsCount = dto.getMicroLessonsNumber() != null ? dto.getMicroLessonsNumber() * dto.getIds().size() : 0;
            int totalEvaluationCount = dto.getEvaluationNumber() != null ? dto.getEvaluationNumber() * dto.getIds().size() : 0;

            // 检查代理商剩余次数是否足够
            if (dto.getMicroLessonsNumber() != null && dto.getMicroLessonsNumber() > 0 &&
                    (supplierSchool.getMicroLessonsNumber() == null || supplierSchool.getMicroLessonsNumber() < totalMicroLessonsCount)) {
                throw new BizException("代理商微课次数不足，需要" + totalMicroLessonsCount + "次，剩余" +
                        (supplierSchool.getMicroLessonsNumber() != null ? supplierSchool.getMicroLessonsNumber() : 0) + "次");
            }

            if (dto.getEvaluationNumber() != null && dto.getEvaluationNumber() > 0 &&
                    (supplierSchool.getEvaluationNumber() == null || supplierSchool.getEvaluationNumber() < totalEvaluationCount)) {
                throw new BizException("代理商测评次数不足，需要" + totalEvaluationCount + "次，剩余" +
                        (supplierSchool.getEvaluationNumber() != null ? supplierSchool.getEvaluationNumber() : 0) + "次");
            }

            // 更新代理商剩余次数
            if (dto.getMicroLessonsNumber() != null && dto.getMicroLessonsNumber() > 0) {
                supplierSchool.setMicroLessonsNumber(supplierSchool.getMicroLessonsNumber() - totalMicroLessonsCount);
            }

            if (dto.getEvaluationNumber() != null && dto.getEvaluationNumber() > 0) {
                supplierSchool.setEvaluationNumber(supplierSchool.getEvaluationNumber() - totalEvaluationCount);
            }

            // 更新代理商
            bseSchoolService.updateById(supplierSchool);
        }

        // 更新学校次数
        for (BseSchool school : schools) {
            // 更新微课次数
            if (dto.getMicroLessonsNumber() != null && dto.getMicroLessonsNumber() > 0) {
                Integer currentMicroLessons = school.getMicroLessonsNumber();
                if (currentMicroLessons == null) {
                    currentMicroLessons = 0;
                }
                school.setMicroLessonsNumber(currentMicroLessons + dto.getMicroLessonsNumber());
            }

            // 更新测评次数
            if (dto.getEvaluationNumber() != null && dto.getEvaluationNumber() > 0) {
                Integer currentEvaluations = school.getEvaluationNumber();
                if (currentEvaluations == null) {
                    currentEvaluations = 0;
                }
                school.setEvaluationNumber(currentEvaluations + dto.getEvaluationNumber());
            }
        }

        // 批量更新学校
        return bseSchoolService.updateBatchById(schools);
    }
}