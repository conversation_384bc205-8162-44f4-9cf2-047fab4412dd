/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.domain.dto.school;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 批量分配次数 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.domain.dto.school.BseSchoolBatchAssignCountsDTO
 * @CreateTime 2024-03-21
 */
@Getter
@Setter
@Schema(name = "BseSchoolBatchAssignCountsDTO", description = "批量分配次数 DTO 对象")
public class BseSchoolBatchAssignCountsDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "ids列表")
    private List<String> ids;

    @Schema(description = "分配类型: 1-学校给用户分配, 2-供应商给学校分配")
    private Integer type;

    @Schema(description = "每人分配的微课次数")
    private Integer microLessonsNumber;

    @Schema(description = "每人分配的测评次数")
    private Integer evaluationNumber;

    @Schema(description = "是否校验次数: true-校验次数是否足够, false-不校验（默认true）")
    private Boolean needValidate = true;
} 