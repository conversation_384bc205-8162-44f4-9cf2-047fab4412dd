/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.dto.article;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
* 文章表 编辑更新 DTO 对象
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.dto.article.BseArticleUpdateDTO
* @CreateTime 2025-03-24 - 11:17:41
*/

@Getter
@Setter
@Schema(name = "BseArticleUpdateDTO", description = "文章表 编辑更新 DTO 对象")
public class BseArticleUpdateDTO implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "文章类型（如记叙文、议论文等）")
    private String articleType;

    @Schema(description = "文章主题（如爱国、节气等）")
    private String theme;

    @Schema(description = "文章字数")
    private Integer wordCount;

    @Schema(description = "适用年级")
    private String grade;

    @Schema(description = "作文内容")
    private String content;

    @Schema(description = "来源（如deepseek、kimi）")
    private String source;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "是否启用(0:禁用,1:启用)")
    private String status;

}