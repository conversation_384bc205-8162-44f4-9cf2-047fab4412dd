/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.vo;

import com.izpan.infrastructure.domain.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
* 基础服务-套餐信息表 VO 展示类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.vo.BsePackageVO
* @CreateTime 2025-07-20 - 12:54:18
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BsePackageVO", description = "基础服务-套餐信息表 VO 对象")
public class BsePackageVO extends BaseVO {

    @Schema(description = "套餐名称 (例如：微课套餐，测评套餐)")
    private String name;

    @Schema(description = "套餐类型 (例如：microLessons-微课, evaluation-测评)")
    private String type;

    @Schema(description = "套餐的详细描述信息")
    private String description;

    @Schema(description = "价格，单位为“分”，以避免浮点数精度问题")
    private Double price;

    @Schema(description = "赠送次数")
    private Integer giftCount;

    @Schema(description = "套餐状态 (0:下架, 1:上架)")
    private String status;

    @Schema(description = "排序值，用于前端展示顺序，值越小越靠前")
    private Integer sortOrder;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}