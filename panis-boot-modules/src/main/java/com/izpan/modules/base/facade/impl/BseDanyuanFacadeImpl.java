/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BseDanyuanBO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanAddDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanDeleteDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanSearchDTO;
import com.izpan.modules.base.domain.dto.danyuan.BseDanyuanUpdateDTO;
import com.izpan.modules.base.domain.entity.BseDanyuan;
import com.izpan.modules.base.domain.vo.BseDanyuanVO;
import com.izpan.modules.base.facade.IBseDanyuanFacade;
import com.izpan.modules.base.service.IBseDanyuanService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 单元教学信息表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.facade.impl.BseDanyuanFacadeImpl
 * @CreateTime 2025-03-25 - 20:45:00
 */

@Service
@RequiredArgsConstructor
public class BseDanyuanFacadeImpl implements IBseDanyuanFacade {

    @NonNull
    private IBseDanyuanService bseDanyuanService;

    @Override
    public RPage<BseDanyuanVO> listBseDanyuanPage(PageQuery pageQuery, BseDanyuanSearchDTO bseDanyuanSearchDTO) {
        BseDanyuanBO bseDanyuanBO = CglibUtil.convertObj(bseDanyuanSearchDTO, BseDanyuanBO::new);
        IPage<BseDanyuan> bseDanyuanIPage = bseDanyuanService.listBseDanyuanPage(pageQuery, bseDanyuanBO);
        return RPage.build(bseDanyuanIPage, BseDanyuanVO::new);
    }

    @Override
    public BseDanyuanVO get(Long id) {
        BseDanyuan byId = bseDanyuanService.getById(id);
        return CglibUtil.convertObj(byId, BseDanyuanVO::new);
    }

    @Override
    @Transactional
    public boolean add(BseDanyuanAddDTO bseDanyuanAddDTO) {
        BseDanyuanBO bseDanyuanBO = CglibUtil.convertObj(bseDanyuanAddDTO, BseDanyuanBO::new);
        return bseDanyuanService.save(bseDanyuanBO);
    }

    @Override
    @Transactional
    public boolean update(BseDanyuanUpdateDTO bseDanyuanUpdateDTO) {
        BseDanyuanBO bseDanyuanBO = CglibUtil.convertObj(bseDanyuanUpdateDTO, BseDanyuanBO::new);
        return bseDanyuanService.updateById(bseDanyuanBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BseDanyuanDeleteDTO bseDanyuanDeleteDTO) {
        BseDanyuanBO bseDanyuanBO = CglibUtil.convertObj(bseDanyuanDeleteDTO, BseDanyuanBO::new);
        return bseDanyuanService.removeBatchByIds(bseDanyuanBO.getIds(), true);
    }

    @Override
    public List<BseDanyuanVO> list(BseDanyuanSearchDTO bseDanyuanSearchDTO) {
        BseDanyuanBO bseDanyuanBO = CglibUtil.convertObj(bseDanyuanSearchDTO, BseDanyuanBO::new);
        List<BseDanyuan> list = bseDanyuanService.list(new LambdaQueryWrapper<BseDanyuan>().
                like(bseDanyuanBO.getDDanyuan() != null, BseDanyuan::getDDanyuan, bseDanyuanBO.getDDanyuan()).
                eq(bseDanyuanBO.getDGjc() != null, BseDanyuan::getDGjc, bseDanyuanBO.getDGjc()).
                like(bseDanyuanBO.getDGrade() != null, BseDanyuan::getDGrade, bseDanyuanBO.getDGrade()).
                orderByDesc(BseDanyuan::getCreateTime));
        return CglibUtil.convertList(list, BseDanyuanVO::new);
    }

}