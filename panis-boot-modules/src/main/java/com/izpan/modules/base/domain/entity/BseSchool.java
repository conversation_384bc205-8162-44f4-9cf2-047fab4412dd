/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 学校管理 Entity 实体类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.domain.entity.BseSchool
 * @CreateTime 2025-04-17 - 15:11:33
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("bse_school")
public class BseSchool extends BaseEntity {

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 类型:1代理2学校
     */
    private Integer type;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 校长名称
     */
    private String principalName;

    /**
     * 校长电话
     */
    private String principalPhone;

    /**
     * 校长邮箱
     */
    private String principalMail;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系号码
     */
    private String contactPhone;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区名称
     */
    private String areaName;

    /**
     * 所在地址(详细地址)
     */
    private String belongAddress;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 微课次数
     */
    private Integer microLessonsNumber;

    /**
     * 测评次数
     */
    private Integer evaluationNumber;

    /**
     * 状态(0:禁用,1:启用)
     */
    private String status;

    /**
     * 映射id
     */
    private Long mappingId;

}