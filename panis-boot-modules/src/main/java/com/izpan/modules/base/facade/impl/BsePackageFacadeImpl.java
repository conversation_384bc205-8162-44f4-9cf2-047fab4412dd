/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BsePackageBO;
import com.izpan.modules.base.domain.dto.packages.BsePackageAddDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageDeleteDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageSearchDTO;
import com.izpan.modules.base.domain.dto.packages.BsePackageUpdateDTO;
import com.izpan.modules.base.domain.entity.BsePackage;
import com.izpan.modules.base.domain.vo.BsePackageVO;
import com.izpan.modules.base.domain.vo.BsePackageMiniVO;
import com.izpan.modules.base.facade.IBsePackageFacade;
import com.izpan.modules.base.service.IBsePackageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 基础服务-套餐信息表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.impl.BsePackageFacadeImpl
 * @CreateTime 2025-07-20 - 12:54:18
 */

@Service
@RequiredArgsConstructor
public class BsePackageFacadeImpl implements IBsePackageFacade {

    @NonNull
    private IBsePackageService bsePackageService;

    @Override
    public RPage<BsePackageVO> listBsePackagePage(PageQuery pageQuery, BsePackageSearchDTO bsePackageSearchDTO) {
        BsePackageBO bsePackageBO = CglibUtil.convertObj(bsePackageSearchDTO, BsePackageBO::new);
        IPage<BsePackage> bsePackageIPage = bsePackageService.listBsePackagePage(pageQuery, bsePackageBO);
        return RPage.build(bsePackageIPage, BsePackageVO::new);
    }

    @Override
    public BsePackageVO get(Long id) {
        BsePackage byId = bsePackageService.getById(id);
        return CglibUtil.convertObj(byId, BsePackageVO::new);
    }

    @Override
    @Transactional
    public boolean add(BsePackageAddDTO bsePackageAddDTO) {
        BsePackageBO bsePackageBO = CglibUtil.convertObj(bsePackageAddDTO, BsePackageBO::new);
        return bsePackageService.save(bsePackageBO);
    }

    @Override
    @Transactional
    public boolean update(BsePackageUpdateDTO bsePackageUpdateDTO) {
        BsePackageBO bsePackageBO = CglibUtil.convertObj(bsePackageUpdateDTO, BsePackageBO::new);
        return bsePackageService.updateById(bsePackageBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BsePackageDeleteDTO bsePackageDeleteDTO) {
        BsePackageBO bsePackageBO = CglibUtil.convertObj(bsePackageDeleteDTO, BsePackageBO::new);
        return bsePackageService.removeBatchByIds(bsePackageBO.getIds(), true);
    }

    @Override
    public List<BsePackageMiniVO> listMiniPackages() {
        List<BsePackage> packages = bsePackageService.listMiniPackages();
        return packages.stream()
                .map(pkg -> CglibUtil.convertObj(pkg, BsePackageMiniVO::new))
                .collect(Collectors.toList());
    }

}