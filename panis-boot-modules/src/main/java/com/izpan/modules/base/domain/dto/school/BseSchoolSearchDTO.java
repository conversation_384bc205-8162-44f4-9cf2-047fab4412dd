/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.domain.dto.school;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 学校管理 查询 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.domain.dto.school.BseSchoolSearchDTO
 * @CreateTime 2025-04-17 - 15:11:33
 */

@Getter
@Setter
@Schema(name = "BseSchoolSearchDTO", description = "学校管理 查询 DTO 对象")
public class BseSchoolSearchDTO implements Serializable {

    @Schema(description = "类型:1代理2学校")
    private Integer type;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系号码")
    private String contactPhone;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "区名称")
    private String areaName;

    @Schema(description = "所在地址(详细地址)")
    private String belongAddress;

}