/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BseCorpusBO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusAddDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusDeleteDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusSearchDTO;
import com.izpan.modules.base.domain.dto.corpus.BseCorpusUpdateDTO;
import com.izpan.modules.base.domain.entity.BseCorpus;
import com.izpan.modules.base.domain.vo.BseCorpusVO;
import com.izpan.modules.base.facade.IBseCorpusFacade;
import com.izpan.modules.base.service.IBseCorpusService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

/**
 *  门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.impl.BseCorpusFacadeImpl
 * @CreateTime 2024-12-14 - 14:49:29
 */

@Service
@RequiredArgsConstructor
public class BseCorpusFacadeImpl implements IBseCorpusFacade {

    @NonNull
    private IBseCorpusService bseCorpusService;

    @Override
    public RPage<BseCorpusVO> listBseCorpusPage(PageQuery pageQuery, BseCorpusSearchDTO bseCorpusSearchDTO) {
        BseCorpusBO bseCorpusBO = CglibUtil.convertObj(bseCorpusSearchDTO, BseCorpusBO::new);
        IPage<BseCorpus> bseCorpusIPage = bseCorpusService.listBseCorpusPage(pageQuery, bseCorpusBO);
        return RPage.build(bseCorpusIPage, BseCorpusVO::new);
    }

    @Override
    public BseCorpusVO get(Long id) {
        BseCorpus byId = bseCorpusService.getById(id);
        return CglibUtil.convertObj(byId, BseCorpusVO::new);
    }

    @Override
    @Transactional
    public boolean add(BseCorpusAddDTO bseCorpusAddDTO) {
        BseCorpusBO bseCorpusBO = CglibUtil.convertObj(bseCorpusAddDTO, BseCorpusBO::new);
        LoginUser loginUser = GlobalUserHolder.getUser();
        bseCorpusBO.setSchoolId(loginUser.getSchoolId());
        bseCorpusBO.setSchoolName(loginUser.getSchoolName());
        return bseCorpusService.save(bseCorpusBO);
    }

    @Override
    @Transactional
    public boolean update(BseCorpusUpdateDTO bseCorpusUpdateDTO) {
        BseCorpusBO bseCorpusBO = CglibUtil.convertObj(bseCorpusUpdateDTO, BseCorpusBO::new);
        return bseCorpusService.updateById(bseCorpusBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BseCorpusDeleteDTO bseCorpusDeleteDTO) {
        BseCorpusBO bseCorpusBO = CglibUtil.convertObj(bseCorpusDeleteDTO, BseCorpusBO::new);
        return bseCorpusService.removeBatchByIds(bseCorpusBO.getIds(), true);
    }

}