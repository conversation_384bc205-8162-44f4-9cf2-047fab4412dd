<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.base.repository.mapper.BseSchoolMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BseSchoolResultMap" type="com.izpan.modules.base.domain.entity.BseSchool">
        <result column="parent_id" property="parentId"/>
        <result column="type" property="type"/>
        <result column="school_name" property="schoolName"/>
        <result column="principal_name" property="principalName"/>
        <result column="principal_phone" property="principalPhone"/>
        <result column="principal_mail" property="principalMail"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="area_code" property="areaCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_name" property="cityName"/>
        <result column="area_name" property="areaName"/>
        <result column="belong_address" property="belongAddress"/>
        <result column="expire_time" property="expireTime"/>
        <result column="micro_lessons_number" property="microLessonsNumber"/>
        <result column="evaluation_number" property="evaluationNumber"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BseSchoolColumnList">
        id,
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        is_deleted,
        parent_id, `type`, school_name, principal_name, principal_phone, principal_mail, contact_person, contact_phone, province_code, city_code, area_code, province_name, city_name, area_name, belong_address, expire_time, micro_lessons_number, evaluation_number, `status`
    </sql>

</mapper>
