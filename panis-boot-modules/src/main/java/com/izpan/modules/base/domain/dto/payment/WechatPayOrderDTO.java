/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.domain.dto.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;

import java.io.Serializable;

/**
 * 微信支付订单创建 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO
 * @CreateTime 2025-07-20
 */
@Data
@Schema(name = "WechatPayOrderDTO", description = "微信支付订单创建 DTO 对象")
public class WechatPayOrderDTO implements Serializable {

    @Schema(description = "支付用户的ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "关联的套餐ID", required = true)
    @NotNull(message = "套餐ID不能为空")
    private Long packageId;

    @Schema(description = "套餐信息快照", required = true)
    @NotBlank(message = "套餐信息不能为空")
    private String packageSnapshot;

    @Schema(description = "应付金额（单位：分）", required = true)
    @NotNull(message = "支付金额不能为空")
    @Min(value = 1, message = "支付金额必须大于0")
    private Integer amountPayable;

    @Schema(description = "商品描述", required = true)
    @NotBlank(message = "商品描述不能为空")
    private String body;

    @Schema(description = "用户openid（小程序支付必需）", required = true)
    @NotBlank(message = "用户openid不能为空")
    private String openid;

    @Schema(description = "订单过期时间（分钟）", example = "30")
    private Integer expireMinutes = 30;

}
