/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.dto.corpus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
*  查询 DTO 对象
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.dto.corpus.BseCorpusSearchDTO
* @CreateTime 2024-12-14 - 14:49:29
*/

@Getter
@Setter
@Schema(name = "BseCorpusSearchDTO", description = " 查询 DTO 对象")
public class BseCorpusSearchDTO implements Serializable {

    @Schema(description = "语料类型")
    private String category;

    @Schema(description = "语料内容")
    private String content;

    @Schema(description = "是否启用(0:禁用,1:启用)")
    private String status;

}