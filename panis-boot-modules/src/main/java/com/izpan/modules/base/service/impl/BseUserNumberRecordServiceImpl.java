/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BseUserNumberRecordBO;
import com.izpan.modules.base.domain.entity.BseUserNumberRecord;
import com.izpan.modules.base.repository.mapper.BseUserNumberRecordMapper;
import com.izpan.modules.base.service.IBseUserNumberRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 *  Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BseUserNumberRecordServiceImpl
 * @CreateTime 2025-03-24 - 22:55:48
 */

@Service
public class BseUserNumberRecordServiceImpl extends ServiceImpl<BseUserNumberRecordMapper, BseUserNumberRecord> implements IBseUserNumberRecordService {

    @Override
    public IPage<BseUserNumberRecord> listBseUserNumberRecordPage(PageQuery pageQuery, BseUserNumberRecordBO bseUserNumberRecordBO) {
        LambdaQueryWrapper<BseUserNumberRecord> queryWrapper = new LambdaQueryWrapper<BseUserNumberRecord>()
            .eq(ObjectUtils.isNotEmpty(bseUserNumberRecordBO.getBusinessType()), BseUserNumberRecord::getBusinessType, bseUserNumberRecordBO.getBusinessType())
            .eq(ObjectUtils.isNotEmpty(bseUserNumberRecordBO.getBusinessId()), BseUserNumberRecord::getBusinessId, bseUserNumberRecordBO.getBusinessId())
            .eq(ObjectUtils.isNotEmpty(bseUserNumberRecordBO.getOperationType()), BseUserNumberRecord::getOperationType, bseUserNumberRecordBO.getOperationType())
            .eq(ObjectUtils.isNotEmpty(bseUserNumberRecordBO.getUserId()), BseUserNumberRecord::getUserId, bseUserNumberRecordBO.getUserId()).orderByDesc(BseUserNumberRecord::getOperationTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

