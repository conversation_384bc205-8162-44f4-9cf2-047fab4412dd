/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.base.domain.dto.danyuan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
* 单元教学信息表 新增 DTO 对象
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.base.domain.dto.danyuan.BseDanyuanAddDTO
* @CreateTime 2025-03-25 - 20:45:00
*/

@Getter
@Setter
@Schema(name = "BseDanyuanAddDTO", description = "单元教学信息表 新增 DTO 对象")
public class BseDanyuanAddDTO implements Serializable {

    @Schema(description = "年级")
    private String dGrade;

    @Schema(description = "单元名称")
    private String dDanyuan;

    @Schema(description = "课程名称")
    private String dName;

    @Schema(description = "关键词")
    private String dGjc;

    @Schema(description = "文体类型")
    private String dWenti;

    @Schema(description = "是否显示(0/1)")
    private Integer dIsshow;

    @Schema(description = "指标版本说明")
    private String dZbbb;

    @Schema(description = "导学内容")
    private String dDaoxue;

}