/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 微信支付结果 VO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.domain.vo.WechatPayResultVO
 * @CreateTime 2025-07-20
 */
@Data
@Schema(name = "WechatPayResultVO", description = "微信支付结果 VO 对象")
public class WechatPayResultVO implements Serializable {

    @Schema(description = "商户内部订单号")
    private String orderNo;

    @Schema(description = "微信支付参数（用于小程序调起支付）")
    private Map<String, Object> payParams;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "支付状态")
    private String status;

    @Schema(description = "错误信息")
    private String errorMsg;

}
