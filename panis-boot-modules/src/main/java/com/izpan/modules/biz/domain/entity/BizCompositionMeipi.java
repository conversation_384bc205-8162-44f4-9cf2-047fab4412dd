/*
* All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
* Open Source Agreement: Apache License, Version 2.0
* For educational purposes only, commercial use shall comply with the author's copyright information.
* The author does not guarantee or assume any responsibility for the risks of using software.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

package com.izpan.modules.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BaseEntity;
import java.io.Serializable;
import com.izpan.infrastructure.domain.BaseVO;
import com.izpan.infrastructure.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
    import java.io.Serial;

/**
* 作文眉批表 Entity 实体类
*
* <AUTHOR>
* @ProjectName panis-boot
* @ClassName com.izpan.modules.biz.domain.entity.BizCompositionMeipi
* @CreateTime 2025-04-09 - 12:23:00
*/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("biz_composition_meipi")
public class BizCompositionMeipi extends BizEntity {

    /**
    * 段落下标
    */
    private Integer paragraph;

    /**
    * 眉批评语
    */
    private String sentence;

    /**
    * 原文句子
    */
    private String original;

    /**
    * 建议
    */
    private String advise;

}