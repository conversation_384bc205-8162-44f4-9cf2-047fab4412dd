<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.biz.repository.mapper.BizCompositionCtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BizCompositionCtResultMap" type="com.izpan.modules.biz.domain.entity.BizCompositionCt">
        <result column="cl_id" property="clId"/>
        <result column="Ctitle" property="Ctitle"/>
        <result column="Cstyle" property="Cstyle"/>
        <result column="Cpercentage" property="Cpercentage"/>
        <result column="user_id" property="userId"/>
        <result column="school_id" property="schoolId"/>
        <result column="grade_id" property="gradeId"/>
        <result column="clazz_id" property="clazzId"/>
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="Cdata" property="Cdata" typeHandler="com.izpan.infrastructure.handler.JsonTypeHandler"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BizCompositionCtColumnList">
        id,
        create_time,
        is_deleted,
        cl_id, Ctitle, Cstyle, Cpercentage, Cdata, user_id, school_id, grade_id, clazz_id
    </sql>

</mapper>
