/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.biz.domain.dto.composition.log.*;
import com.izpan.modules.biz.domain.entity.BizCompositionLog;
import com.izpan.modules.biz.domain.vo.BizCompBatchEvaluateVO;
import com.izpan.modules.biz.domain.vo.BizCompositionHistoryLogVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogExportVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;

import java.util.List;
import java.util.Map;

/**
 * 作文记录表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.facade.IBizCompositionLogFacade
 * @CreateTime 2025-03-23 - 12:45:30
 */

public interface IBizCompositionLogFacade {

    /**
     * 作文记录表 - 分页查询
     *
     * @param pageQuery                  分页对象
     * @param bizCompositionLogSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-03-23 - 12:45:30
     */
    RPage<BizCompositionLogVO> listBizCompositionLogPage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO);


    /**
     * 获取登录者的作文记录表列表
     * 
     * @param pageQuery                  分页对象
     * @param bizCompositionLogSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2025-04-22 - 12:45:30
     */
    RPage<BizCompositionHistoryLogVO> userPage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 作文记录表ID
     * @return {@link BizCompositionLogVO} 作文记录表 VO 对象
     * <AUTHOR>
     * @CreateTime 2025-03-23 - 12:45:30
     */
    BizCompositionLogVO get(Long id);

    /**
     * 新增作文记录表
     *
     * @param bizCompositionLogAddDTO 新增作文记录表 DTO 对象
     * @return {@link Long} 新增记录的ID
     * <AUTHOR>
     * @CreateTime 2025-03-23 - 12:45:30
     */
    Long add(BizCompositionLogAddDTO bizCompositionLogAddDTO);

    /**
     * 编辑更新作文记录表信息
     *
     * @param bizCompositionLogUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-23 - 12:45:30
     */
    boolean update(BizCompositionLogUpdateDTO bizCompositionLogUpdateDTO);

    /**
     * 批量删除作文记录表信息
     *
     * @param bizCompositionLogDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-03-23 - 12:45:30
     */
    boolean batchDelete(BizCompositionLogDeleteDTO bizCompositionLogDeleteDTO);


    /**
     * 获取作文记录表列表
     *
     * @param bizCompositionLogSearchDTO 查询对象
     * @return {@link List<BizCompositionLogVO>} 作文记录表 VO 列表
     * @CreateTime 2025-03-23 - 12:45:30
     */
    List<BizCompositionLog> list(BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 统计班级学生数量和提交作文人数
     *
     * @param bizCompositionLogSearchDTO 查询对象
     * @return 包含班级总人数和提交作文人数的Map
     */
    Map<String, Object> countByClazz(BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 批量测评记录
     *
     * @param bizCompositionLogSearchDTO 查询对象
     * @return {@link RPage<BizCompositionLogVO>} 批量测评记录
     */
    RPage<BizCompBatchEvaluateVO> listBatchEvaluatePage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 手动匹配 学生 和 作文
     *
     * @param bizCompositionLogMatchStudentDTO 查询对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2025-04-18 - 12:45:30
     */
    boolean matchStudentAndComposition(BizCompositionLogMatchStudentDTO bizCompositionLogMatchStudentDTO);

    /**
     * 获取登录者的作文记录表列表-次
     *
     * @param bizCompositionLogSearchDTO 查询对象
     * @return {@link List<BizCompositionHistoryLogVO>} 作文记录表 VO 列表
     */
    List<BizCompositionHistoryLogVO> userList(BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 导出功能获取作文记录表列表（包含初始分数和最终分数）
     *
     * @param bizCompositionLogSearchDTO 查询对象
     * @return {@link List<BizCompositionLogExportVO>} 作文记录导出VO列表（包含初始分数和最终分数）
     * @CreateTime 2025-04-16
     */
    List<BizCompositionLogExportVO> listForExport(BizCompositionLogExportSearchDTO bizCompositionLogSearchDTO);

    /**
     * 同步新增作文记录表
     *
     * @param bizCompositionLogAddDTO 新增作文记录表 DTO 对象
     * @return {@link Long} 新增记录的ID
     * <AUTHOR>
     * @CreateTime 2025-04-17 - 12:45:30
     */
    Long syncAdd(BizCompositionLogAddDTO bizCompositionLogAddDTO);
}