/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.dto.composition.release;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 作文发布表 查询 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.dto.composition.release.BizCompositionReleaseSearchDTO
 * @CreateTime 2024-12-18 - 12:57:47
 */

@Getter
@Setter
@Schema(name = "BizCompositionReleaseSearchDTO", description = "作文发布表 查询 DTO 对象")
public class BizCompositionReleaseSearchDTO implements Serializable {

    @Schema(description = "发布者ID")
    private Long releaseId;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "年级编码")
    private String gradeId;

    @Schema(description = "班级编码")
    private String clazzId;

    @Schema(description = "发布状态")
    private Integer compositionStatus;

    @Schema(description = "作文标题")
    private String compositionTitle;

    @Schema(description = "作文类型")
    private String compositionType;

    @Schema(description = "是否为模版")
    private String isTemplate;

}