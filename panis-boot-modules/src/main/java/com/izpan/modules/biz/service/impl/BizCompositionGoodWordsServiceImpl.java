/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import com.izpan.modules.biz.domain.bo.BizCompositionGoodWordsBO;
import com.izpan.modules.biz.domain.entity.BizCompositionGoodWords;
import com.izpan.modules.biz.repository.mapper.BizCompositionGoodWordsMapper;
import com.izpan.modules.biz.service.IBizCompositionGoodWordsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 作文好词好句表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionGoodWordsServiceImpl
 * @CreateTime 2025-04-09 - 11:20:19
 */

@Service
public class BizCompositionGoodWordsServiceImpl extends ServiceImpl<BizCompositionGoodWordsMapper, BizCompositionGoodWords> implements IBizCompositionGoodWordsService {

    @Override
    public IPage<BizCompositionGoodWords> listBizCompositionGoodWordsPage(PageQuery pageQuery, BizCompositionGoodWordsBO bizCompositionGoodWordsBO) {
        LambdaQueryWrapper<BizCompositionGoodWords> queryWrapper = new LambdaQueryWrapper<BizCompositionGoodWords>()
                .eq(ObjectUtils.isNotEmpty(bizCompositionGoodWordsBO.getClId()), BizCompositionGoodWords::getClId, bizCompositionGoodWordsBO.getClId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionGoodWordsBO.getGname()), BizCompositionGoodWords::getGname, bizCompositionGoodWordsBO.getGname())
                .eq(ObjectUtils.isNotEmpty(bizCompositionGoodWordsBO.getUserId()), BizCompositionGoodWords::getUserId, bizCompositionGoodWordsBO.getUserId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionGoodWordsBO.getSchoolId()), BizCompositionGoodWords::getSchoolId, bizCompositionGoodWordsBO.getSchoolId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionGoodWordsBO.getGradeId()), BizCompositionGoodWords::getGradeId, bizCompositionGoodWordsBO.getGradeId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionGoodWordsBO.getClazzId()), BizCompositionGoodWords::getClazzId, bizCompositionGoodWordsBO.getClazzId()).orderByDesc(BizCompositionGoodWords::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

