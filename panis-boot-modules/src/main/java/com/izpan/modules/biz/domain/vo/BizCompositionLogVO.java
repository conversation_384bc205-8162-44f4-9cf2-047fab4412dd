/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.izpan.infrastructure.domain.BaseVO;
import com.izpan.modules.biz.domain.entity.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作文记录表 VO 展示类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.vo.BizCompositionLogVO
 * @CreateTime 2025-03-23 - 12:45:30
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BizCompositionLogVO", description = "作文记录表 VO 对象")
public class BizCompositionLogVO extends BaseVO {

    @Schema(description = "作业ID")
    private Long initLogId;

    @Schema(description = "作业ID")
    private Long compositionId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "作文要求题目")
    private String requireTitle;

    @Schema(description = "作文文体")
    private String wenTi;

    @Schema(description = "作文要求文体")
    private String requireWenTi;

    @Schema(description = "作文字数")
    private Integer words;

    @Schema(description = "作文要求字数")
    private Integer requireWords;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "年级")
    private String grade;

    @Schema(description = "星标")
    private Integer star;

    @Schema(description = "分数")
    private Double score;

    @Schema(description = "百分比分数")
    private Double percentage;

    @Schema(description = "最高分数")
    private Double maxScore;

    @Schema(description = "作文满分分数")
    private Double fullScore;

    @Schema(description = "累计测评次数")
    private Integer evaluationCount;

    @Schema(description = "作文归类")
    private String classified;

    @Schema(description = "段落数")
    private Integer paragraphs;

    @Schema(description = "句子数")
    private Integer sentences;

    @Schema(description = "作文路径")
    private String compositionPath;

    @Schema(description = "测文评价内容")
    private String evaluateContent;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "测文建议")
    private String advice;

    @Schema(description = "测文新评语")
    private String comments;

    @Schema(description = "敏感词：是否有敏感词，-1有，0无")
    private String sensitive;

    @Schema(description = "应答状态:应答状态:0评测 5老师批量评测 10已提交 15已批阅")
    private Integer answerStatus;

    @Schema(description = "测文来源：0自主，1作业，2测验")
    private Integer evaluateSources;

    @Schema(description = "老师评价")
    private String teacherEvaluate;

    @Schema(description = "应答者ID")
    private Long answerId;

    @Schema(description = "应答者学号")
    private String answerStudentNum;

    @Schema(description = "应答者名称")
    private String answerName;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "批阅时间")
    private LocalDateTime reviewTime;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "年级ID")
    private String gradeId;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "班级ID")
    private String clazzId;

    @Schema(description = "班级名称")
    private String clazzName;

    @Schema(description = "作文类别")
    private String compositionType;

    @Schema(description = "作文文体")
    private String compositionWenTi;

    @Schema(description = "作业内容要求")
    private String compositionContent;

    @Schema(description = "字数要求")
    private Integer compositionWordCount;

    @Schema(description = "作文标题")
    private String compositionTitle;

    @Schema(description = "参考内容路径")
    private String compositionExamplePath;

    @Schema(description = "作业资源路径")
    private String compositionResourcePath;

    @Schema(description = "作文开始时间")
    private LocalDateTime compositionBeginTime;

    @Schema(description = "作文截至时间")
    private LocalDateTime compositionEndTime;

    @Schema(description = "发布状态:0 草稿 1 发文")
    private Integer compositionStatus;

    @Schema(description = "测文返回段落内容")
    @JsonProperty("ContentList")
    private List<BizCompositionContent> contentList;

    @Schema(description = "测文返回好词好句")
    @JsonProperty("GoodWordsList")
    private List<BizCompositionGoodWords> goodWordsList;

    @Schema(description = "测文返回错别字")
    @JsonProperty("ErrorList")
    private List<BizCompositionErrorLog> errorList;

    @Schema(description = "测文返回题记")
    @JsonProperty("JiList")
    private List<BizCompositionJi> jiList;

    @Schema(description = "测文返回眉批")
    @JsonProperty("MeiList")
    private List<BizCompositionMeipi> meiList;

    @Schema(description = "测文返回CT")
    @JsonProperty("CTList")
    private List<BizCompositionCt> ctList;

    @Schema(description = "分数排名")
    private Integer scoreRanking;

}