/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import com.izpan.modules.biz.domain.bo.BizCompositionJiBO;
import com.izpan.modules.biz.domain.entity.BizCompositionJi;
import com.izpan.modules.biz.repository.mapper.BizCompositionJiMapper;
import com.izpan.modules.biz.service.IBizCompositionJiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 作文题记表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionJiServiceImpl
 * @CreateTime 2025-04-09 - 12:27:46
 */

@Service
public class BizCompositionJiServiceImpl extends ServiceImpl<BizCompositionJiMapper, BizCompositionJi> implements IBizCompositionJiService {

    @Override
    public IPage<BizCompositionJi> listBizCompositionJiPage(PageQuery pageQuery, BizCompositionJiBO bizCompositionJiBO) {
        LambdaQueryWrapper<BizCompositionJi> queryWrapper = new LambdaQueryWrapper<BizCompositionJi>()
            .eq(ObjectUtils.isNotEmpty(bizCompositionJiBO.getClId()), BizCompositionJi::getClId, bizCompositionJiBO.getClId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionJiBO.getUserId()), BizCompositionJi::getUserId, bizCompositionJiBO.getUserId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionJiBO.getSchoolId()), BizCompositionJi::getSchoolId, bizCompositionJiBO.getSchoolId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionJiBO.getGradeId()), BizCompositionJi::getGradeId, bizCompositionJiBO.getGradeId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionJiBO.getClazzId()), BizCompositionJi::getClazzId, bizCompositionJiBO.getClazzId()).orderByDesc(BizCompositionJi::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

