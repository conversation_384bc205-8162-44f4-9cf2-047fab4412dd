package com.izpan.modules.biz.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.izpan.modules.biz.domain.entity.BizCompositionRelease;
import com.izpan.modules.biz.domain.entity.BizCompositionReleaseExtend;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BizCompositionReleaseExtendMapper extends BaseMapper<BizCompositionReleaseExtend> {

    @Delete("DELETE FROM biz_composition_release_extend WHERE composition_id = #{compositionId}")
    int deletePhysicallyByCompositionId(@Param("compositionId") Long compositionId);

}
