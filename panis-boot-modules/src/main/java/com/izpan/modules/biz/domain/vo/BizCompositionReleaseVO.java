/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.vo;

import com.izpan.infrastructure.domain.BaseVO;
import com.izpan.modules.biz.domain.entity.BizCompositionReleaseExtend;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作文发布表 VO 展示类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO
 * @CreateTime 2024-12-18 - 12:57:47
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BizCompositionReleaseVO", description = "作文发布表 VO 对象")
public class BizCompositionReleaseVO extends BaseVO {

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "发布类别：0 平台 1 本校 2 年级 3 班级")
    private Integer releaseType;

    @Schema(description = "年级编码")
    private String gradeId;

    @Schema(description = "班级编码")
    private String clazzId;

    @Schema(description = "作文类别")
    private Integer compositionType;

    @Schema(description = "作文文体")
    private String compositionWenTi;

    @Schema(description = "作业内容要求")
    private String compositionContent;

    @Schema(description = "字数要求")
    private Integer compositionWordCount;

    @Schema(description = "作文标题")
    private String compositionTitle;

    @Schema(description = "参考内容路径")
    private String compositionExamplePath;

    @Schema(description = "作业资源路径")
    private String compositionResourcePath;

    @Schema(description = "作文开始时间")
    private LocalDateTime compositionBeginTime;

    @Schema(description = "作文截至时间")
    private LocalDateTime compositionEndTime;

    @Schema(description = "发布状态:0 草稿 1 发文")
    private Integer compositionStatus;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "班级名称")
    private String clazzName;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "作文满分")
    private Integer compositionFullScore;

    @Schema(description = "应答状态:应答状态:0评测 5老师批量评测 10已提交 15已批阅")
    private Integer answerStatus;

    @Schema(description = "更新人")
    private String updateUser;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "作文拓展表信息")
    private List<BizCompositionReleaseExtend> extendList;

    @Schema(description = "单元Id")
    private Long did;

    @Schema(description = "单元名称")
    private String ddanyuan;

    @Schema(description = "是否为模版")
    private String isTemplate;

}