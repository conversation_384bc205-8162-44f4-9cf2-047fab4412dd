/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.izpan.infrastructure.domain.BusinessEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 作文记录表 Entity 实体类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.entity.BizCompositionLog
 * @CreateTime 2025-04-11 - 15:29:42
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("biz_composition_log")
public class BizCompositionLog extends BusinessEntity {

    /**
     * 作业ID
     */
    private Long compositionId;

    /**
     * 初始测评ID
     */
    private Long initLogId;

    /**
     * 标题
     */
    private String title;

    /**
     * 作文要求题目
     */
    private String requireTitle;

    /**
     * 作文文体
     */
    private String wenTi;

    /**
     * 作文要求文体
     */
    private String requireWenTi;

    /**
     * 作文字数
     */
    private Integer words;

    /**
     * 星标
     */
    private Integer star;

    /**
     * 作文要求字数
     */
    private Integer requireWords;

    /**
     * 作者
     */
    private String author;

    /**
     * 内容
     */
    private String content;

    /**
     * 年级
     */
    private String grade;

    /**
     * 分数
     */
    private Double score;

    /**
     * 百分比分数
     */
    private Double percentage;

    /**
     * 最高分数
     */
    private Double maxScore;

    /**
     * 作文满分分数
     */
    private Double fullScore;

    /**
     * 累计测评次数
     */
    private Integer evaluationCount;

    /**
     * 作文归类
     */
    private String classified;

    /**
     * 段落数
     */
    private Integer paragraphs;

    /**
     * 句子数
     */
    private Integer sentences;

    /**
     * 作文路径
     */
    private String compositionPath;

    /**
     * 测文评价内容
     */
    private String evaluateContent;

    /**
     * 来源
     */
    private String source;

    /**
     * 测文建议
     */
    private String advice;

    /**
     * 测文新评语
     */
    private String comments;

    /**
     * 敏感词：是否有敏感词，-1有，0无
     */
    @TableField(value = "`sensitive`")
    private String sensitive;

    /**
     * 应答状态:应答状态:0评测 5老师批量评测 10已提交 15已批阅
     */
    private Integer answerStatus;

    /**
     * 测文来源：0自主，1作业，2测验
     */
    private Integer evaluateSources;

    /**
     * 老师评价
     */
    private String teacherEvaluate;

    /**
     * 应答者ID
     */
    private Long answerId;

    /**
     * 应答者学号
     */
    private String answerStudentNum;

    /**
     * 应答者名称
     */
    private String answerName;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 批阅时间
     */
    private LocalDateTime reviewTime;

    /**
     * 映射id
     */
    private Long mappingId;

}