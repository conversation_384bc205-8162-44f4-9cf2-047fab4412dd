/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.modules.biz.domain.bo.BizCompositionLogBO;
import com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogExportSearchDTO;
import com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogSearchDTO;
import com.izpan.modules.biz.domain.entity.BizCompositionLog;
import com.izpan.modules.biz.domain.vo.BizCompBatchEvaluateVO;
import com.izpan.modules.biz.domain.vo.BizCompositionHistoryLogVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogExportVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;

import java.util.List;
import java.util.Map;

/**
 * 作文记录表 Service 服务接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.IBizCompositionLogService
 * @CreateTime 2025-03-23 - 12:45:30
 */

public interface IBizCompositionLogService extends IService<BizCompositionLog> {

    /**
     * 作文记录表 - 分页查询
     *
     * @param pageQuery           分页对象
     * @param bizCompositionLogBO BO 查询对象
     * @return {@link IPage} 分页结果
     * <AUTHOR>
     * @CreateTime 2025-03-23 - 12:45:30
     */
    IPage<BizCompositionLogVO> listBizCompositionLogPage(PageQuery pageQuery, BizCompositionLogBO bizCompositionLogBO);


    /**
     * 获取登录者的作文记录表列表
     *
     * @param pageQuery           分页对象
     * @param bizCompositionLogSearchDTO BO 查询对象
     * @return {@link IPage} 分页结果
     * <AUTHOR>
     * @CreateTime 2025-04-22 - 12:45:30
     */
    IPage<BizCompositionHistoryLogVO> userPage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 批量测评记录 - 分页查询
     *
     * @param pageQuery           分页对象
     * @param bizCompositionLogSearchDTO BO 查询对象
     * @return {@link IPage} 分页结果
     * <AUTHOR>
     * @CreateTime 2025-04-14 - 12:45:30
     */
    IPage<BizCompBatchEvaluateVO> listBatchEvaluatePage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO);

    /**
     * 根据ID查询作文记录详情并关联作文主表
     *
     * @param id 作文记录ID
     * @return 作文记录详情VO对象，包含作文主表信息
     * <AUTHOR>
     * @CreateTime 2025-03-24
     */
    BizCompositionLogVO getDetailById(Long id);


    Integer getScoreRanking(Long id, String grade);
    /**
     * 根据条件查询作文记录列表
     *
     * @param queryWrapper 查询条件
     * @return 作文记录列表
     */
    List<BizCompositionLog> listWithMaxStatus(QueryWrapper<BizCompositionLog> queryWrapper);

    /**
     * 按照answer_id分组获取每组中状态最高的记录
     *
     * @param compositionId 作文ID
     * @param clazzId       班级编码
     * @param minStatus     最小状态值
     * @return 筛选后的记录列表
     */
    List<BizCompositionLog> listWithMaxStatus(Long compositionId, String clazzId, Integer minStatus);

    /**
     * 统计班级学生数量和提交作文人数
     *
     * @param clazzId       班级编码
     * @param compositionId 作文ID (可选)
     * @return Map 包含班级总人数和提交作文的人数
     */
    Map<String, Object> countStudentsAndSubmissions(String clazzId, Long compositionId);

    /**
     * 检查用户是否提交过特定作文
     *
     * @param compositionId 作文ID
     * @param userId 用户ID
     * @return 是否提交过
     */
    boolean checkUserSubmission(Long compositionId, Long userId);

    /**
     * 作文记录表 - 导出查询（获取首次和最终分数）
     *
     * @param bizCompositionLogSearchDTO 查询条件
     * @return {@link List<BizCompositionLogExportVO>} 包含初始分数和最终分数的作文记录导出VO列表
     * <AUTHOR>
     * @CreateTime 2025-04-16
     */
    List<BizCompositionLogExportVO> listForExport(BizCompositionLogExportSearchDTO bizCompositionLogSearchDTO);

}
