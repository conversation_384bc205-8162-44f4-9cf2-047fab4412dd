/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.izpan.modules.base.domain.dto.EvaluationCountDTO;
import com.izpan.modules.base.domain.dto.EvaluationTimeDTO;
import com.izpan.modules.base.domain.dto.ScoreDTO;
import com.izpan.modules.biz.domain.entity.BizCompositionLog;
import com.izpan.modules.biz.domain.vo.BizCompBatchEvaluateVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 作文记录表 Mapper 接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.repository.mapper.BizCompositionLogMapper
 * @CreateTime 2025-03-23 - 12:45:30
 */

public interface BizCompositionLogMapper extends MPJBaseMapper<BizCompositionLog> {
    /**
     * 连表分页查询作文记录和作文主表（用于普通分页查询）
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果，包含作文记录和作文主表信息
     */
    IPage<BizCompositionLogVO> selectBizCompositionLogPage(Page<BizCompositionLogVO> page, @Param("ew") Object queryWrapper);
    

    /**
     * 连表分页查询作文记录和作文主表（用于批量测评记录分页查询）
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果，包含作文记录和作文主表信息
     */
    IPage<BizCompBatchEvaluateVO> selectBatchEvaluatePage(Page<BizCompBatchEvaluateVO> page, @Param("ew") Object queryWrapper);

    /**
     * 根据ID查询作文记录详情并关联作文主表
     *
     * @param id 作文记录ID
     * @return 作文记录详情VO对象，包含作文主表信息
     */
    BizCompositionLogVO getDetailById(@Param("id") Long id);

    Integer getScoreRanking(@Param("id") Long id,@Param("grade") String grade);

    /**
     * 执行自定义SQL查询
     *
     * @param sql 自定义SQL语句
     * @return 查询结果列表
     */
    List<BizCompositionLog> selectByCustomSql(@Param("sql") String sql);

    /**
     * 按照answer_id分组获取每组中状态最高的记录
     *
     * @param compositionId 作文ID
     * @param clazzId 班级编码
     * @param minStatus 最小状态值
     * @return 筛选后的记录列表
     */
    List<BizCompositionLog> listWithMaxStatus(@Param("compositionId") Long compositionId, 
                                             @Param("clazzId") String clazzId,
                                             @Param("minStatus") Integer minStatus);

    /**
     * 统计班级学生数量和提交作文人数
     *
     * @param clazzId 班级编码
     * @param compositionId 作文ID (可选)
     * @return Map 包含 total_students(班级总人数) 和 submitted_students(提交作文的人数)
     */
    Map<String, Object> countStudentsAndSubmissions(@Param("clazzId") String clazzId,
                                                   @Param("compositionId") Long compositionId);

    @MapKey("id")
    Map<Long, ScoreDTO> getLatestScoreByInitIds(@Param("list") List<Long> initLogIds);

    @MapKey("id")
    Map<Long, EvaluationCountDTO> getLatestEvaluationCountByInitIds(@Param("list") List<Long> initLogIds);

    @MapKey("id")
    Map<Long, EvaluationTimeDTO> getLatestEvaluationTimeByInitIds(@Param("list") List<Long> initLogIds);

    @Select("SELECT t1.id FROM biz_composition_log t1 " +
            "WHERE t1.is_deleted = 0 " +
            "AND t1.answer_status >= 5 " +
            "AND (#{compositionId} IS NULL OR t1.composition_id = #{compositionId}) " +
            "AND (#{clazzId} IS NULL OR t1.clazz_id = #{clazzId}) " +
            "AND t1.create_time = (" +
            "    SELECT MAX(t2.create_time) " +
            "    FROM biz_composition_log t2 " +
            "    WHERE t2.is_deleted = 0 " +
            "    AND t2.answer_id = t1.answer_id " +
            "    AND t2.answer_status >= 5" +
            ") " +
            "ORDER BY t1.create_time DESC")
    List<Long> selectLatestIdsByAnswerIdWithTime(@Param("compositionId") Long compositionId,
                                                 @Param("clazzId") String clazzId);


}