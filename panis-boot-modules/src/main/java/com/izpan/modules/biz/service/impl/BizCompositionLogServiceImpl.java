/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.modules.base.domain.dto.EvaluationCountDTO;
import com.izpan.modules.base.domain.dto.EvaluationTimeDTO;
import com.izpan.modules.base.domain.dto.ScoreDTO;
import com.izpan.modules.biz.domain.bo.BizCompositionLogBO;
import com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogExportSearchDTO;
import com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogSearchDTO;
import com.izpan.modules.biz.domain.entity.BizCompositionLog;
import com.izpan.modules.biz.domain.entity.BizCompositionRelease;
import com.izpan.modules.biz.domain.vo.BizCompBatchEvaluateVO;
import com.izpan.modules.biz.domain.vo.BizCompositionHistoryLogVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogExportVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;
import com.izpan.modules.biz.repository.mapper.BizCompositionLogMapper;
import com.izpan.modules.biz.repository.mapper.BizCompositionReleaseMapper;
import com.izpan.modules.biz.service.IBizCompositionLogService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 作文记录表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionLogServiceImpl
 * @CreateTime 2025-03-23 - 12:45:30
 */

@Service
public class BizCompositionLogServiceImpl extends MPJBaseServiceImpl<BizCompositionLogMapper, BizCompositionLog> implements IBizCompositionLogService {

    @Resource
    private BizCompositionReleaseMapper bizCompositionReleaseMapper;

    @Override
    public IPage<BizCompositionLogVO> listBizCompositionLogPage(PageQuery pageQuery, BizCompositionLogBO bizCompositionLogBO) {
        LambdaQueryWrapper<BizCompositionLog> queryWrapper = new LambdaQueryWrapper<BizCompositionLog>()
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getCompositionId()), BizCompositionLog::getCompositionId, bizCompositionLogBO.getCompositionId())
                .like(ObjectUtils.isNotEmpty(bizCompositionLogBO.getTitle()), BizCompositionLog::getTitle, bizCompositionLogBO.getTitle())
                .like(ObjectUtils.isNotEmpty(bizCompositionLogBO.getContent()), BizCompositionLog::getContent, bizCompositionLogBO.getContent())
                .like(ObjectUtils.isNotEmpty(bizCompositionLogBO.getAuthor()), BizCompositionLog::getAuthor, bizCompositionLogBO.getAuthor())
                .like(ObjectUtils.isNotEmpty(bizCompositionLogBO.getGrade()), BizCompositionLog::getGrade, bizCompositionLogBO.getGrade())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getAnswerStatus()), BizCompositionLog::getAnswerStatus, bizCompositionLogBO.getAnswerStatus())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getAnswerId()), BizCompositionLog::getAnswerId, bizCompositionLogBO.getAnswerId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getAnswerName()), BizCompositionLog::getAnswerName, bizCompositionLogBO.getAnswerName())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getSchoolId()), BizCompositionLog::getSchoolId, bizCompositionLogBO.getSchoolId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getSchoolName()), BizCompositionLog::getSchoolName, bizCompositionLogBO.getSchoolName())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getGradeId()), BizCompositionLog::getGradeId, bizCompositionLogBO.getGradeId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getGradeName()), BizCompositionLog::getGradeName, bizCompositionLogBO.getGradeName())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getClazzId()), BizCompositionLog::getClazzId, bizCompositionLogBO.getClazzId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogBO.getClazzName()), BizCompositionLog::getClazzName, bizCompositionLogBO.getClazzName())
                .eq(BizCompositionLog::getDeleted, 0)
                .orderByDesc(BizCompositionLog::getCreateTime);

        // 使用关联查询
        Page<BizCompositionLogVO> page = new Page<>(pageQuery.getPage(), pageQuery.getPageSize());
        return baseMapper.selectBizCompositionLogPage(page, queryWrapper);
    }

    /**
     * 获取用户的作文历史记录，包含初始分数和最新分数
     * 查询逻辑：
     * 1. 查询所有初始记录（t.id = t.init_log_id）
     * 2. 使用子查询获取每个init_log_id对应的最后一条记录（最新修改）
     */
    @Override
    public IPage<BizCompositionHistoryLogVO> userPage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        // Step 1: 查询初始记录（主表分页）
        Page<BizCompositionHistoryLogVO> bizLogPage = new Page<>(pageQuery.getPage(), pageQuery.getPageSize());

        MPJLambdaQueryWrapper<BizCompositionLog> wrapper = new MPJLambdaQueryWrapper<BizCompositionLog>()
                .selectAll(BizCompositionLog.class)
                .apply("t.id = t.init_log_id")
                .eq(BizCompositionLog::getAnswerId, bizCompositionLogSearchDTO.getAnswerId())
                .like(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getTitle()), BizCompositionLog::getTitle, bizCompositionLogSearchDTO.getTitle())
                .orderByDesc(BizCompositionLog::getCreateTime);

        bizLogPage = baseMapper.selectJoinPage(bizLogPage, BizCompositionHistoryLogVO.class, wrapper);

        List<BizCompositionHistoryLogVO> records = bizLogPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return bizLogPage;
        }

        // Step 2: 提取ID列表
        List<Long> initLogIds = records.stream().map(BizCompositionHistoryLogVO::getId).collect(Collectors.toList());
        List<Long> compositionIds = records.stream().map(BizCompositionHistoryLogVO::getCompositionId).collect(Collectors.toList());

        // Step 3: 并发查询题目、最新分数、评价次数
//        CompletableFuture<Map<Long, String>> titleFuture = CompletableFuture.supplyAsync(() -> {
//            return bizCompositionReleaseMapper.selectBatchIds(compositionIds).stream()
//                    .collect(Collectors.toMap(BizCompositionRelease::getId, BizCompositionRelease::getCompositionTitle));
//        });

        CompletableFuture<Map<Long, ScoreDTO>> scoreFuture = CompletableFuture.supplyAsync(() -> {
            return baseMapper.getLatestScoreByInitIds(initLogIds); // 自定义SQL：按init_log_id分组取submit_time最新一条
        });

        CompletableFuture<Map<Long, EvaluationCountDTO>> evaluationCountFuture = CompletableFuture.supplyAsync(() -> {
            return baseMapper.getLatestEvaluationCountByInitIds(initLogIds); // 自定义SQL
        });

        CompletableFuture<Map<Long, EvaluationTimeDTO>> evaluationTimeFuture = CompletableFuture.supplyAsync(() -> {
            return baseMapper.getLatestEvaluationTimeByInitIds(initLogIds); // 自定义SQL
        });


        CompletableFuture.allOf(scoreFuture, evaluationCountFuture, evaluationTimeFuture).join();

//        Map<Long, String> titleMap = titleFuture.join();
        Map<Long, ScoreDTO> scoreMap = scoreFuture.join();
        Map<Long, EvaluationCountDTO> evaluationCountMap = evaluationCountFuture.join();
        Map<Long, EvaluationTimeDTO> evaluationTimeMap = evaluationTimeFuture.join();

        // Step 4: 封装VO列表
        records.forEach(log -> {
//            log.setTitle(titleMap.get(log.getCompositionId()));
            log.setLastScore(scoreMap.get(log.getId()).getScore());
            log.setLastEvaluationCount(evaluationCountMap.get(log.getId()).getEvaluationCount());
            log.setSubmitTime(evaluationTimeMap.get(log.getId()).getSubmitTime());
        });

        return bizLogPage;
    }


    @Override
    public IPage<BizCompBatchEvaluateVO> listBatchEvaluatePage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        // 使用MPJLambdaWrapper构建查询
        MPJLambdaWrapper<BizCompositionLog> wrapper = new MPJLambdaWrapper<BizCompositionLog>()
                .selectAll(BizCompositionLog.class)
                .select(BizCompositionRelease::getCompositionTitle)
                .selectAs("CASE WHEN t.answer_id IS NULL THEN 0 ELSE 1 END", BizCompBatchEvaluateVO::getMatchStatus)
                .leftJoin(BizCompositionRelease.class, BizCompositionRelease::getId, BizCompositionLog::getCompositionId)
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getCompositionId()),
                        BizCompositionLog::getCompositionId, bizCompositionLogSearchDTO.getCompositionId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getSchoolId()),
                        BizCompositionLog::getSchoolId, bizCompositionLogSearchDTO.getSchoolId())
                .eq(BizCompositionLog::getSource, "1");

        if (bizCompositionLogSearchDTO.getMatchStatus() != null) {
            if (bizCompositionLogSearchDTO.getMatchStatus() == 0) {
                wrapper.isNull(BizCompositionLog::getAnswerId);
            } else if (bizCompositionLogSearchDTO.getMatchStatus() == 1) {
                wrapper.isNotNull(BizCompositionLog::getAnswerId);
            }
        }

        wrapper.orderByDesc(BizCompositionLog::getCreateTime);

        Page<BizCompBatchEvaluateVO> page = new Page<>(pageQuery.getPage(), pageQuery.getPageSize());

        return baseMapper.selectJoinPage(page, BizCompBatchEvaluateVO.class, wrapper);
    }

    @Override
    public BizCompositionLogVO getDetailById(Long id) {
        BizCompositionLog bizCompositionLog = Optional.ofNullable(baseMapper.selectById(id)).orElseThrow(() -> new RuntimeException("作文记录不存在"));
        return Convert.convert(BizCompositionLogVO.class, bizCompositionLog);
    }

    @Override
    public Integer getScoreRanking(Long id, String grade) {
        return baseMapper.getScoreRanking(id, grade);
    }

    /**
     * 获取作文日志列表，按照answer_id分组，并获取每组中status最大的记录
     *
     * @param queryWrapper 查询条件
     * @return 筛选后的记录列表
     */
    @Override
    public List<BizCompositionLog> listWithMaxStatus(QueryWrapper<BizCompositionLog> queryWrapper) {
        // 从QueryWrapper中提取条件
        Long compositionId = null;
        String clazzId = null;
        Integer minStatus = 5; // 默认最小状态为5

        // 检查并提取作文ID条件
        if (queryWrapper.getParamNameValuePairs().containsKey("composition_id")) {
            compositionId = (Long) queryWrapper.getParamNameValuePairs().get("composition_id");
        }

        // 检查并提取班级编码条件
        if (queryWrapper.getParamNameValuePairs().containsKey("clazz_id")) {
            clazzId = (String) queryWrapper.getParamNameValuePairs().get("clazz_id");
        }

        // 使用新的mapper方法
        return this.listWithMaxStatus(compositionId, clazzId, minStatus);
    }

    /**
     * 获取作文日志列表，按照answer_id分组，并获取每组中status最大的记录
     *
     * @param compositionId 作文ID
     * @param clazzId       班级编码
     * @param minStatus     最小状态值
     * @return 筛选后的记录列表
     */
    @Override
    public List<BizCompositionLog> listWithMaxStatus(Long compositionId, String clazzId, Integer minStatus) {
        // 直接调用mapper方法，传递参数
        return baseMapper.listWithMaxStatus(compositionId, clazzId, minStatus);
    }

    /**
     * 统计班级学生数量和提交作文人数
     *
     * @param clazzId       班级编码
     * @param compositionId 作文ID (可选)
     * @return Map 包含班级总人数和提交作文的人数
     */
    @Override
    public Map<String, Object> countStudentsAndSubmissions(String clazzId, Long compositionId) {
        return baseMapper.countStudentsAndSubmissions(clazzId, compositionId);
    }

    @Override
    public boolean checkUserSubmission(Long compositionId, Long userId) {
        Long count = this.count(new LambdaQueryWrapper<BizCompositionLog>()
                .eq(BizCompositionLog::getCompositionId, compositionId)
                .eq(BizCompositionLog::getAnswerId, userId)
                .ge(BizCompositionLog::getAnswerStatus, 5)); // 状态大于等于5表示已提交或已批阅
        return count > 0;
    }

    /**
     * 导出功能获取作文记录表列表（包含初始分数和最终分数）
     */
    @Override
    public List<BizCompositionLogExportVO> listForExport(BizCompositionLogExportSearchDTO bizCompositionLogSearchDTO) {
        List<Long> latestIds = baseMapper.selectLatestIdsByAnswerIdWithTime(
                bizCompositionLogSearchDTO.getCompositionId(),
                bizCompositionLogSearchDTO.getClazzId()
        );

        if (ObjectUtils.isNotEmpty(latestIds)) {
            MPJLambdaWrapper<BizCompositionLog> wrapper = new MPJLambdaWrapper<BizCompositionLog>()
                    .selectAll(BizCompositionLog.class)
                    .selectAs("i.score", BizCompositionLogExportVO::getInitScore)
                    .leftJoin(BizCompositionLog.class, "i", i -> i.eq("i.id", "t.init_log_id"))
                    .in(BizCompositionLog::getId, latestIds)
                    .orderByDesc(BizCompositionLog::getScore);

            List<BizCompositionLogExportVO> resultList = this.selectJoinList(BizCompositionLogExportVO.class, wrapper);

            // 设置序号并计算分数差值
            for (int i = 0; i < resultList.size(); i++) {
                BizCompositionLogExportVO vo = resultList.get(i);
                // 设置序号
                vo.setSequence(i + 1);

                // 计算分数差值
                if (vo.getInitScore() != null && vo.getScore() != null) {
                    vo.setImproveScore(vo.getScore() - vo.getInitScore());
                } else {
                    vo.setImproveScore(0.0);
                    // 如果初始分数为空，则使用当前分数作为初始分数
                    if (vo.getInitScore() == null) {
                        vo.setInitScore(vo.getScore());
                    }
                }
            }

            return resultList;
        }

        return null;

        // 创建查询构造器     初始分第一次提交的分数
//        MPJLambdaWrapper<BizCompositionLog> wrapper = new MPJLambdaWrapper<BizCompositionLog>()
//                .selectAll(BizCompositionLog.class)
//                .selectAs("i.score", BizCompositionLogExportVO::getInitScore) // 选择初始分数
//                .leftJoin(BizCompositionLog.class, "i", i -> i.eq("i.id", "t.init_log_id"))
//                .ge(BizCompositionLog::getAnswerStatus, 5) // 状态大于等于5表示已提交
//                .eq(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getCompositionId()),
//                        BizCompositionLog::getCompositionId, bizCompositionLogSearchDTO.getCompositionId())
//                .eq(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getClazzId()),
//                        BizCompositionLog::getClazzId, bizCompositionLogSearchDTO.getClazzId())
//                .like(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getTitle()),
//                        BizCompositionLog::getTitle, bizCompositionLogSearchDTO.getTitle())
//                .like(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getAnswerName()),
//                        BizCompositionLog::getAnswerName, bizCompositionLogSearchDTO.getAnswerName())
//                .eq(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getSchoolId()),
//                        BizCompositionLog::getSchoolId, bizCompositionLogSearchDTO.getSchoolId())
//                .eq(ObjectUtils.isNotEmpty(bizCompositionLogSearchDTO.getGradeId()),
//                        BizCompositionLog::getGradeId, bizCompositionLogSearchDTO.getGradeId())
//                .isNotNull(BizCompositionLog::getInitLogId) // 确保有关联的初始记录
//                .orderByDesc(BizCompositionLog::getCreateTime);

        // 执行查询获取结果

    }
}

