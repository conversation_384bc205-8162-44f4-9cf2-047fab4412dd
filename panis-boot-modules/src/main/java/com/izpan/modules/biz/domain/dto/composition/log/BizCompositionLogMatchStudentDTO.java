/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.dto.composition.log;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.izpan.modules.biz.domain.entity.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 作文记录表 新增 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogAddDTO
 * @CreateTime 2025-04-08 - 22:52:48
 */

@Getter
@Setter
@Schema(name = "BizCompositionLogMatchStudentDTO", description = "作文匹配 DTO 对象")
public class BizCompositionLogMatchStudentDTO implements Serializable {

    @Schema(description = "作文记录ID")
    private Long id;

    @Schema(description = "应答者ID")
    private Long answerId;

    @Schema(description = "应答者学号")
    private String answerStudentNum;

    @Schema(description = "应答者名称")
    private String answerName;

    @Schema(description = "作文标题")
    private String title;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "年级ID")
    private String gradeId;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "班级ID")
    private String clazzId;

    @Schema(description = "班级名称")
    private String clazzName;

    @Schema(description = "年级")
    private String grade;

}