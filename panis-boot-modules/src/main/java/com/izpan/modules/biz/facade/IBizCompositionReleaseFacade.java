/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.facade;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.biz.domain.dto.composition.release.*;
import com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO;
import com.izpan.modules.system.domain.vo.SysUserVO;

import java.util.List;

/**
 * 作文发布表 门面接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.facade.IBizCompositionReleaseFacade
 * @CreateTime 2024-12-18 - 12:57:47
 */

public interface IBizCompositionReleaseFacade {

    /**
     * 作文发布表 - 分页查询
     *
     * @param pageQuery                      分页对象
     * @param bizCompositionReleaseSearchDTO 查询对象
     * @return {@link RPage} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    RPage<BizCompositionReleaseVO> listBizCompositionReleasePage(PageQuery pageQuery, BizCompositionReleaseSearchDTO bizCompositionReleaseSearchDTO);

    /**
     * 根据 ID 获取详情信息
     *
     * @param id 作文发布表ID
     * @return {@link BizCompositionReleaseVO} 作文发布表 VO 对象
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    BizCompositionReleaseVO get(Long id);

    /**
     * 新增作文发布表
     *
     * @param bizCompositionReleaseAddDTO 新增作文发布表 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    boolean add(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO);

    /**
     * 编辑更新作文发布表信息
     *
     * @param bizCompositionReleaseUpdateDTO 编辑更新 DTO 对象
     * @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    boolean update(BizCompositionReleaseUpdateDTO bizCompositionReleaseUpdateDTO);

    /**
     * 批量删除作文发布表信息
     *
     * @param bizCompositionReleaseDeleteDTO 删除 DTO 对象
     * @return @return {@link Boolean} 结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    boolean batchDelete(BizCompositionReleaseDeleteDTO bizCompositionReleaseDeleteDTO);

    boolean compositionRelease(CompositionReleaseDTO compositionReleaseDTO);

    boolean compositionCreateAndRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO);

    boolean compositionCreate(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO);

    SysUserVO studentAnswerComposition(Long compositionId, String studentNum, Boolean isCheck);

    List<BizCompositionReleaseVO> queryCompositionList(CompositionStudentQueryDTO compositionStudentQueryDTO);

    /**
     * 老师查询作文列表
     *
     * @param bizCompositionReleaseSearchDTO 查询对象
     * @return {@link List} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    List<BizCompositionReleaseVO> list(BizCompositionReleaseSearchDTO bizCompositionReleaseSearchDTO);
}