/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izpan.modules.biz.domain.dto.composition.release.CompositionStudentQueryDTO;
import com.izpan.modules.biz.domain.entity.BizCompositionRelease;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;
import com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 作文发布表 Mapper 接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.repository.mapper.BizCompositionReleaseMapper
 * @CreateTime 2024-12-18 - 12:57:47
 */

public interface BizCompositionReleaseMapper extends BaseMapper<BizCompositionRelease> {


    List<BizCompositionReleaseVO> queryCompositionList(@Param("compositionStudentQueryDTO") CompositionStudentQueryDTO compositionStudentQueryDTO);
    
    /**
     * 根据ID查询作文发布详情并关联作文扩展表
     *
     * @param id 作文发布ID
     * @return 作文发布详情VO对象，包含作文扩展表信息
     */
    BizCompositionReleaseVO getDetailWithExtendById(@Param("id") Long id);
    
    /**
     * 查询作文发布列表并关联作文扩展表
     *
     * @param queryWrapper 查询条件包装器
     * @return 作文发布列表VO对象，每个对象包含作文扩展表信息
     */
    IPage<BizCompositionReleaseVO> listWithExtend(Page<BizCompositionReleaseVO> page, @Param("ew") Object queryWrapper);
}