package com.izpan.modules.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.SimpleEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("biz_composition_release_extend")
public class BizCompositionReleaseExtend extends SimpleEntity {
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 学校ID
     */
    private String schoolId;

    /**
     * 年级编码
     */
    private String gradeId;
    /**
     * 年级名称
     */
    private String gradeName;
    /**
     * 班级编码
     */
    private String clazzId;
    /**
     * 班级名称
     */
    private String clazzName;
    /**
     * 作业ID
     */
    private Long compositionId;

}
