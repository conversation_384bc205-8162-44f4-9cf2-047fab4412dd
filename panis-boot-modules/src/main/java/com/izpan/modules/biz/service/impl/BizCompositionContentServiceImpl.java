/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import com.izpan.modules.biz.domain.bo.BizCompositionContentBO;
import com.izpan.modules.biz.domain.entity.BizCompositionContent;
import com.izpan.modules.biz.repository.mapper.BizCompositionContentMapper;
import com.izpan.modules.biz.service.IBizCompositionContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 作文内容表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionContentServiceImpl
 * @CreateTime 2025-04-08 - 22:45:07
 */

@Service
public class BizCompositionContentServiceImpl extends ServiceImpl<BizCompositionContentMapper, BizCompositionContent> implements IBizCompositionContentService {

    @Override
    public IPage<BizCompositionContent> listBizCompositionContentPage(PageQuery pageQuery, BizCompositionContentBO bizCompositionContentBO) {
        LambdaQueryWrapper<BizCompositionContent> queryWrapper = new LambdaQueryWrapper<BizCompositionContent>()
            .eq(ObjectUtils.isNotEmpty(bizCompositionContentBO.getClId()), BizCompositionContent::getClId, bizCompositionContentBO.getClId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionContentBO.getUserId()), BizCompositionContent::getUserId, bizCompositionContentBO.getUserId());
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

