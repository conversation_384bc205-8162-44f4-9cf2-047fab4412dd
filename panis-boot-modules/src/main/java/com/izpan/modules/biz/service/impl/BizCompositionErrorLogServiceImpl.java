/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import com.izpan.modules.biz.domain.bo.BizCompositionErrorLogBO;
import com.izpan.modules.biz.domain.entity.BizCompositionErrorLog;
import com.izpan.modules.biz.repository.mapper.BizCompositionErrorLogMapper;
import com.izpan.modules.biz.service.IBizCompositionErrorLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 作文错误表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionErrorLogServiceImpl
 * @CreateTime 2025-04-09 - 11:48:51
 */

@Service
public class BizCompositionErrorLogServiceImpl extends ServiceImpl<BizCompositionErrorLogMapper, BizCompositionErrorLog> implements IBizCompositionErrorLogService {

    @Override
    public IPage<BizCompositionErrorLog> listBizCompositionErrorLogPage(PageQuery pageQuery, BizCompositionErrorLogBO bizCompositionErrorLogBO) {
        LambdaQueryWrapper<BizCompositionErrorLog> queryWrapper = new LambdaQueryWrapper<BizCompositionErrorLog>()
            .eq(ObjectUtils.isNotEmpty(bizCompositionErrorLogBO.getClId()), BizCompositionErrorLog::getClId, bizCompositionErrorLogBO.getClId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionErrorLogBO.getEtype()), BizCompositionErrorLog::getEtype, bizCompositionErrorLogBO.getEtype())
            .eq(ObjectUtils.isNotEmpty(bizCompositionErrorLogBO.getUserId()), BizCompositionErrorLog::getUserId, bizCompositionErrorLogBO.getUserId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionErrorLogBO.getSchoolId()), BizCompositionErrorLog::getSchoolId, bizCompositionErrorLogBO.getSchoolId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionErrorLogBO.getGradeId()), BizCompositionErrorLog::getGradeId, bizCompositionErrorLogBO.getGradeId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionErrorLogBO.getClazzId()), BizCompositionErrorLog::getClazzId, bizCompositionErrorLogBO.getClazzId()).orderByDesc(BizCompositionErrorLog::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

