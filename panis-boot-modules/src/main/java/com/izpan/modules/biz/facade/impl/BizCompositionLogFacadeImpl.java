/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.exception.BizException;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.domain.BizEntity;
import com.izpan.infrastructure.enums.CompositionSourceEnum;
import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.infrastructure.util.SchoolUtil;
import com.izpan.modules.biz.domain.bo.BizCompositionLogBO;
import com.izpan.modules.biz.domain.dto.composition.log.*;
import com.izpan.modules.biz.domain.entity.*;
import com.izpan.modules.biz.domain.vo.BizCompBatchEvaluateVO;
import com.izpan.modules.biz.domain.vo.BizCompositionHistoryLogVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogExportVO;
import com.izpan.modules.biz.domain.vo.BizCompositionLogVO;
import com.izpan.modules.biz.facade.IBizCompositionLogFacade;
import com.izpan.modules.biz.service.*;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.BiConsumer;

/**
 * 作文记录表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.facade.impl.BizCompositionLogFacadeImpl
 * @CreateTime 2025-03-23 - 12:45:30
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class BizCompositionLogFacadeImpl implements IBizCompositionLogFacade {

    @NonNull
    private IBizCompositionLogService bizCompositionLogService;

    @NonNull
    private IBizCompositionContentService bizCompositionContentService;

    @NonNull
    private IBizCompositionGoodWordsService bizCompositionGoodWordsService;

    @NonNull
    private IBizCompositionErrorLogService bizCompositionErrorLogService;

    @NonNull
    private IBizCompositionJiService bizCompositionJiService;

    @NonNull
    private IBizCompositionMeipiService bizCompositionMeipiService;

    @NonNull
    private IBizCompositionCtService bizCompositionCtService;

    @NonNull
    private IBizCompositionReleaseService bizCompositionReleaseService;

    @Resource(name = "loadExecutor")
    private ExecutorService executor;

    @Override
    public RPage<BizCompositionLogVO> listBizCompositionLogPage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogSearchDTO, BizCompositionLogBO::new);
        LoginUser loginUser = GlobalUserHolder.getUser();
        bizCompositionLogBO.setAnswerId(loginUser.getId());
        IPage<BizCompositionLogVO> bizCompositionLogIPage = bizCompositionLogService.listBizCompositionLogPage(pageQuery, bizCompositionLogBO);
        return RPage.build(bizCompositionLogIPage, BizCompositionLogVO::new);
    }

    @Override
    public RPage<BizCompositionHistoryLogVO> userPage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        bizCompositionLogSearchDTO.setAnswerId(GlobalUserHolder.getUser().getId());
        IPage<BizCompositionHistoryLogVO> bizCompositionLogIPage = bizCompositionLogService.userPage(pageQuery, bizCompositionLogSearchDTO);
        return RPage.build(bizCompositionLogIPage, BizCompositionHistoryLogVO::new);
    }

    @Override
    public RPage<BizCompBatchEvaluateVO> listBatchEvaluatePage(PageQuery pageQuery, BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        SchoolUtil.setSchoolId(bizCompositionLogSearchDTO);
        IPage<BizCompBatchEvaluateVO> bizCompositionLogIPage = bizCompositionLogService.listBatchEvaluatePage(pageQuery, bizCompositionLogSearchDTO);
        return RPage.build(bizCompositionLogIPage, BizCompBatchEvaluateVO::new);
    }

    @Override
    public BizCompositionLogVO get(Long id) {
        BizCompositionLogVO bizCompositionLogVO = bizCompositionLogService.getDetailById(id);

        // 异步任务列表，后续用于 allOf
        List<CompletableFuture<Void>> futureList = new ArrayList<>();

        // rankFuture 始终执行
        CompletableFuture<Void> rankFuture = CompletableFuture.supplyAsync(() ->
                        bizCompositionLogService.getScoreRanking(bizCompositionLogVO.getId(), bizCompositionLogVO.getGrade()))
                .thenAccept(bizCompositionLogVO::setScoreRanking);
        futureList.add(rankFuture);

        // releaseFuture 仅当 compositionId 非空时才执行
        if (ObjectUtil.isNotEmpty(bizCompositionLogVO.getCompositionId())) {
            CompletableFuture<Void> releaseFuture = CompletableFuture.supplyAsync(() ->
                            bizCompositionReleaseService.getOne(new LambdaQueryWrapper<BizCompositionRelease>()
                                    .eq(BizCompositionRelease::getId, bizCompositionLogVO.getCompositionId())))
                    .thenAccept(bizCompositionRelease -> {
                        bizCompositionLogVO.setCompositionTitle(bizCompositionRelease.getCompositionTitle());
                        bizCompositionLogVO.setCompositionContent(bizCompositionRelease.getCompositionContent());
                        bizCompositionLogVO.setCompositionWordCount(bizCompositionRelease.getCompositionWordCount());
                        bizCompositionLogVO.setCompositionWenTi(bizCompositionRelease.getCompositionWenTi());
                        bizCompositionLogVO.setCompositionExamplePath(bizCompositionRelease.getCompositionExamplePath());
                        bizCompositionLogVO.setCompositionResourcePath(bizCompositionRelease.getCompositionResourcePath());
                        bizCompositionLogVO.setCompositionBeginTime(bizCompositionRelease.getCompositionBeginTime());
                        bizCompositionLogVO.setCompositionEndTime(bizCompositionRelease.getCompositionEndTime());
                        bizCompositionLogVO.setCompositionBeginTime(bizCompositionRelease.getCompositionBeginTime());
                        bizCompositionLogVO.setCompositionType(Convert.toStr(bizCompositionRelease.getCompositionType()));
                    });
            futureList.add(releaseFuture);
        }

        // 添加其余异步任务到 futureList
        futureList.add(CompletableFuture.supplyAsync(() ->
                        bizCompositionContentService.list(new LambdaQueryWrapper<BizCompositionContent>()
                                .eq(BizCompositionContent::getClId, bizCompositionLogVO.getId())
                                .orderByAsc(BizCompositionContent::getCreateTime)), executor)
                .thenAccept(bizCompositionLogVO::setContentList));

        futureList.add(CompletableFuture.supplyAsync(() ->
                        bizCompositionGoodWordsService.list(new LambdaQueryWrapper<BizCompositionGoodWords>()
                                .eq(BizCompositionGoodWords::getClId, bizCompositionLogVO.getId())
                                .orderByAsc(BizCompositionGoodWords::getGname)), executor)
                .thenAccept(bizCompositionLogVO::setGoodWordsList));

        futureList.add(CompletableFuture.supplyAsync(() ->
                        bizCompositionErrorLogService.list(new LambdaQueryWrapper<BizCompositionErrorLog>()
                                .eq(BizCompositionErrorLog::getClId, bizCompositionLogVO.getId())
                                .orderByAsc(BizCompositionErrorLog::getCreateTime)), executor)
                .thenAccept(bizCompositionLogVO::setErrorList));

        futureList.add(CompletableFuture.supplyAsync(() ->
                        bizCompositionJiService.list(new LambdaQueryWrapper<BizCompositionJi>()
                                .eq(BizCompositionJi::getClId, bizCompositionLogVO.getId())
                                .orderByAsc(BizCompositionJi::getCreateTime)), executor)
                .thenAccept(bizCompositionLogVO::setJiList));

        futureList.add(CompletableFuture.supplyAsync(() ->
                        bizCompositionMeipiService.list(new LambdaQueryWrapper<BizCompositionMeipi>()
                                .eq(BizCompositionMeipi::getClId, bizCompositionLogVO.getId())
                                .orderByAsc(BizCompositionMeipi::getCreateTime)), executor)
                .thenAccept(bizCompositionLogVO::setMeiList));

        futureList.add(CompletableFuture.supplyAsync(() ->
                        bizCompositionCtService.list(new LambdaQueryWrapper<BizCompositionCt>()
                                .eq(BizCompositionCt::getClId, bizCompositionLogVO.getId())
                                .orderByAsc(BizCompositionCt::getCreateTime)), executor)
                .thenAccept(bizCompositionLogVO::setCtList));

        // 等待所有非空任务完成
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();


        return bizCompositionLogVO;
    }

    @Override
    @Transactional
    public Long add(BizCompositionLogAddDTO bizCompositionLogAddDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogAddDTO, BizCompositionLogBO::new);

        if (bizCompositionLogBO.getSchoolId() == null) {
            SchoolUtil.setSchoolInfo(bizCompositionLogBO);
        }
        if (bizCompositionLogAddDTO.getSubmitTime() == null) {
            bizCompositionLogBO.setSubmitTime(LocalDateTime.now());
        }

        // 学生&用户提交情况
        if (bizCompositionLogBO.getAnswerId() == null) {
            LoginUser loginUser = GlobalUserHolder.getUser();
            if (CollUtil.contains(Arrays.asList(SystemUserTypeEnum.USER.getValue(), SystemUserTypeEnum.STUDENT.getValue()), loginUser.getUserType())) {
                bizCompositionLogBO.setAnswerId(loginUser.getId());
                bizCompositionLogBO.setAnswerName(loginUser.getRealName());
                bizCompositionLogBO.setAnswerStudentNum(loginUser.getStudentNum());
                bizCompositionLogBO.setGradeId(loginUser.getGradeId());
                bizCompositionLogBO.setGradeName(loginUser.getGradeName());
                bizCompositionLogBO.setClazzId(loginUser.getClazzId());
                bizCompositionLogBO.setClazzName(loginUser.getClazzName());
            }

            if (CollUtil.contains(Arrays.asList(SystemUserTypeEnum.TEACHER.getValue()), loginUser.getUserType()) && !StrUtil.equals(bizCompositionLogBO.getSource(), CompositionSourceEnum.BATCH.getValue())) {
                bizCompositionLogBO.setAnswerId(loginUser.getId());
                bizCompositionLogBO.setAnswerName(loginUser.getRealName());
                bizCompositionLogBO.setGradeId(null);
                bizCompositionLogBO.setGradeName(null);
                bizCompositionLogBO.setClazzId(null);
                bizCompositionLogBO.setClazzName(null);
            }
        }

        // 批量测评情况
        if (StrUtil.equals(bizCompositionLogBO.getSource(), CompositionSourceEnum.BATCH.getValue())) {
        } else {
            // 非批量测评情况，对第一次测评分数进行限制
            limitFirstEvaluationScore(bizCompositionLogBO);
        }


        if (ObjectUtil.isNotEmpty(bizCompositionLogBO.getCompositionId())) {
            Long evaluationCount = bizCompositionLogService.count(new QueryWrapper<BizCompositionLog>().eq("composition_id", bizCompositionLogBO.getCompositionId()).eq("answer_id", bizCompositionLogBO.getAnswerId()));
            bizCompositionLogBO.setEvaluationCount(Convert.toInt(evaluationCount));

            Double maxScore = bizCompositionLogService.getObj(new QueryWrapper<BizCompositionLog>().select("MAX(score)").eq("composition_id", bizCompositionLogBO.getCompositionId()).eq("answer_id", bizCompositionLogBO.getAnswerId()), obj -> (Double) obj);

            double effectiveMaxScore = maxScore != null ? maxScore : 0.0;
            double currentScore = Optional.ofNullable(bizCompositionLogBO.getScore()).orElse(0.0);
            bizCompositionLogBO.setMaxScore(Math.max(effectiveMaxScore, currentScore));
        } else if (ObjectUtil.isNotEmpty(bizCompositionLogBO.getInitLogId())) {
            Long evaluationCount = bizCompositionLogService.count(new QueryWrapper<BizCompositionLog>().eq("init_log_id", bizCompositionLogBO.getInitLogId()).eq("answer_id", bizCompositionLogBO.getAnswerId()));
            bizCompositionLogBO.setEvaluationCount(Convert.toInt(evaluationCount));

            Double maxScore = bizCompositionLogService.getObj(new QueryWrapper<BizCompositionLog>().select("MAX(score)").eq("init_log_id", bizCompositionLogBO.getInitLogId()).eq("answer_id", bizCompositionLogBO.getAnswerId()), obj -> (Double) obj);

            double effectiveMaxScore = maxScore != null ? maxScore : 0.0;
            double currentScore = Optional.ofNullable(bizCompositionLogBO.getScore()).orElse(0.0);
            bizCompositionLogBO.setMaxScore(Math.max(effectiveMaxScore, currentScore));
        } else {
            // 自主测评第一次情况，当initLogId为空时进行分数限制
            limitFirstEvaluationScore(bizCompositionLogBO);
        }


        boolean success = bizCompositionLogService.save(bizCompositionLogBO);

        // 自主第一次测评
        if (ObjectUtil.isEmpty(bizCompositionLogBO.getInitLogId()) && ObjectUtil.isEmpty(bizCompositionLogBO.getCompositionId())) {
            bizCompositionLogBO.setInitLogId(bizCompositionLogBO.getId());
            bizCompositionLogService.updateById(bizCompositionLogBO);
        } else if (ObjectUtil.isNotEmpty(bizCompositionLogBO.getCompositionId())) {
            // 作业测评   需要判断下
            BizCompositionLog bizCompositionLog = bizCompositionLogService.getOne(new LambdaQueryWrapper<BizCompositionLog>().eq(BizCompositionLog::getCompositionId, bizCompositionLogBO.getCompositionId()).eq(BizCompositionLog::getAnswerId, bizCompositionLogBO.getAnswerId()).orderByAsc(BizCompositionLog::getSubmitTime).last("LIMIT 1"));
            if (ObjectUtil.isNotEmpty(bizCompositionLog)) {
                bizCompositionLogBO.setInitLogId(bizCompositionLog.getId());
                bizCompositionLogService.updateById(bizCompositionLogBO);
            } else {
                bizCompositionLogBO.setInitLogId(bizCompositionLogBO.getId());
                bizCompositionLogService.updateById(bizCompositionLogBO);
            }
        }

        if (success) {
            Long compositionLogId = bizCompositionLogBO.getId();
            try {
                processBatchEntities(bizCompositionLogAddDTO.getContentList(), bizCompositionLogBO, bizCompositionContentService, BizCompositionContent::setClId);
            } catch (Exception e) {
                throw new RuntimeException("保存作文内容失败: " + e.getMessage(), e);
            }
            try {
                processBatchEntities(bizCompositionLogAddDTO.getGoodWordsList(), bizCompositionLogBO, bizCompositionGoodWordsService, BizCompositionGoodWords::setClId);
            } catch (Exception e) {
                throw new RuntimeException("保存作文好词好句失败: " + e.getMessage(), e);
            }

            try {
                processBatchEntities(bizCompositionLogAddDTO.getErrorList(), bizCompositionLogBO, bizCompositionErrorLogService, BizCompositionErrorLog::setClId);
            } catch (Exception e) {
                throw new RuntimeException("保存作文错别字失败: " + e.getMessage(), e);
            }

            try {
                processBatchEntities(bizCompositionLogAddDTO.getJiList(), bizCompositionLogBO, bizCompositionJiService, BizCompositionJi::setClId);
            } catch (Exception e) {
                throw new RuntimeException("保存作文题记失败: " + e.getMessage(), e);
            }

            try {
                processBatchEntities(bizCompositionLogAddDTO.getMeiList(), bizCompositionLogBO, bizCompositionMeipiService, BizCompositionMeipi::setClId);
            } catch (Exception e) {
                throw new RuntimeException("保存作文眉批失败: " + e.getMessage(), e);
            }

            try {
                processBatchEntities(bizCompositionLogAddDTO.getCtList(), bizCompositionLogBO, bizCompositionCtService, BizCompositionCt::setClId);
            } catch (Exception e) {
                throw new RuntimeException("保存作文CT失败: " + e.getMessage(), e);
            }

            return compositionLogId;
        } else {
            // 如果保存失败，可以抛出异常
            throw new RuntimeException("保存作文记录失败");
        }
    }

    @Override
    public Long syncAdd(BizCompositionLogAddDTO bizCompositionLogAddDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogAddDTO, BizCompositionLogBO::new);

        if (ObjectUtil.isNotEmpty(bizCompositionLogBO.getCompositionId())) {
            Long evaluationCount = bizCompositionLogService.count(new QueryWrapper<BizCompositionLog>().eq("composition_id", bizCompositionLogBO.getCompositionId()).eq("answer_id", bizCompositionLogBO.getAnswerId()));
            bizCompositionLogBO.setEvaluationCount(Convert.toInt(evaluationCount));

            Double maxScore = bizCompositionLogService.getObj(new QueryWrapper<BizCompositionLog>().select("MAX(score)").eq("composition_id", bizCompositionLogBO.getCompositionId()).eq("answer_id", bizCompositionLogBO.getAnswerId()), obj -> (Double) obj);

            double effectiveMaxScore = maxScore != null ? maxScore : 0.0;
            double currentScore = Optional.ofNullable(bizCompositionLogBO.getScore()).orElse(0.0);
            bizCompositionLogBO.setMaxScore(Math.max(effectiveMaxScore, currentScore));
        } else if (ObjectUtil.isNotEmpty(bizCompositionLogBO.getInitLogId())) {
            Long evaluationCount = bizCompositionLogService.count(new QueryWrapper<BizCompositionLog>().eq("init_log_id", bizCompositionLogBO.getInitLogId()).eq("answer_id", bizCompositionLogBO.getAnswerId()));
            bizCompositionLogBO.setEvaluationCount(Convert.toInt(evaluationCount));

            Double maxScore = bizCompositionLogService.getObj(new QueryWrapper<BizCompositionLog>().select("MAX(score)").eq("init_log_id", bizCompositionLogBO.getInitLogId()).eq("answer_id", bizCompositionLogBO.getAnswerId()), obj -> (Double) obj);

            double effectiveMaxScore = maxScore != null ? maxScore : 0.0;
            double currentScore = Optional.ofNullable(bizCompositionLogBO.getScore()).orElse(0.0);
            bizCompositionLogBO.setMaxScore(Math.max(effectiveMaxScore, currentScore));
        }

        boolean success = bizCompositionLogService.save(bizCompositionLogBO);

        if (success) {
            Long compositionLogId = bizCompositionLogBO.getId();
            processBatchEntities(bizCompositionLogAddDTO.getContentList(), bizCompositionLogBO, bizCompositionContentService, BizCompositionContent::setClId);
            processBatchEntities(bizCompositionLogAddDTO.getGoodWordsList(), bizCompositionLogBO, bizCompositionGoodWordsService, BizCompositionGoodWords::setClId);

            processBatchEntities(bizCompositionLogAddDTO.getErrorList(), bizCompositionLogBO, bizCompositionErrorLogService, BizCompositionErrorLog::setClId);

            processBatchEntities(bizCompositionLogAddDTO.getJiList(), bizCompositionLogBO, bizCompositionJiService, BizCompositionJi::setClId);

            processBatchEntities(bizCompositionLogAddDTO.getMeiList(), bizCompositionLogBO, bizCompositionMeipiService, BizCompositionMeipi::setClId);

            processBatchEntities(bizCompositionLogAddDTO.getCtList(), bizCompositionLogBO, bizCompositionCtService, BizCompositionCt::setClId);

            return compositionLogId;
        } else {
            // 如果保存失败，可以抛出异常
            throw new RuntimeException("保存作文记录失败");
        }
    }

    @Override
    @Transactional
    public boolean update(BizCompositionLogUpdateDTO bizCompositionLogUpdateDTO) {
        // 确保ID不为空
        if (bizCompositionLogUpdateDTO.getId() == null) {
            log.error("更新作文记录失败: ID不能为空");
            throw new BizException("更新作文记录失败: ID不能为空");
        }
        
        // 记录一下输入参数，以便调试
        log.info("更新作文记录, ID: {}, score: {}, percentage: {}", 
                 bizCompositionLogUpdateDTO.getId(), 
                 bizCompositionLogUpdateDTO.getScore(), 
                 bizCompositionLogUpdateDTO.getPercentage());
        
        // 先查询原有记录，确保它存在
        BizCompositionLog existingLog = bizCompositionLogService.getById(bizCompositionLogUpdateDTO.getId());
        if (existingLog == null) {
            log.error("更新作文记录失败: 找不到ID为{}的记录", bizCompositionLogUpdateDTO.getId());
            throw new BizException("更新作文记录失败: 找不到该记录");
        }
        
        // 转换DTO到BO
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogUpdateDTO, BizCompositionLogBO::new);
        
        // 确保ID字段被正确设置
        bizCompositionLogBO.setId(Long.valueOf(bizCompositionLogUpdateDTO.getId().toString()));
        
        // 根据用户类型设置状态和时间
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (SystemUserTypeEnum.TEACHER.getValue().equals(loginUser.getUserType())) {
            Double maxScore = bizCompositionLogService.getObj(new QueryWrapper<BizCompositionLog>().select("MAX(score)").eq("init_log_id", bizCompositionLogBO.getInitLogId()).eq("answer_id", bizCompositionLogBO.getAnswerId()), obj -> (Double) obj);

            double effectiveMaxScore = maxScore != null ? maxScore : 0.0;
            double currentScore = Optional.ofNullable(bizCompositionLogBO.getScore()).orElse(0.0);
            bizCompositionLogBO.setMaxScore(Math.max(effectiveMaxScore, currentScore));


            bizCompositionLogBO.setAnswerStatus(15);
            bizCompositionLogBO.setReviewTime(LocalDateTime.now());
            log.info("教师用户批阅, 设置状态为15, 批阅时间为{}", bizCompositionLogBO.getReviewTime());
        } else if (SystemUserTypeEnum.STUDENT.getValue().equals(loginUser.getUserType())) {
            bizCompositionLogBO.setAnswerStatus(10);
            bizCompositionLogBO.setSubmitTime(LocalDateTime.now());
            log.info("学生用户提交, 设置状态为10, 提交时间为{}", bizCompositionLogBO.getSubmitTime());
        }
        
        // 执行更新
        boolean result = bizCompositionLogService.updateById(bizCompositionLogBO);
        log.info("更新作文记录结果: {}", result);
        
        return result;
    }

    @Override
    @Transactional
    public boolean batchDelete(BizCompositionLogDeleteDTO bizCompositionLogDeleteDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogDeleteDTO, BizCompositionLogBO::new);
        for (Long id : bizCompositionLogBO.getIds()) {
            bizCompositionContentService.remove(new LambdaQueryWrapper<BizCompositionContent>().eq(BizCompositionContent::getClId, id));
            bizCompositionGoodWordsService.remove(new LambdaQueryWrapper<BizCompositionGoodWords>().eq(BizCompositionGoodWords::getClId, id));
            bizCompositionErrorLogService.remove(new LambdaQueryWrapper<BizCompositionErrorLog>().eq(BizCompositionErrorLog::getClId, id));
            bizCompositionJiService.remove(new LambdaQueryWrapper<BizCompositionJi>().eq(BizCompositionJi::getClId, id));
            bizCompositionMeipiService.remove(new LambdaQueryWrapper<BizCompositionMeipi>().eq(BizCompositionMeipi::getClId, id));
            bizCompositionCtService.remove(new LambdaQueryWrapper<BizCompositionCt>().eq(BizCompositionCt::getClId, id));
        }
        return bizCompositionLogService.removeBatchByIds(bizCompositionLogBO.getIds(), true);
    }

    @Override
    public List<BizCompositionLog> list(BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogSearchDTO, BizCompositionLogBO::new);

        // Extract parameters directly without using a QueryWrapper
        Long compositionId = bizCompositionLogBO.getCompositionId();
        String clazzId = bizCompositionLogBO.getClazzId();
        Integer minStatus = 5; // 默认最小状态为5

        // 确保作文ID和班级编码都被正确传递给service方法
        return bizCompositionLogService.listWithMaxStatus(compositionId, clazzId, minStatus);
    }

    @Override
    public Map<String, Object> countByClazz(BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogSearchDTO, BizCompositionLogBO::new);

        // 提取班级编码和作文ID
        String clazzId = bizCompositionLogBO.getClazzId();
        Long compositionId = bizCompositionLogBO.getCompositionId();

        // 参数校验
        if (ObjectUtil.isEmpty(clazzId)) {
            throw new IllegalArgumentException("班级编码不能为空");
        }

        // 调用service层方法获取统计数据
        return bizCompositionLogService.countStudentsAndSubmissions(clazzId, compositionId);
    }


    /**
     * 批量处理实体列表并保存或更新
     *
     * @param entityList          待处理的实体列表
     * @param bizCompositionLogBO 作文信息
     * @param service             实体对应的Service
     * @param idSetter            ID设置函数（如何设置关联ID），为null时执行更新操作，不为null时执行保存操作
     * @param <T>                 实体类型
     * @return 是否操作成功
     */
    public static <T> boolean processBatchEntities(List<T> entityList, BizCompositionLogBO bizCompositionLogBO, IService<T> service, BiConsumer<T, Long> idSetter) {
        if (ObjectUtil.isEmpty(entityList) || entityList.isEmpty()) {
            return true;
        }
        try {
            for (T entity : entityList) {
                if (idSetter != null) {
                    idSetter.accept(entity, bizCompositionLogBO.getId());
                }
                BizEntity bizEntity = (BizEntity) entity;
                bizEntity.setUserId(bizCompositionLogBO.getAnswerId());
                bizEntity.setSchoolId(bizCompositionLogBO.getSchoolId());
                bizEntity.setGradeId(bizCompositionLogBO.getGradeId());
                bizEntity.setClazzId(bizCompositionLogBO.getClazzId());
            }

            // 根据idSetter是否为null决定执行保存或更新操作
            if (idSetter == null) {
                // 更新操作
                return service.updateBatchById(entityList);
            } else {
                // 保存操作
                return service.saveBatch(entityList);
            }
        } catch (Exception e) {
            String operationType = idSetter == null ? "更新" : "保存";
            throw new RuntimeException("批量" + operationType + "实体失败: " + e.getMessage(), e);
        }
    }


    @Override
    public boolean matchStudentAndComposition(BizCompositionLogMatchStudentDTO bizCompositionLogMatchStudentDTO) {
        BizCompositionLogBO bizCompositionLogBO = CglibUtil.convertObj(bizCompositionLogMatchStudentDTO, BizCompositionLogBO::new);

        BizCompositionLog bizCompositionLog = bizCompositionLogService.getOne(new LambdaQueryWrapper<BizCompositionLog>().eq(BizCompositionLog::getCompositionId, bizCompositionLogBO.getCompositionId()).eq(BizCompositionLog::getAnswerId, bizCompositionLogBO.getAnswerId()).orderByDesc(BizCompositionLog::getCreateTime));
        if (ObjectUtil.isNotEmpty(bizCompositionLog)) {
            bizCompositionLogBO.setInitLogId(bizCompositionLog.getId());
        } else {
            bizCompositionLogBO.setInitLogId(bizCompositionLogBO.getId());
        }

        bizCompositionLogService.updateById(bizCompositionLogBO);

        List<BizCompositionErrorLog> bizCompositionErrorLogList = bizCompositionErrorLogService.list(new LambdaQueryWrapper<BizCompositionErrorLog>().eq(BizCompositionErrorLog::getClId, bizCompositionLogBO.getId()));
        processBatchEntities(bizCompositionErrorLogList, bizCompositionLogBO, bizCompositionErrorLogService, null);

        List<BizCompositionJi> bizCompositionJiList = bizCompositionJiService.list(new LambdaQueryWrapper<BizCompositionJi>().eq(BizCompositionJi::getClId, bizCompositionLogBO.getId()));
        processBatchEntities(bizCompositionJiList, bizCompositionLogBO, bizCompositionJiService, null);

        List<BizCompositionMeipi> bizCompositionMeipiList = bizCompositionMeipiService.list(new LambdaQueryWrapper<BizCompositionMeipi>().eq(BizCompositionMeipi::getClId, bizCompositionLogBO.getId()));
        processBatchEntities(bizCompositionMeipiList, bizCompositionLogBO, bizCompositionMeipiService, null);

        List<BizCompositionCt> bizCompositionCtList = bizCompositionCtService.list(new LambdaQueryWrapper<BizCompositionCt>().eq(BizCompositionCt::getClId, bizCompositionLogBO.getId()));
        processBatchEntities(bizCompositionCtList, bizCompositionLogBO, bizCompositionCtService, null);

        List<BizCompositionGoodWords> bizCompositionGoodWordsList = bizCompositionGoodWordsService.list(new LambdaQueryWrapper<BizCompositionGoodWords>().eq(BizCompositionGoodWords::getClId, bizCompositionLogBO.getId()));
        processBatchEntities(bizCompositionGoodWordsList, bizCompositionLogBO, bizCompositionGoodWordsService, null);

        List<BizCompositionContent> bizCompositionContentList = bizCompositionContentService.list(new LambdaQueryWrapper<BizCompositionContent>().eq(BizCompositionContent::getClId, bizCompositionLogBO.getId()));
        processBatchEntities(bizCompositionContentList, bizCompositionLogBO, bizCompositionContentService, null);

        return true;
    }

    @Override
    public List<BizCompositionHistoryLogVO> userList(BizCompositionLogSearchDTO bizCompositionLogSearchDTO) {
        bizCompositionLogSearchDTO.setAnswerId(GlobalUserHolder.getUser().getId());
        List<BizCompositionLog> bizCompositionLogList = bizCompositionLogService.list(new LambdaQueryWrapper<BizCompositionLog>()
                .eq(BizCompositionLog::getAnswerId, bizCompositionLogSearchDTO.getAnswerId())
                .eq(BizCompositionLog::getInitLogId, bizCompositionLogSearchDTO.getInitLogId())
                .orderByAsc(BizCompositionLog::getSubmitTime));
        return CglibUtil.convertList(bizCompositionLogList, BizCompositionHistoryLogVO::new);
    }

    @Override
    public List<BizCompositionLogExportVO> listForExport(BizCompositionLogExportSearchDTO bizCompositionLogSearchDTO) {
        return bizCompositionLogService.listForExport(bizCompositionLogSearchDTO);
    }

    /**
     * 限制第一次测评分数
     * 当initLogId为空且年级为初中或高中时，如果百分比超过91%，控制在90-91%之间
     * 
     * @param bizCompositionLogBO 作文记录BO对象
     */
    private void limitFirstEvaluationScore(BizCompositionLogBO bizCompositionLogBO) {
        // 只有当initLogId为空时才进行限制
        if (ObjectUtil.isNotEmpty(bizCompositionLogBO.getInitLogId())) {
            return;
        }
        
        String grade = bizCompositionLogBO.getGrade();
        Double currentScore = bizCompositionLogBO.getScore();
        Double fullScore = bizCompositionLogBO.getFullScore();
        
        // 检查必要字段
        if (StrUtil.isBlank(grade) || currentScore == null || fullScore == null || fullScore <= 0) {
            return;
        }
        
        // 判断年级是否为初中或高中
        boolean isJuniorOrSeniorHigh = grade.contains("初") || grade.contains("高");
        if (!isJuniorOrSeniorHigh) {
            return;
        }
        
        // 计算当前百分比
        double currentPercentage = (currentScore / fullScore) * 100;
        
        // 如果百分比超过91%，进行限制
        if (currentPercentage > 91.0) {
            // 生成90-91之间的随机百分比，保留两位小数
            double randomPercentage = 90.0 + ThreadLocalRandom.current().nextDouble() * 1.0;
            BigDecimal limitedPercentage = new BigDecimal(randomPercentage).setScale(2, RoundingMode.HALF_UP);
            
            // 计算对应的分数
            double limitedScore = (limitedPercentage.doubleValue() / 100.0) * fullScore;
            BigDecimal limitedScoreBD = new BigDecimal(limitedScore).setScale(2, RoundingMode.HALF_UP);
            
            // 更新BO对象
            bizCompositionLogBO.setScore(limitedScoreBD.doubleValue());
            bizCompositionLogBO.setPercentage(limitedPercentage.doubleValue());
            
            log.info("第一次测评分数限制: 年级={}, 满分={}, 原始分数={}, 原始百分比={:.2f}%, 限制后分数={}, 限制后百分比={:.2f}%", 
                    grade, fullScore, currentScore, currentPercentage, 
                    bizCompositionLogBO.getScore(), bizCompositionLogBO.getPercentage());
        } else {
            // 未超过91%，设置正常的百分比
            BigDecimal percentage = new BigDecimal(currentPercentage).setScale(2, RoundingMode.HALF_UP);
            bizCompositionLogBO.setPercentage(percentage.doubleValue());
        }
    }
}