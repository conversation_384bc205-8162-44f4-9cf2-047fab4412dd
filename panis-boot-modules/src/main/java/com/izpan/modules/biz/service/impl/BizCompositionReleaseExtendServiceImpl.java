package com.izpan.modules.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izpan.modules.biz.domain.entity.BizCompositionReleaseExtend;
import com.izpan.modules.biz.repository.mapper.BizCompositionReleaseExtendMapper;
import com.izpan.modules.biz.service.IBizCompositionReleaseExtendService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.collection.CollectionUtil;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BizCompositionReleaseExtendServiceImpl extends ServiceImpl<BizCompositionReleaseExtendMapper, BizCompositionReleaseExtend> implements IBizCompositionReleaseExtendService {

    /**
     * 绑定作文与班级的关联关系
     *
     * @param compositionId 作文ID
     * @param newList 新的班级关联列表
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindReleaseObject(Long compositionId, List<BizCompositionReleaseExtend> newList) {
        try {
            // 参数校验
            if (compositionId == null) {
                log.error("绑定作文与班级关联失败：作文ID为空");
                return false;
            }

            // 1. 删除所有现有关联
            baseMapper.deletePhysicallyByCompositionId(compositionId);

            // 2. 如果有新关联，则批量保存
            if (CollectionUtil.isNotEmpty(newList)) {
                // 确保每个记录都有正确的作文ID
                newList.forEach(item -> item.setCompositionId(compositionId));
                boolean addResult = saveBatch(newList);
                log.info("作文[{}]新增了{}个班级关联", compositionId, newList.size());
                return addResult;
            }

            log.info("作文[{}]清空所有班级关联", compositionId);
            return true;
        } catch (Exception e) {
            log.error("绑定作文与班级关联异常", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }
}
