/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.dto.composition.release;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作文发布表 编辑更新 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.dto.composition.release.BizCompositionReleaseUpdateDTO
 * @CreateTime 2025-03-26 - 21:13:58
 */

@Getter
@Setter
@Schema(name = "BizCompositionReleaseUpdateDTO", description = "作文发布表 编辑更新 DTO 对象")
public class BizCompositionReleaseUpdateDTO implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "作文标题")
    private String compositionTitle;

    @Schema(description = "作业内容")
    private String compositionContent;

    @Schema(description = "发布类别：0 平台 1 本校 2 年级 3 班级")
    private Integer releaseType;

    @Schema(description = "作业类型:1作业 ")
    private String compositionType;

    @Schema(description = "发布状态:0 草稿 1 发文")
    private Integer compositionStatus;

    @Schema(description = "文体类型:1 叙事文 2 议论文")
    private String compositionWenTi;

    @Schema(description = "字数要求:1 100 2 200")
    private Integer compositionWordCount;

    @Schema(description = "参考内容路径")
    private String compositionExamplePath;

    @Schema(description = "作业资源路径")
    private String compositionResourcePath;

    @Schema(description = "作文开始时间")
    private LocalDateTime compositionBeginTime;

    @Schema(description = "作文截至时间")
    private LocalDateTime compositionEndTime;

    @Schema(description = "满分值")
    private Integer compositionFullScore;

    @Schema(description = "单元Id")
    private Long dId;

    @Schema(description = "单元名称")
    private String dDanyuan;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "年级编码")
    private String gradeId;

    @Schema(description = "年级名称")
    private String gradeName;

    @Schema(description = "班级名称")
    private String clazzName;

    @Schema(description = "班级编码")
    private String clazzId;

    @Schema(description = "是否为模版")
    private String isTemplate;

}