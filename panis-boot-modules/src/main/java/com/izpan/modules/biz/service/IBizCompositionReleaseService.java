/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.modules.biz.domain.bo.BizCompositionReleaseBO;
import com.izpan.modules.biz.domain.dto.composition.release.BizCompositionReleaseAddDTO;
import com.izpan.modules.biz.domain.dto.composition.release.CompositionStudentQueryDTO;
import com.izpan.modules.biz.domain.entity.BizCompositionRelease;
import com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO;
import com.izpan.modules.system.domain.vo.SysUserVO;

import java.util.List;

/**
 * 作文发布表 Service 服务接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.IBizCompositionReleaseService
 * @CreateTime 2024-12-18 - 12:57:47
 */

public interface IBizCompositionReleaseService extends IService<BizCompositionRelease> {

    /**
     * 作文发布表 - 分页查询
     *
     * @param pageQuery               分页对象
     * @param bizCompositionReleaseBO BO 查询对象
     * @return {@link IPage} 分页结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    IPage<BizCompositionReleaseVO> listBizCompositionReleasePage(PageQuery pageQuery, BizCompositionReleaseBO bizCompositionReleaseBO);

    boolean updateCompositionRelease(Long id, int status);

    boolean compositionCreateAndRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO);

    boolean compositionCreate(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO);

    SysUserVO studentAnswerComposition(Long compositionId, String stu, Boolean isCheck);

    List<BizCompositionReleaseVO> queryCompositionList(CompositionStudentQueryDTO compositionStudentQueryDTO);

    /**
     * 根据ID查询作文发布详情并关联作文扩展表
     *
     * @param id 作文发布ID
     * @return 作文发布详情VO对象，包含作文扩展表信息
     */
    BizCompositionReleaseVO getDetailWithExtendById(Long id);

    Long syncCompositionCreateOrRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO);
}
