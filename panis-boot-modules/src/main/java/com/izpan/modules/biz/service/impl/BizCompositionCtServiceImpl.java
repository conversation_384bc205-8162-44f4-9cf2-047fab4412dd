/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import com.izpan.modules.biz.domain.bo.BizCompositionCtBO;
import com.izpan.modules.biz.domain.entity.BizCompositionCt;
import com.izpan.modules.biz.repository.mapper.BizCompositionCtMapper;
import com.izpan.modules.biz.service.IBizCompositionCtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 作文CT表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionCtServiceImpl
 * @CreateTime 2025-04-09 - 12:48:39
 */

@Service
public class BizCompositionCtServiceImpl extends ServiceImpl<BizCompositionCtMapper, BizCompositionCt> implements IBizCompositionCtService {

    @Override
    public IPage<BizCompositionCt> listBizCompositionCtPage(PageQuery pageQuery, BizCompositionCtBO bizCompositionCtBO) {
        LambdaQueryWrapper<BizCompositionCt> queryWrapper = new LambdaQueryWrapper<BizCompositionCt>()
            .eq(ObjectUtils.isNotEmpty(bizCompositionCtBO.getClId()), BizCompositionCt::getClId, bizCompositionCtBO.getClId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionCtBO.getCtitle()), BizCompositionCt::getCtitle, bizCompositionCtBO.getCtitle())
            .eq(ObjectUtils.isNotEmpty(bizCompositionCtBO.getUserId()), BizCompositionCt::getUserId, bizCompositionCtBO.getUserId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionCtBO.getSchoolId()), BizCompositionCt::getSchoolId, bizCompositionCtBO.getSchoolId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionCtBO.getGradeId()), BizCompositionCt::getGradeId, bizCompositionCtBO.getGradeId())
            .eq(ObjectUtils.isNotEmpty(bizCompositionCtBO.getClazzId()), BizCompositionCt::getClazzId, bizCompositionCtBO.getClazzId()).orderByDesc(BizCompositionCt::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

}

