package com.izpan.modules.biz.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 作文评测统计信息VO
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName BizCompositionStatsVO
 * @CreateTime 2025-04-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "作文评测统计信息VO")
public class BizCompositionStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 测评总篇数
     */
    @Schema(description = "测评总篇数")
    private Integer totalCount;

    /**
     * 总修改次数
     */
    @Schema(description = "总修改次数")
    private Integer totalEditCount;

    /**
     * 单篇最高修改次数
     */
    @Schema(description = "单篇最高修改次数")
    private Integer maxEditCount;

    /**
     * 单篇最高提分
     */
    @Schema(description = "单篇最高提分")
    private Double maxImproveScore;

    /**
     * A全体学生平均提分
     */
    @Schema(description = "全体学生平均提分")
    private Double avgImproveScore;

    /**
     * 统计结果摘要
     */
    @Schema(description = "统计结果摘要")
    private String summary;
} 