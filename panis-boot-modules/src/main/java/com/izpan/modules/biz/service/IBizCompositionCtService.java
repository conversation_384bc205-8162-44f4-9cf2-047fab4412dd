/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service;

import com.izpan.infrastructure.page.PageQuery;
import com.izpan.modules.biz.domain.bo.BizCompositionCtBO;
import com.izpan.modules.biz.domain.entity.BizCompositionCt;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 作文CT表 Service 服务接口层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.IBizCompositionCtService
 * @CreateTime 2025-04-09 - 12:48:39
 */

public interface IBizCompositionCtService extends IService<BizCompositionCt> {

    /**
     * 作文CT表 - 分页查询
     *
     * @param pageQuery 分页对象
     * @param bizCompositionCtBO BO 查询对象
     * @return {@link IPage} 分页结果
     * <AUTHOR>
     * @CreateTime 2025-04-09 - 12:48:39
     */
    IPage<BizCompositionCt> listBizCompositionCtPage(PageQuery pageQuery, BizCompositionCtBO bizCompositionCtBO);
}
