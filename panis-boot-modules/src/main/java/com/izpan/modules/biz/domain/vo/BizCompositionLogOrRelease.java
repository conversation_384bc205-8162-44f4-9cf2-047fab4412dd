package com.izpan.modules.biz.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 作文发布表和作文记录表的公共属性
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.vo.BizCompositionLogOrRelease
 * @CreateTime 2025-03-23 - 12:45:30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BizCompositionLogOrRelease", description = "作文发布表 导出 对象")
public class BizCompositionLogOrRelease {

    @Schema(description = "作文类别")
    private String compositionType;

    @Schema(description = "作文文体")
    private String compositionWenTi;

    @Schema(description = "作业内容要求")
    private String compositionContent;

    @Schema(description = "字数要求")
    private Integer compositionWordCount;

    @Schema(description = "作文标题")
    private String compositionTitle;

    @Schema(description = "参考内容路径")
    private String compositionExamplePath;

    @Schema(description = "作业资源路径")
    private String compositionResourcePath;

    @Schema(description = "作文开始时间")
    private LocalDateTime compositionBeginTime;

    @Schema(description = "作文截至时间")
    private LocalDateTime compositionEndTime;

    @Schema(description = "发布状态:0 草稿 1 发文")
    private Integer compositionStatus;
}
