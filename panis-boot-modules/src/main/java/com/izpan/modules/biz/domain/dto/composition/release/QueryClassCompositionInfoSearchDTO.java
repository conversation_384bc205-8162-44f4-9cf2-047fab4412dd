package com.izpan.modules.biz.domain.dto.composition.release;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Schema(name = "QueryClassCompositionInfoSearchDTO", description = "查询班级作文情况")
public class QueryClassCompositionInfoSearchDTO implements Serializable {

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "年级编码")
    private String gradeId;

    @Schema(description = "班级编码")
    private String clazzId;

    @Schema(description = "作文标题")
    private String compositionTitle;

    @Schema(description = "年级编码List")
    private List<String> gradeIdList;

    @Schema(description = "班级编码List")
    private List<String> clazzIdList;

}