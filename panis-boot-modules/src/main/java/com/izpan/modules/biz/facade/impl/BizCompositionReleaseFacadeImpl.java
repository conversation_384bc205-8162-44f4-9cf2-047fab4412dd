/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.facade.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.util.CglibUtil;
import com.izpan.common.util.LongUtil;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.infrastructure.util.SchoolUtil;
import com.izpan.modules.biz.domain.bo.BizCompositionReleaseBO;
import com.izpan.modules.biz.domain.dto.composition.release.*;
import com.izpan.modules.biz.domain.entity.BizCompositionRelease;
import com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO;
import com.izpan.modules.biz.facade.IBizCompositionReleaseFacade;
import com.izpan.modules.biz.service.IBizCompositionReleaseService;
import com.izpan.modules.system.domain.vo.SysUserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作文发布表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.facade.impl.BizCompositionReleaseFacadeImpl
 * @CreateTime 2024-12-18 - 12:57:47
 */

@Service
@RequiredArgsConstructor
public class BizCompositionReleaseFacadeImpl implements IBizCompositionReleaseFacade {

    @NonNull
    private IBizCompositionReleaseService bizCompositionReleaseService;

    @Override
    public RPage<BizCompositionReleaseVO> listBizCompositionReleasePage(PageQuery pageQuery, BizCompositionReleaseSearchDTO bizCompositionReleaseSearchDTO) {
        BizCompositionReleaseBO bizCompositionReleaseBO = CglibUtil.convertObj(bizCompositionReleaseSearchDTO, BizCompositionReleaseBO::new);
        IPage<BizCompositionReleaseVO> bizCompositionReleaseIPage = bizCompositionReleaseService.listBizCompositionReleasePage(pageQuery, bizCompositionReleaseBO);
        return RPage.build(bizCompositionReleaseIPage, BizCompositionReleaseVO::new);
    }

    @Override
    public BizCompositionReleaseVO get(Long id) {
        // 使用连表查询方法获取详情，包含扩展表信息
        return bizCompositionReleaseService.getDetailWithExtendById(id);
    }

    @Override
    @Transactional
    public boolean add(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        BizCompositionReleaseBO bizCompositionReleaseBO = CglibUtil.convertObj(bizCompositionReleaseAddDTO, BizCompositionReleaseBO::new);
        return bizCompositionReleaseService.save(bizCompositionReleaseBO);
    }

    @Override
    @Transactional
    public boolean update(BizCompositionReleaseUpdateDTO bizCompositionReleaseUpdateDTO) {
        BizCompositionReleaseBO bizCompositionReleaseBO = CglibUtil.convertObj(bizCompositionReleaseUpdateDTO, BizCompositionReleaseBO::new);
        return bizCompositionReleaseService.updateById(bizCompositionReleaseBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BizCompositionReleaseDeleteDTO bizCompositionReleaseDeleteDTO) {
        BizCompositionReleaseBO bizCompositionReleaseBO = CglibUtil.convertObj(bizCompositionReleaseDeleteDTO, BizCompositionReleaseBO::new);
        return bizCompositionReleaseService.removeBatchByIds(bizCompositionReleaseBO.getIds(), true);
    }

    /**
     * 用户查询作文列表
     */
    @Override
    public List<BizCompositionReleaseVO> queryCompositionList(CompositionStudentQueryDTO compositionStudentQueryDTO) {
        LoginUser loginUser = GlobalUserHolder.getUser();

        if (compositionStudentQueryDTO.getAnswerId() == null) {
            compositionStudentQueryDTO.setAnswerId(loginUser.getId());
        }

        // 平台  "发布类别：0 平台 1 本校 2 年级 3 班级"
        if (compositionStudentQueryDTO.getReleaseType() == 0) {
            // 保持不变
        }
        // 校级
        if (compositionStudentQueryDTO.getReleaseType() == 1) {
            compositionStudentQueryDTO.setSchoolId(loginUser.getSchoolId());
        }
        // 年级
        if (compositionStudentQueryDTO.getReleaseType() == 2) {
            compositionStudentQueryDTO.setGradeId(loginUser.getGradeId());
        }
        // 班级
        if (compositionStudentQueryDTO.getReleaseType() == 3) {
            compositionStudentQueryDTO.setClazzId(loginUser.getClazzId());
        }

        return bizCompositionReleaseService.queryCompositionList(compositionStudentQueryDTO);
    }


    @Override
    public SysUserVO studentAnswerComposition(Long compositionId, String studentNum, Boolean isCheck) {
        return bizCompositionReleaseService.studentAnswerComposition(compositionId, studentNum, isCheck);
    }

    /**
     * 发布作文
     *
     * @param compositionReleaseDTO
     * @return
     */
    @Override
    public boolean compositionRelease(CompositionReleaseDTO compositionReleaseDTO) {
        return bizCompositionReleaseService.updateCompositionRelease(compositionReleaseDTO.getId(), compositionReleaseDTO.getStatus());
    }

    /**
     * 保存并发布作文
     *
     * @param bizCompositionReleaseAddDTO
     * @return
     */
    @Override
    public boolean compositionCreateAndRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return bizCompositionReleaseService.compositionCreateAndRelease(bizCompositionReleaseAddDTO);
    }

    /**
     * 保存作文
     *
     * @param bizCompositionReleaseAddDTO
     * @return
     */
    @Override
    public boolean compositionCreate(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return bizCompositionReleaseService.compositionCreate(bizCompositionReleaseAddDTO);
    }

    /**
     * 老师查询作文列表
     *
     * @param bizCompositionReleaseSearchDTO 查询对象
     * @return {@link List} 查询结果
     * <AUTHOR>
     * @CreateTime 2024-12-18 - 12:57:47
     */
    @Override
    public List<BizCompositionReleaseVO> list(BizCompositionReleaseSearchDTO bizCompositionReleaseSearchDTO) {

        SchoolUtil.setSchoolId(bizCompositionReleaseSearchDTO);
        List<BizCompositionRelease> releaseList = bizCompositionReleaseService.list(new LambdaQueryWrapper<BizCompositionRelease>()
                .eq(ObjectUtil.isNotEmpty(bizCompositionReleaseSearchDTO.getCompositionType()), BizCompositionRelease::getCompositionType, bizCompositionReleaseSearchDTO.getCompositionType())
                .eq(BizCompositionRelease::getSchoolId, bizCompositionReleaseSearchDTO.getSchoolId())
                .eq(StrUtil.isNotBlank(bizCompositionReleaseSearchDTO.getIsTemplate()), BizCompositionRelease::getIsTemplate, bizCompositionReleaseSearchDTO.getIsTemplate())
                .orderByDesc(BizCompositionRelease::getCreateTime));

        return CglibUtil.convertList(releaseList, BizCompositionReleaseVO::new);
    }
}