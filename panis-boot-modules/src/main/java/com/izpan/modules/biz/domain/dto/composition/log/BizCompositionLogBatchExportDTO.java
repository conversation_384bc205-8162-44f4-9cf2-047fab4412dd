/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.dto.composition.log;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 作文记录表批量导出 DTO 对象
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName BizCompositionLogBatchExportDTO
 */
@Getter
@Setter
@Schema(name = "BizCompositionLogBatchExportDTO", description = "作文记录表批量导出 DTO 对象")
public class BizCompositionLogBatchExportDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8613304082968236155L;

    @Schema(description = "作文记录ID列表")
    @NotEmpty(message = "作文记录ID列表不能为空")
    @Size(max = 100, message = "单次批量导出不能超过100个作文")
    private List<Long> compositionIds;

    @Schema(description = "压缩包文件名（可选，为空时自动生成）")
    private String zipFileName;
} 