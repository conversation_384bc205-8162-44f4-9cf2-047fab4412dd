/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izpan.common.domain.LoginUser;
import com.izpan.common.exception.BizException;
import com.izpan.common.util.CglibUtil;
import com.izpan.common.util.CollectionUtil;
import com.izpan.infrastructure.enums.PositionIdentifyEnum;
import com.izpan.infrastructure.enums.SystemUserTypeEnum;
import com.izpan.infrastructure.holder.GlobalUserHolder;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.util.SchoolUtil;
import com.izpan.modules.biz.domain.bo.BizCompositionReleaseBO;
import com.izpan.modules.biz.domain.dto.composition.release.BizCompositionReleaseAddDTO;
import com.izpan.modules.biz.domain.dto.composition.release.CompositionStudentQueryDTO;
import com.izpan.modules.biz.domain.entity.BizCompositionLog;
import com.izpan.modules.biz.domain.entity.BizCompositionRelease;
import com.izpan.modules.biz.domain.entity.BizCompositionReleaseExtend;
import com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO;
import com.izpan.modules.biz.repository.mapper.BizCompositionReleaseMapper;
import com.izpan.modules.biz.service.IBizCompositionLogService;
import com.izpan.modules.biz.service.IBizCompositionReleaseExtendService;
import com.izpan.modules.biz.service.IBizCompositionReleaseService;
import com.izpan.modules.system.domain.entity.SysOrgUnits;
import com.izpan.modules.system.domain.entity.SysUser;
import com.izpan.modules.system.domain.vo.SysUserVO;
import com.izpan.modules.system.service.ISysOrgUnitsService;
import com.izpan.modules.system.service.ISysUserService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 作文发布表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.service.impl.BizCompositionReleaseServiceImpl
 * @CreateTime 2024-12-18 - 12:57:47
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BizCompositionReleaseServiceImpl extends ServiceImpl<BizCompositionReleaseMapper, BizCompositionRelease> implements IBizCompositionReleaseService {
    @NonNull
    private IBizCompositionReleaseExtendService bizCompositionReleaseExtendService;

    @NonNull
    private ISysOrgUnitsService sysOrgUnitsService;

    @NonNull
    private ISysUserService sysUserService;

    @NonNull
    private BizCompositionReleaseMapper bizCompositionReleaseMapper;

    @NonNull
    private IBizCompositionLogService bizCompositionLogService;

    @Override
    public IPage<BizCompositionReleaseVO> listBizCompositionReleasePage(PageQuery pageQuery, BizCompositionReleaseBO bizCompositionReleaseBO) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        QueryWrapper<BizCompositionRelease> queryWrapper = new QueryWrapper<BizCompositionRelease>();
        queryWrapper.apply("bcr.school_id = {0}", loginUser.getSchoolId())
                .eq(ObjectUtils.isNotEmpty(bizCompositionReleaseBO.getCompositionStatus()),
                        "bcr.composition_status", bizCompositionReleaseBO.getCompositionStatus())
                .like(ObjectUtils.isNotEmpty(bizCompositionReleaseBO.getCompositionTitle()),
                        "bcr.composition_title", bizCompositionReleaseBO.getCompositionTitle())
                .like(ObjectUtils.isNotEmpty(bizCompositionReleaseBO.getClazzId()),
                        "bcr.clazz_id", bizCompositionReleaseBO.getClazzId())
                .eq("bcr.is_deleted", 0);


        if (loginUser.getPositionIdentify().equals(PositionIdentifyEnum.GRADE_DIRECTOR.getValue())) {
            queryWrapper.and(wq -> wq.eq("bcr.create_user_id", loginUser.getId())
                    .or()
                    .eq("bcr.grade_id", loginUser.getGradeId()));
        } else if (loginUser.getPositionIdentify().equals(PositionIdentifyEnum.TEACHER.getValue())) {
            queryWrapper.eq("bcr.create_user_id", loginUser.getId());
        }

        queryWrapper.orderByDesc("bcr.create_time");

        Page<BizCompositionReleaseVO> page = new Page<>(pageQuery.getPage(), pageQuery.getPageSize());

        return baseMapper.listWithExtend(page, queryWrapper);
    }

    @Override
    public boolean updateCompositionRelease(Long id, int status) {
        LambdaQueryWrapper<BizCompositionRelease> queryWrapper = new LambdaQueryWrapper<BizCompositionRelease>().eq(BizCompositionRelease::getId, id).eq(BizCompositionRelease::getCompositionStatus, status);
        return baseMapper.update(queryWrapper) > 0;
    }

    public Long compositionCreateOrRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO, boolean isRelease) {
        // 获取当前登录用户
        LoginUser loginUser = GlobalUserHolder.getUser();
        if (loginUser == null) {
            log.warn("当前没有登录用户，无法创建或发布作文");
            return null;
        }

        // 设置作文状态（1=发布，0=草稿）
        bizCompositionReleaseAddDTO.setCompositionStatus(isRelease ? 1 : 0);
        log.info("正在{}作文", isRelease ? "发布" : "保存草稿");

        // 转换为实体并设置学校信息
        BizCompositionReleaseBO bizCompositionRelease = CglibUtil.convertObj(bizCompositionReleaseAddDTO, BizCompositionReleaseBO::new);
        SchoolUtil.setSchoolInfo(bizCompositionRelease);

        // 插入或更新作文基本信息
        int insertOrUpdateResult;
        if (ObjectUtils.isEmpty(bizCompositionReleaseAddDTO.getId())) {
            log.info("创建新作文");
            insertOrUpdateResult = baseMapper.insert(bizCompositionRelease);
        } else {
            log.info("更新现有作文 ID={}", bizCompositionReleaseAddDTO.getId());
            insertOrUpdateResult = baseMapper.updateById(bizCompositionRelease);
        }

        if (insertOrUpdateResult <= 0) {
            log.error("作文基本信息保存失败");
            return null;
        }

        // 获取作文ID
        Long compositionId = bizCompositionRelease.getId();

        // 处理作文发布的班级关联
        List<BizCompositionReleaseExtend> extendList = createExtendList(
                compositionId,
                bizCompositionReleaseAddDTO.getGradeId(),
                bizCompositionReleaseAddDTO.getGradeName(),
                bizCompositionReleaseAddDTO.getClazzId(),
                bizCompositionReleaseAddDTO.getClazzName(),
                loginUser
        );

        // 绑定作文与班级的关联关系
        boolean bindResult = bizCompositionReleaseExtendService.bindReleaseObject(compositionId, extendList);
        if (!bindResult) {
            log.warn("作文与班级关联绑定失败，compositionId={}", compositionId);
        }

        return bizCompositionRelease.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long syncCompositionCreateOrRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {


        // 转换为实体并设置学校信息
        BizCompositionReleaseBO bizCompositionRelease = CglibUtil.convertObj(bizCompositionReleaseAddDTO, BizCompositionReleaseBO::new);

        // 插入或更新作文基本信息
        baseMapper.insert(bizCompositionRelease);

        // 绑定作文与班级的关联关系
        bizCompositionReleaseExtendService.saveBatch(bizCompositionReleaseAddDTO.getExtendList());

        return bizCompositionRelease.getId();
    }

    /**
     * 创建作文发布扩展对象列表
     *
     * @param compositionId 作文ID
     * @param gradeId       年级ID
     * @param gradeName     年级名称
     * @param clazzId       班级ID（可能包含多个，以逗号分隔）
     * @param clazzName     班级名称（可能包含多个，以逗号分隔）
     * @param loginUser     当前登录用户
     * @return 扩展对象列表
     */
    private List<BizCompositionReleaseExtend> createExtendList(Long compositionId, String gradeId, String gradeName,
                                                               String clazzId, String clazzName, LoginUser loginUser) {
        List<BizCompositionReleaseExtend> extendList = new ArrayList<>();

        // 如果未指定班级，但指定了年级，则获取该年级下的所有班级
//        if (StrUtil.isEmpty(clazzId)) {
//            if (StrUtil.isNotEmpty(gradeId)) {
//                extendList.addAll(createExtendListFromGrade(compositionId, gradeId, gradeName, loginUser));
//            } else {
//                log.warn("未指定班级和年级，无法创建扩展关联");
//            }
//            return extendList;
//        }

        // 处理指定班级的情况
        if (clazzId.contains(",")) {
            // 多个班级
            extendList.addAll(createExtendListFromMultipleClazz(compositionId, gradeId, gradeName, clazzId, clazzName));
        } else {
            // 单个班级
            extendList.add(createExtendObject(compositionId, gradeId, gradeName, clazzId, clazzName));
        }

        return extendList;
    }

    /**
     * 从年级创建扩展对象列表
     */
    private List<BizCompositionReleaseExtend> createExtendListFromGrade(Long compositionId, String gradeId, String gradeName, LoginUser loginUser) {
        List<BizCompositionReleaseExtend> result = new ArrayList<>();

        // 获取年级下所有班级
        List<SysOrgUnits> clazzList = sysOrgUnitsService.listChildren(gradeId);
        if (CollectionUtil.isEmpty(clazzList)) {
            log.warn("年级[{}]{}下没有班级", gradeId, gradeName);
            return result;
        }

        // 为每个班级创建扩展对象
        for (SysOrgUnits clazz : clazzList) {
            BizCompositionReleaseExtend extend = createExtendObject(
                    compositionId,
                    gradeId,
                    gradeName,
                    clazz.getId().toString(),
                    clazz.getName()
            );
            result.add(extend);
        }

        return result;
    }

    /**
     * 从多个班级创建扩展对象列表
     */
    private List<BizCompositionReleaseExtend> createExtendListFromMultipleClazz(Long compositionId, String gradeId, String gradeName, String clazzIds, String clazzNames) {
        List<BizCompositionReleaseExtend> result = new ArrayList<>();

        // 分割班级ID和名称
        String[] clazzIdArr = clazzIds.split(",");
        String[] clazzNameArr = clazzNames.split(",");

        // 为每个班级创建扩展对象
        for (int i = 0; i < clazzIdArr.length; i++) {
            result.add(createExtendObject(compositionId, gradeId, gradeName, clazzIdArr[i], clazzNameArr[i]));
        }

        return result;
    }

    /**
     * 创建单个扩展对象
     */
    private BizCompositionReleaseExtend createExtendObject(Long compositionId, String gradeId, String gradeName,
                                                           String clazzId, String clazzName) {
        BizCompositionReleaseExtend extend = new BizCompositionReleaseExtend();
        extend.setCompositionId(compositionId);
        SchoolUtil.setSchoolAndGradeAndClazzWithNames(
                null, SchoolUtil.getSchoolName(),
                gradeId, gradeName,
                clazzId, clazzName,
                extend
        );
        return extend;
    }

    @Override
    @Transactional
    public boolean compositionCreateAndRelease(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return compositionCreateOrRelease(bizCompositionReleaseAddDTO, true) != null;
    }

    @Override
    public boolean compositionCreate(BizCompositionReleaseAddDTO bizCompositionReleaseAddDTO) {
        return compositionCreateOrRelease(bizCompositionReleaseAddDTO, false) != null;
    }

    @Override
    public SysUserVO studentAnswerComposition(Long compositionId, String studentNum, Boolean isCheck) {
        LoginUser loginUser = GlobalUserHolder.getUser();
        String schoolId = loginUser.getSchoolId();
        SysUser user = sysUserService.getOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getStudentNum, studentNum)
                .eq(SysUser::getSchoolId, schoolId)
                .eq(SysUser::getUserType, SystemUserTypeEnum.STUDENT.getValue())
        );
        SysUserVO sysUser = CglibUtil.convertObj(user, SysUserVO::new);

        if (sysUser == null) {
            throw new BizException("未匹配上学生信息");
        } else {
            List<BizCompositionLog> list = bizCompositionLogService.list(new LambdaQueryWrapper<BizCompositionLog>().eq(BizCompositionLog::getCompositionId, compositionId).eq(BizCompositionLog::getAnswerId, sysUser.getId()).ge(BizCompositionLog::getAnswerStatus, 5));
            if (CollectionUtil.isNotEmpty(list)) {
                throw new BizException("学生评测信息已存在");
            }
            List<BizCompositionReleaseExtend> releaseExtendList = bizCompositionReleaseExtendService.list(new LambdaQueryWrapper<BizCompositionReleaseExtend>().eq(BizCompositionReleaseExtend::getCompositionId, compositionId).eq(BizCompositionReleaseExtend::getClazzId, sysUser.getClazzId()));

            if (CollectionUtil.isEmpty(releaseExtendList)) {
                throw new BizException("学生不在发布范围内");
            }
        }

        return sysUser;
    }

    @Override
    public List<BizCompositionReleaseVO> queryCompositionList(CompositionStudentQueryDTO compositionStudentQueryDTO) {
        return bizCompositionReleaseMapper.queryCompositionList(compositionStudentQueryDTO);
    }

    @Override
    public BizCompositionReleaseVO getDetailWithExtendById(Long id) {
        return baseMapper.getDetailWithExtendById(id);
    }
}

