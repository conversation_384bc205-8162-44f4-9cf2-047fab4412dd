/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.izpan.infrastructure.domain.BusinessEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 作文发布表 Entity 实体类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.entity.BizCompositionRelease
 * @CreateTime 2025-03-26 - 21:13:58
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("biz_composition_release")
public class BizCompositionRelease extends BusinessEntity {

    /**
     * 作文标题
     */
    private String compositionTitle;

    /**
     * 作业内容
     */
    private String compositionContent;

    /**
     * 发布类别：0 平台 1 本校 2 年级 3 班级
     */
    private Integer releaseType;

    /**
     * 作业类型:1作业
     */
    private Integer compositionType;

    /**
     * 发布状态:0 草稿 1 发文
     */
    private Integer compositionStatus;

    /**
     * 文体类型:1 叙事文 2 议论文
     */
    private String compositionWenTi;

    /**
     * 字数要求:1 100 2 200
     */
    private Integer compositionWordCount;

    /**
     * 参考内容路径
     */
    private String compositionExamplePath;

    /**
     * 作业资源路径
     */
    private String compositionResourcePath;

    /**
     * 作文开始时间
     */
    private LocalDateTime compositionBeginTime;

    /**
     * 作文截至时间
     */
    private LocalDateTime compositionEndTime;

    /**
     * 满分值
     */
    private Integer compositionFullScore;

    /**
     * 单元Id
     */
    private Long did;

    /**
     * 单元名称
     */
    private String ddanyuan;

    /**
     * 设定为模版 是否为模版：1是0否
     * {@link com.izpan.infrastructure.enums.IsEnum}
     */
    private String isTemplate;

    /**
     * 映射id
     */
    private Long mappingId;

}