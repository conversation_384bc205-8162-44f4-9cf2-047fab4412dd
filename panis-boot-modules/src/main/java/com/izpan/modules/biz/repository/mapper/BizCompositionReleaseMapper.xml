<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
All Rights Reserved: Copyright [2024] [Zhuang Pan]
Open Source Agreement: Apache License, Version 2.0
For educational purposes only, commercial use shall comply with the author's copyright information.
The author does not guarantee or assume any responsibility for the risks of using software.

Licensed under the Apache License, Version 2.0 (the "License").
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<mapper namespace="com.izpan.modules.biz.repository.mapper.BizCompositionReleaseMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BizCompositionReleaseResultMap" type="com.izpan.modules.biz.domain.entity.BizCompositionRelease">
        <result column="composition_title" property="compositionTitle"/>
        <result column="composition_content" property="compositionContent"/>
        <result column="release_type" property="releaseType"/>
        <result column="composition_type" property="compositionType"/>
        <result column="composition_status" property="compositionStatus"/>
        <result column="composition_wen_ti" property="compositionWenTi"/>
        <result column="composition_word_count" property="compositionWordCount"/>
        <result column="composition_example_path" property="compositionExamplePath"/>
        <result column="composition_resource_path" property="compositionResourcePath"/>
        <result column="composition_begin_time" property="compositionBeginTime"/>
        <result column="composition_end_time" property="compositionEndTime"/>
        <result column="composition_full_score" property="compositionFullScore"/>
        <result column="is_template" property="isTemplate"/>
        <result column="did" property="did"/>
        <result column="ddanyuan" property="ddanyuan"/>
        <result column="school_name" property="schoolName"/>
        <result column="school_id" property="schoolId"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="clazz_name" property="clazzName"/>
        <result column="clazz_id" property="clazzId"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- VO对象查询映射结果 -->
    <resultMap id="BizCompositionReleaseVOResultMap" type="com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO">
        <result column="school_name" property="schoolName"/>
        <result column="school_id" property="schoolId"/>
        <result column="composition_content" property="compositionContent"/>
        <result column="release_type" property="releaseType"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="clazz_name" property="clazzName"/>
        <result column="clazz_id" property="clazzId"/>
        <result column="composition_type" property="compositionType"/>
        <result column="composition_wen_ti" property="compositionWenTi"/>
        <result column="composition_word_count" property="compositionWordCount"/>
        <result column="composition_title" property="compositionTitle"/>
        <result column="composition_example_path" property="compositionExamplePath"/>
        <result column="composition_resource_path" property="compositionResourcePath"/>
        <result column="composition_begin_time" property="compositionBeginTime"/>
        <result column="composition_end_time" property="compositionEndTime"/>
        <result column="composition_status" property="compositionStatus"/>
        <result column="composition_full_score" property="compositionFullScore"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="did" property="did"/>
        <result column="ddanyuan" property="ddanyuan"/>
        <result column="is_template" property="isTemplate"/>
        
        <!-- 关联作文扩展表的字段映射，使用collection -->
        <collection property="extendList" ofType="com.izpan.modules.biz.domain.entity.BizCompositionReleaseExtend">
            <result column="extend_id" property="id"/>
            <result column="composition_id" property="compositionId"/>
            <result column="extend_school_id" property="schoolId"/>
            <result column="extend_school_name" property="schoolName"/>
            <result column="extend_grade_id" property="gradeId"/>
            <result column="extend_grade_name" property="gradeName"/>
            <result column="extend_clazz_id" property="clazzId"/>
            <result column="extend_clazz_name" property="clazzName"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BizCompositionReleaseColumnList">
        id,
		create_user,
		create_user_id,
		create_time,
		update_user,
		update_user_id,
		update_time,
		grade_name,
		clazz_name,
	    school_name,
        school_id,
          release_type,
          grade_id,
          clazz_id,
          composition_type,
          composition_wen_ti,
          composition_word_count,
          composition_title,
          composition_content,
          composition_example_path,
          composition_resource_path,
          composition_begin_time,
          composition_end_time,
          composition_status,
          composition_full_score,
          did,
            ddanyuan,
          is_template,
    </sql>

    <select id="queryCompositionList" resultType="com.izpan.modules.biz.domain.vo.BizCompositionReleaseVO">
        SELECT
        r.*,
        MAX(CASE
        WHEN a.id IS NULL THEN 0 -- 未完成
        WHEN a.answer_status IN (0, 5) THEN 1 -- 已修改
        WHEN a.answer_status = 10 THEN 2 -- 已提交
        ELSE a.answer_status
        END) as answer_status
        FROM biz_composition_release r
        LEFT JOIN biz_composition_log a ON r.id = a.composition_id
        AND a.is_deleted = 0
        AND a.answer_id = #{compositionStudentQueryDTO.answerId}
        WHERE r.release_type = #{compositionStudentQueryDTO.releaseType}
        AND r.composition_status = 1
        AND r.composition_begin_time &lt;= NOW()
        AND r.composition_end_time &gt;= NOW()
        AND r.is_deleted = 0
        AND r.composition_type = 1
        <if test="compositionStudentQueryDTO.compositionTitle != null and compositionStudentQueryDTO.compositionTitle != ''">
            AND r.composition_title LIKE CONCAT('%', #{compositionStudentQueryDTO.compositionTitle}, '%')
        </if>
        <choose>
            <when test="compositionStudentQueryDTO.releaseType == 1">
                AND r.school_id = #{compositionStudentQueryDTO.schoolId}
            </when>
            <when test="compositionStudentQueryDTO.releaseType == 2">
                AND r.grade_id = #{compositionStudentQueryDTO.gradeId}
            </when>
            <when test="compositionStudentQueryDTO.releaseType == 3">
                AND r.clazz_id LIKE CONCAT('%', #{compositionStudentQueryDTO.clazzId}, '%')
            </when>
        </choose>
        GROUP BY r.id
    </select>

    <!-- 根据ID查询作文发布详情并关联作文扩展表 -->
    <select id="getDetailWithExtendById" resultMap="BizCompositionReleaseVOResultMap">
        SELECT
            bcr.*,
            bcre.id as extend_id,
            bcre.composition_id,
            bcre.school_id as extend_school_id,
            bcre.school_name as extend_school_name,
            bcre.grade_id as extend_grade_id,
            bcre.grade_name as extend_grade_name,
            bcre.clazz_id as extend_clazz_id,
            bcre.clazz_name as extend_clazz_name
        FROM
            biz_composition_release bcr
        LEFT JOIN
            biz_composition_release_extend bcre ON bcr.id = bcre.composition_id
        WHERE
            bcr.id = #{id}
            AND bcr.is_deleted = 0
    </select>
    
    <!-- 分页查询作文发布列表并关联作文扩展表 -->
    <select id="listWithExtend" resultMap="BizCompositionReleaseVOResultMap">
        SELECT
            bcr.id,
            bcr.school_name, 
            bcr.school_id,
            bcr.composition_content,
            bcr.release_type,
            bcr.grade_id,
            bcr.grade_name,
            bcr.clazz_name,
            bcr.clazz_id,
            bcr.composition_type,
            bcr.composition_wen_ti,
            bcr.composition_word_count,
            bcr.composition_title,
            bcr.composition_example_path,
            bcr.composition_resource_path,
            bcr.composition_begin_time,
            bcr.composition_end_time,
            bcr.composition_status,
            bcr.composition_full_score,
            bcr.create_user,
            bcr.create_user_id,
            bcr.create_time,
            bcr.update_user,
            bcr.update_user_id,
            bcr.update_time,
            bcr.did,
            bcr.ddanyuan,
            bcr.is_template,
            bcre.id as extend_id,
            bcre.composition_id,
            bcre.school_id as extend_school_id,
            bcre.school_name as extend_school_name,
            bcre.grade_id as extend_grade_id,
            bcre.grade_name as extend_grade_name,
            bcre.clazz_id as extend_clazz_id,
            bcre.clazz_name as extend_clazz_name
        FROM
            biz_composition_release bcr
        LEFT JOIN
            biz_composition_release_extend bcre ON bcr.id = bcre.composition_id
        <if test="_parameter.containsKey('ew') and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
    </select>
</mapper>
