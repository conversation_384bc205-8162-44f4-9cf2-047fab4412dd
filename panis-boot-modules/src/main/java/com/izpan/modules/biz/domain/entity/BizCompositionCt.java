/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.izpan.infrastructure.domain.BizEntity;
import com.izpan.infrastructure.handler.JsonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 作文CT表 Entity 实体类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.biz.domain.entity.BizCompositionCt
 * @CreateTime 2025-04-09 - 12:48:39
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "biz_composition_ct",autoResultMap = true)
public class BizCompositionCt extends BizEntity {

    /**
     * CT标题
     */
    @JsonProperty("Ctitle")
    private String Ctitle;

    /**
     * 作文文体
     */
    @JsonProperty("Cstyle")
    private String Cstyle;

    /**
     * 分数
     */
    @JsonProperty("Cpercentage")
    private Double Cpercentage;

    /**
     * CT结果 - 存储为JSON字符串
     */
    @JsonProperty("Cdata")
    @TableField(value = "Cdata", typeHandler = JsonTypeHandler.class)
    private List<CdList> Cdata;

    @Data
    public static class CdList implements Serializable {

        @Serial
        private static final long serialVersionUID = -1182725599996696966L;

        /**
         * CT标题
         */
        @JsonProperty("Ctitle")
        private String Ctitle;

        /**
         * 分数
         */
        @JsonProperty("Cpercentage")
        private Double Cpercentage;

    }

}