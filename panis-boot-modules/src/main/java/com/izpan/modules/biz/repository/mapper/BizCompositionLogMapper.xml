<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 All Rights Reserved: Copyright [2024] [Zhuang Pan]
 Open Source Agreement: Apache License, Version 2.0
 For educational purposes only, commercial use shall comply with the author's copyright information.
 The author does not guarantee or assume any responsibility for the risks of using software.

 Licensed under the Apache License, Version 2.0 (the "License").
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<mapper namespace="com.izpan.modules.biz.repository.mapper.BizCompositionLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BizCompositionLogResultMap" type="com.izpan.modules.biz.domain.entity.BizCompositionLog">
        <result column="composition_id" property="compositionId"/>
        <result column="init_log_id" property="initLogId"/>
        <result column="title" property="title"/>
        <result column="require_title" property="requireTitle"/>
        <result column="wen_ti" property="wenTi"/>
        <result column="require_wen_ti" property="requireWenTi"/>
        <result column="words" property="words"/>
        <result column="require_words" property="requireWords"/>
        <result column="author" property="author"/>
        <result column="content" property="content"/>
        <result column="grade" property="grade"/>
        <result column="score" property="score"/>
        <result column="percentage" property="percentage"/>
        <result column="max_score" property="maxScore"/>
        <result column="full_score" property="fullScore"/>
        <result column="evaluation_count" property="evaluationCount"/>
        <result column="classified" property="classified"/>
        <result column="paragraphs" property="paragraphs"/>
        <result column="sentences" property="sentences"/>
        <result column="composition_path" property="compositionPath"/>
        <result column="evaluate_content" property="evaluateContent"/>
        <result column="source" property="source"/>
        <result column="advice" property="advice"/>
        <result column="comments" property="comments"/>
        <result column="`sensitive`" property="sensitive"/>
        <result column="answer_status" property="answerStatus"/>
        <result column="evaluate_sources" property="evaluateSources"/>
        <result column="teacher_evaluate" property="teacherEvaluate"/>
        <result column="answer_id" property="answerId"/>
        <result column="answer_student_num" property="answerStudentNum"/>
        <result column="answer_name" property="answerName"/>
        <result column="submit_time" property="submitTime"/>
        <result column="review_time" property="reviewTime"/>
        <result column="school_id" property="schoolId"/>
        <result column="school_name" property="schoolName"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="clazz_id" property="clazzId"/>
        <result column="clazz_name" property="clazzName"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="star" property="star"/>
    </resultMap>

    <!-- 为VO对象定义的结果映射 -->
    <resultMap id="BizCompositionLogVOResultMap" type="com.izpan.modules.biz.domain.vo.BizCompositionLogVO">
        <result column="composition_id" property="compositionId"/>
        <result column="init_log_id" property="initLogId"/>
        <result column="title" property="title"/>
        <result column="require_title" property="requireTitle"/>
        <result column="wen_ti" property="wenTi"/>
        <result column="require_wen_ti" property="requireWenTi"/>
        <result column="words" property="words"/>
        <result column="require_words" property="requireWords"/>
        <result column="author" property="author"/>
        <result column="content" property="content"/>
        <result column="grade" property="grade"/>
        <result column="score" property="score"/>
        <result column="percentage" property="percentage"/>
        <result column="max_score" property="maxScore"/>
        <result column="full_score" property="fullScore"/>
        <result column="evaluation_count" property="evaluationCount"/>
        <result column="classified" property="classified"/>
        <result column="paragraphs" property="paragraphs"/>
        <result column="sentences" property="sentences"/>
        <result column="composition_path" property="compositionPath"/>
        <result column="evaluate_content" property="evaluateContent"/>
        <result column="source" property="source"/>
        <result column="advice" property="advice"/>
        <result column="comments" property="comments"/>
        <result column="`sensitive`" property="sensitive"/>
        <result column="answer_status" property="answerStatus"/>
        <result column="evaluate_sources" property="evaluateSources"/>
        <result column="teacher_evaluate" property="teacherEvaluate"/>
        <result column="answer_id" property="answerId"/>
        <result column="answer_student_num" property="answerStudentNum"/>
        <result column="answer_name" property="answerName"/>
        <result column="submit_time" property="submitTime"/>
        <result column="review_time" property="reviewTime"/>
        <result column="school_id" property="schoolId"/>
        <result column="school_name" property="schoolName"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="clazz_id" property="clazzId"/>
        <result column="clazz_name" property="clazzName"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="score_ranking" property="scoreRanking"/>
        <result column="star" property="star"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BizCompositionLogColumnList">
        id
        ,
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        review_time,
        submit_time,
        evaluation_count,
        is_deleted,
        composition_id, 
        title, 
        require_title,
        wen_ti,
        require_wen_ti,
        words,
        require_words,
        author,
        content, 
        grade, 
        score,
        percentage,
        max_score, 
        full_score, 
        classified, 
        paragraphs, 
        sentences, 
        composition_path, 
        evaluate_content, 
        source,
        star,
        advice,
        comments,
        `sensitive`,
        answer_status, 
        evaluate_sources, 
        teacher_evaluate,
        answer_id,
        answer_student_num,
        answer_name,
        school_id, 
        school_name, 
        grade_id, 
        grade_name, 
        clazz_id, 
        clazz_name,
          init_log_id
    </sql>

    <!-- 连表分页查询作文记录和作文主表（用于普通分页查询） -->
    <select id="selectBizCompositionLogPage" resultMap="BizCompositionLogVOResultMap">
        SELECT bcl.*
        FROM biz_composition_log bcl
            ${ew.customSqlSegment}
    </select>

    <!-- 根据ID查询作文记录详情并关联作文主表 -->
    <select id="getDetailById" resultMap="BizCompositionLogVOResultMap">
        SELECT bcl.*,
               bc.composition_title,
               bc.composition_content,
               bc.composition_word_count,
               bc.composition_wen_ti,
               bc.composition_example_path,
               bc.composition_resource_path,
               bc.composition_begin_time,
               bc.composition_end_time,
               bc.composition_type,
               (SELECT COUNT(*) + 1
                FROM biz_composition_log r
                WHERE r.grade = bcl.grade
                  AND r.is_deleted = 0
                  AND r.score > bcl.score) AS score_ranking
        FROM biz_composition_log bcl
                 LEFT JOIN
             biz_composition_release bc ON bcl.composition_id = bc.id
        WHERE bcl.id = #{id}
          AND bcl.is_deleted = 0
    </select>

    <select id="getScoreRanking" resultType="java.lang.Integer">
        SELECT ranked.`rank`
        FROM (SELECT id,
                     grade,
                     score,
                     RANK() OVER (ORDER BY score DESC) AS `rank`
              FROM biz_composition_log) AS ranked
        WHERE id = #{id}
          AND grade = #{grade}
    </select>

    <!-- 执行自定义SQL查询 -->
    <select id="selectByCustomSql" resultType="com.izpan.modules.biz.domain.entity.BizCompositionLog">
        ${sql}
    </select>

    <!-- 按照answer_id分组获取每组中状态最高的记录 -->
    <select id="listWithMaxStatus" resultType="com.izpan.modules.biz.domain.entity.BizCompositionLog">
        SELECT * FROM (
        SELECT t.*,
        ROW_NUMBER() OVER(PARTITION BY t.answer_id ORDER BY t.answer_status DESC, t.create_time DESC) as rn
        FROM biz_composition_log t
        WHERE 1=1 and is_deleted = 0
        <if test="compositionId != null">
            AND t.composition_id = #{compositionId}
        </if>
        <if test="clazzId != null and clazzId != ''">
            AND t.clazz_id = #{clazzId}
        </if>
        <if test="minStatus != null">
            AND t.answer_status >= #{minStatus}
        </if>
        ) ranked
        WHERE ranked.rn = 1 and ranked.is_deleted = 0
        ORDER BY ranked.score DESC
    </select>

    <!-- 统计班级学生数量和提交作文人数 -->
    <select id="countStudentsAndSubmissions" resultType="java.util.Map">
        SELECT
        (SELECT COUNT(DISTINCT id) FROM sys_user
        WHERE user_type = '4'
        AND clazz_id = #{clazzId}
        AND is_deleted = 0) AS total_students,

        (SELECT COUNT(DISTINCT answer_id) FROM biz_composition_log
        WHERE clazz_id = #{clazzId}
        <if test="compositionId != null">
            AND composition_id = #{compositionId}
        </if>
        AND answer_status >= 5
        AND is_deleted = 0) AS submitted_students
    </select>

    <!-- 获取最新得分 -->
    <select id="getLatestScoreByInitIds" resultType="com.izpan.modules.base.domain.dto.ScoreDTO">
        SELECT init_log_id AS id, score
        FROM (
        SELECT init_log_id, score,
        ROW_NUMBER() OVER (PARTITION BY init_log_id ORDER BY submit_time DESC) AS rn
        FROM biz_composition_log
        WHERE init_log_id IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) t
        WHERE t.rn = 1;
    </select>

    <!-- 获取最新评价次数 -->
    <select id="getLatestEvaluationCountByInitIds" resultType="com.izpan.modules.base.domain.dto.EvaluationCountDTO">
        SELECT init_log_id AS id, evaluation_count
        FROM (
        SELECT init_log_id, evaluation_count,
        ROW_NUMBER() OVER (PARTITION BY init_log_id ORDER BY submit_time DESC) AS rn
        FROM biz_composition_log
        WHERE init_log_id IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) t
        WHERE t.rn = 1;
    </select>

    <!-- 获取最新提交时间 -->
    <select id="getLatestEvaluationTimeByInitIds" resultType="com.izpan.modules.base.domain.dto.EvaluationTimeDTO">
        SELECT init_log_id AS id, submit_time
        FROM (
        SELECT init_log_id, submit_time,
        ROW_NUMBER() OVER (PARTITION BY init_log_id ORDER BY submit_time DESC) AS rn
        FROM biz_composition_log
        WHERE init_log_id IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) t
        WHERE t.rn = 1;
    </select>

</mapper>
