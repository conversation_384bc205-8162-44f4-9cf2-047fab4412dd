package com.izpan.modules.biz.domain.vo;


import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentRowHeight;
import cn.idev.excel.annotation.write.style.HeadRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作文记录表导出VO
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName BizCompositionLogExportVO
 */
@Data
@ColumnWidth(20)  // 全局列宽
@HeadRowHeight(value = 20) // 头部行高
@ContentRowHeight(value = 18) // 内容行高
@ExcelIgnoreUnannotated // 忽略未注解的类
@Schema(description = "作文记录导出 VO 展示类")
public class BizCompositionLogExportVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L; // 自定义一个 serialVersionUID

    /**
     * 序号
     */
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    @Schema(description = "序号")
    private Integer sequence;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题", index = 1)
    @Schema(description = "标题")
    private String title;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 2)
    @ColumnWidth(15)
    @Schema(description = "姓名")
    private String answerName;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间", index = 3)
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    /**
     * 评测年级
     */
    @ExcelProperty(value = "评测年级", index = 4)
    @Schema(description = "评测年级")
    private String grade;

    /**
     * 学识能力分
     */
    @ExcelProperty(value = "学识能力分", index = 5)
    @Schema(description = "学识能力分")
    private Double initScore;

    /**
     * 修改次数
     */
    @ExcelProperty(value = "修改次数", index = 6)
    @Schema(description = "修改次数")
    private Integer evaluationCount;

    /**
     * 修改后的得分
     */
    @ExcelProperty(value = "改后分数", index = 7)
    @Schema(description = "改后分数")
    private Double score;


    /**
     * 分数提高
     */
    @ExcelProperty(value = "分数提高", index = 8)
    @Schema(description = "分数提高")
    private Double improveScore;

}