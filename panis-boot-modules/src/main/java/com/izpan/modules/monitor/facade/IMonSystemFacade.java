package com.izpan.modules.monitor.facade;

import com.izpan.modules.monitor.domain.vo.MonSystemVO;

/**
 * 系统服务监控 门面接口层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.monitor.facade.IMonSystemFacade
 * @CreateTime 2024/5/1 - 23:31
 */
public interface IMonSystemFacade {

    /**
     * 获取服务器信息
     *
     * @return {@linkplain MonSystemVO} 服务器信息
     * <AUTHOR>
     * @CreateTime 2024-05-01 23:39
     */
    MonSystemVO getServerInfo();
}
