<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.izpan.modules.monitor.repository.mapper.MonLogsLoginMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="MonLogsLoginResultMap" type="com.izpan.modules.monitor.domain.entity.MonLogsLogin">
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_real_name" property="userRealName"/>
        <result column="ip" property="ip"/>
        <result column="ip_addr" property="ipAddr"/>
        <result column="user_agent" property="userAgent"/>
        <result column="status" property="status"/>
        <result column="message" property="message"/>
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="MonLogsLoginColumnList">
        id,
        create_user,
        create_user_id,
        create_time,
        update_user,
        update_user_id,
        update_time,
        is_deleted,
        user_id, user_name, user_real_name, ip, ip_addr, user_agent, status, message
    </sql>

</mapper>
