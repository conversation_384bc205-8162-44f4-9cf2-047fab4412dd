package com.izpan.modules.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.izpan.modules.monitor.domain.entity.MonLogsScheduler;

/**
 * 调度日志 Mapper 接口层
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.monitor.domain.entity.MonLogsScheduler
 * @CreateTime 2024-05-30
 */

public interface MonLogsSchedulerMapper extends BaseMapper<MonLogsScheduler> {

}
