package com.izpan.common.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 登录用户
 *
 * <AUTHOR> <<EMAIL>>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.domain.LoginUser
 * @CreateTime 2023/7/18 - 12:47
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 8648911578225057361L;

    /**
     * 用户id
     */
    private Long id;

    /**
     * 账号
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 真名
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String phone;

    /**
     * 角色IDs
     */
    private List<Long> roleIds;

    /**
     * 角色Codes
     */
    private List<String> roleCodes;

    /**
     * 学校编码
     */
    private String schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 年级ID
     */
    private String gradeId;

    /**
     * 年级名称
     */
    private String gradeName;

    /**
     * 班级ID
     */
    private String clazzId;

    /**
     * 班级名称
     */
    private String clazzName;

    /**
     * 用户类别
     * {@link com.izpan.infrastructure.enums.SystemUserTypeEnum}
     */
    private String userType;

    /**
     * 当前使用角色ID
     */
    private Long currentRoleId;

    /**
     * 当前使用角色名称
     */
    private String currentRoleName;

    /**
     * 当前使用角色植物
     */
    private Long positionIdentify;

    /**
     * 学号
     */
    private String studentNum;

    /**
     * 测评次数
     */
    private Integer evaluationNumber;

    /**
     * 微课次数
     */
    private Integer microLessonsNumber;


}
