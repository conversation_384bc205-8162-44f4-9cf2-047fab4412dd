---
description:
globs:
alwaysApply: false
---

# Panis-Boot 业务开发规范

## 1. 项目架构概述

### 1.1 技术栈

- **框架**: Spring Boot 3.x
- **ORM**: MyBatis-Plus + MyBatis-Plus-Join
- **权限**: Sa-Token
- **API 文档**: Swagger 3 (OpenAPI)
- **数据库**: MySQL
- **构建工具**: Maven

### 1.2 项目模块结构

```
panis-boot/
├── panis-boot-admin/          # 管理后台模块
├── panis-boot-modules/        # 业务模块
├── panis-boot-infrastructure/ # 基础设施模块
└── panis-boot-common/         # 公共模块
```

## 2. 分层架构规范

### 2.1 架构层次

```
Controller -> Facade -> Service -> Mapper -> Database
```

### 2.2 各层职责

#### Controller 层

- **职责**: 接收 HTTP 请求，参数校验，调用 Facade 层
- **位置**: `panis-boot-admin/src/main/java/com/izpan/admin/controller/`
- **命名**: `{业务模块}{实体名}Controller`
- **示例**: `BizCaseCatalogController`

#### Facade 层（门面层）

- **职责**: 业务编排，DTO 与 BO 转换，事务控制
- **位置**: `panis-boot-modules/src/main/java/com/izpan/modules/{模块}/facade/`
- **命名**: `I{实体名}Facade` (接口), `{实体名}FacadeImpl` (实现)
- **示例**: `IBizCaseCatalogFacade`, `BizCaseCatalogFacadeImpl`

#### Service 层

- **职责**: 核心业务逻辑，数据处理
- **位置**: `panis-boot-modules/src/main/java/com/izpan/modules/{模块}/service/`
- **命名**: `I{实体名}Service` (接口), `{实体名}ServiceImpl` (实现)
- **示例**: `IBizCaseCatalogService`, `BizCaseCatalogServiceImpl`

#### Mapper 层

- **职责**: 数据访问，SQL 操作
- **位置**: `panis-boot-modules/src/main/java/com/izpan/modules/{模块}/repository/mapper/`
- **命名**: `{实体名}Mapper`
- **示例**: `BizCaseCatalogMapper`

## 3. 对象模型规范

### 3.1 对象类型定义

#### Entity（实体类）

- **用途**: 数据库表映射
- **位置**: `domain/entity/`
- **命名**: `{表名对应的实体名}`
- **继承**: `BaseEntity`
- **注解**: `@TableName`, `@Data`, `@SuperBuilder`

```java
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("biz_case_catalog")
public class BizCaseCatalog extends BaseEntity {
    // 字段定义
}
```

#### DTO（数据传输对象）

- **用途**: 前端与后端数据传输
- **位置**: `domain/dto/{模块}/`
- **命名**: `{实体名}{操作}DTO`
- **类型**: `AddDTO`, `UpdateDTO`, `SearchDTO`, `DeleteDTO`, `DragDTO`

```java
@Getter
@Setter
@Schema(name = "BizCaseCatalogAddDTO", description = "用例目录新增DTO")
public class BizCaseCatalogAddDTO implements Serializable {
    // 字段定义
}
```

#### BO（业务对象）

- **用途**: 业务层数据处理
- **位置**: `domain/bo/`
- **命名**: `{实体名}BO`
- **继承**: 对应的 Entity 类

```java
@Data
public class BizCaseCatalogBO extends BizCaseCatalog {
    private List<Long> ids;
    // 其他业务字段
}
```

#### VO（视图对象）

- **用途**: 返回给前端的数据
- **位置**: `domain/vo/`
- **命名**: `{实体名}VO`, `{实体名}TreeVO`
- **继承**: `BaseVO`

```java
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BizCaseCatalogVO", description = "用例目录VO")
public class BizCaseCatalogVO extends BaseVO {
    // 字段定义
}
```

## 4. 命名规范

### 4.1 包命名

- 全小写，使用点分隔
- 业务模块: `com.izpan.modules.{模块名}`
- 示例: `com.izpan.modules.biz`

### 4.2 类命名

- 使用大驼峰命名法（PascalCase）
- 业务实体以模块前缀开头: `{模块前缀}{实体名}`
- 示例: `BizCaseCatalog`, `BizTestCase`

### 4.3 方法命名

- 使用小驼峰命名法（camelCase）
- 查询方法: `list{实体名}Page`, `get{实体名}`, `query{实体名}Tree`
- 操作方法: `save{实体名}`, `update{实体名}`, `delete{实体名}`, `drag{实体名}`

### 4.4 字段命名

- 数据库字段: 下划线命名法（snake_case）
- Java 字段: 小驼峰命名法（camelCase）
- 示例: `parent_id` -> `parentId`

## 5. 接口设计规范

### 5.1 RESTful API 规范

```java
@GetMapping("/page")           // 分页查询
@GetMapping("/{id}")           // 根据ID查询
@PostMapping("/")              // 新增
@PutMapping("/")               // 更新
@DeleteMapping("/")            // 删除
@GetMapping("/tree")           // 树形结构查询
@PutMapping("/drag")           // 拖拽操作
```

### 5.2 接口文档注解

```java
@Operation(operationId = "1", summary = "获取用例目录列表")
@Parameter(description = "分页对象", required = true)
@Schema(description = "用例目录名称")
```

### 5.3 权限控制

```java
@SaCheckPermission("biz:case:catalog:page")
```

### 5.4 统一返回格式

```java
public Result<RPage<BizCaseCatalogVO>> page() {
    return Result.data(facade.listPage());
}

public Result<Boolean> add() {
    return Result.status(facade.add());
}
```

## 6. 数据库操作规范

### 6.1 查询构建

```java
LambdaQueryWrapper<BizCaseCatalog> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(ObjectUtils.isNotEmpty(condition), Entity::getField, condition);
queryWrapper.like(StringUtils.isNotBlank(keyword), Entity::getField, keyword);
queryWrapper.orderByAsc(Entity::getSort);
```

### 6.2 关联查询

```java
MPJLambdaWrapper<BizTestCase> queryWrapper = new MPJLambdaWrapper<BizTestCase>()
    .selectAll(BizTestCase.class)
    .selectAs(BizCaseCatalog::getFullName, BizTestCaseVO::getModuleName)
    .leftJoin(BizCaseCatalog.class, BizCaseCatalog::getId, BizTestCase::getModuleId);
```

### 6.3 事务处理

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean saveCaseCatalog(BizCaseCatalogBO bo) {
    // 业务逻辑
}
```

## 7. 业务逻辑规范

### 7.1 树形结构处理

- 使用`ancestors`字段存储祖先节点路径
- 使用`fullName`字段存储完整路径名称
- 新增时自动计算`ancestors`、`fullName`、`sort`
- 更新时级联更新子节点信息

### 7.2 排序处理

- 新增节点默认排在同级最后
- 拖拽时重新排序同级节点
- 使用批量更新提高性能

### 7.3 层级查询

```java
// 查询包含子目录的用例
queryWrapper.like(BizCaseCatalog::getAncestors, moduleId);
```

## 8. 异常处理规范

### 8.1 参数校验

```java
@Valid PageQuery pageQuery
@RequestBody @Valid BizCaseCatalogAddDTO dto
```

### 8.2 业务异常

- 使用统一的异常处理机制
- 返回明确的错误信息
- 记录必要的日志

## 9. 代码注释规范

### 9.1 类注释

```java
/**
 * 用例目录表（支持树形结构） Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.biz.BizCaseCatalogController
 * @CreateTime 2025-05-17 - 17:25:59
 */
```

### 9.2 方法注释

```java
/**
 * 拖拽用例目录（支持平级拖拽和层级拖拽）
 *
 * @param bizCaseCatalogBO 拖拽操作 BO 对象
 * @return {@link Boolean} 操作结果
 * <AUTHOR>
 * @CreateTime 2024-05-28 - 10:00:00
 */
```

## 10. 业务开发流程

### 10.1 新增业务模块流程

1. **创建数据库表**
2. **生成 Entity 实体类**
3. **创建 DTO 对象**（AddDTO、UpdateDTO、SearchDTO、DeleteDTO）
4. **创建 BO 对象**
5. **创建 VO 对象**
6. **创建 Mapper 接口**
7. **创建 Service 接口和实现**
8. **创建 Facade 接口和实现**
9. **创建 Controller**
10. **编写单元测试**

### 10.2 开发检查清单

- [ ] 实体类继承 BaseEntity
- [ ] DTO 使用@Schema 注解
- [ ] Service 方法添加事务注解
- [ ] Controller 添加权限注解
- [ ] 查询条件使用条件判断
- [ ] 批量操作使用批量方法
- [ ] 添加适当的日志记录
- [ ] 编写接口文档
- [ ] 进行单元测试

### 10.3 性能优化建议

1. **查询优化**

   - 使用索引字段进行查询
   - 避免 N+1 查询问题
   - 使用分页查询大数据量

2. **批量操作**

   - 使用`updateBatchById`进行批量更新
   - 使用`saveBatch`进行批量插入

3. **缓存策略**
   - 对频繁查询的数据进行缓存
   - 使用合适的缓存失效策略

## 11. 代码质量要求

### 11.1 代码规范

- 遵循阿里巴巴 Java 开发手册
- 使用统一的代码格式化配置
- 避免魔法数字和硬编码

### 11.2 测试要求

- 单元测试覆盖率不低于 70%
- 关键业务逻辑必须有测试用例
- 集成测试覆盖主要业务流程

### 11.3 文档要求

- API 接口文档完整
- 复杂业务逻辑有设计文档
- 数据库变更有变更记录

---

**注意**: 本规范基于当前项目架构制定，开发过程中如有疑问请及时沟通确认。
